package com.ly.titc.pms.member.biz;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ly.titc.pms.member.dal.dao.CardLevelConfigInfoDao;
import com.ly.titc.pms.member.dal.entity.po.CardLevelConfigInfo;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 卡等级配置
 *
 * <AUTHOR>
 * @date 2025/6/25 11:45
 */
@Component
public class CardLevelConfigBiz {

    @Resource
    private CardLevelConfigInfoDao cardLevelConfigInfoDao;

    public List<CardLevelConfigInfo> listByCardIds(List<Long> cardIds) {
        LambdaQueryWrapper<CardLevelConfigInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(CardLevelConfigInfo::getCardId, cardIds);
        return cardLevelConfigInfoDao.selectList(wrapper);
    }

    public CardLevelConfigInfo getById(Long id) {
        return cardLevelConfigInfoDao.selectById(id);
    }

    public void insert(CardLevelConfigInfo cardLevelConfigInfo) {
        cardLevelConfigInfoDao.insert(cardLevelConfigInfo);
    }

    public void update(CardLevelConfigInfo cardLevelConfigInfo) {
        LambdaUpdateWrapper<CardLevelConfigInfo> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(CardLevelConfigInfo::getId, cardLevelConfigInfo.getId());
        wrapper.set(CardLevelConfigInfo::getCardLevel, cardLevelConfigInfo.getCardLevel())
                .set(CardLevelConfigInfo::getCardLevelName, cardLevelConfigInfo.getCardLevelName())
                .set(CardLevelConfigInfo::getCardLevelDesc, cardLevelConfigInfo.getCardLevelDesc())
                .set(CardLevelConfigInfo::getValidPeriod, cardLevelConfigInfo.getValidPeriod())
                .set(CardLevelConfigInfo::getIsLongTerm, cardLevelConfigInfo.getIsLongTerm())
                .set(CardLevelConfigInfo::getCardPrice, cardLevelConfigInfo.getCardPrice())
                .set(CardLevelConfigInfo::getCardDiscount, cardLevelConfigInfo.getCardDiscount())
                .set(CardLevelConfigInfo::getRelegationType, cardLevelConfigInfo.getRelegationType())
                .set(CardLevelConfigInfo::getLevelImage, cardLevelConfigInfo.getLevelImage())
                .set(CardLevelConfigInfo::getState, cardLevelConfigInfo.getState())
                .set(CardLevelConfigInfo::getModifyUser, cardLevelConfigInfo.getModifyUser());
        cardLevelConfigInfoDao.update(null, wrapper);
    }
}
