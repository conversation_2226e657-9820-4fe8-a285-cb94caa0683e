package com.ly.titc.pms.member.biz;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ly.titc.common.constants.Constant;
import com.ly.titc.pms.member.dal.dao.CardLevelPrivilegeConfigInfoDao;
import com.ly.titc.pms.member.dal.entity.po.CardConfigInfo;
import com.ly.titc.pms.member.dal.entity.po.CardLevelPrivilegeConfigInfo;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 卡等级权益配置
 *
 * <AUTHOR>
 * @date 2025/6/25 11:46
 */
@Component
public class CardLevelPrivilegeConfigBiz {

    @Resource
    private CardLevelPrivilegeConfigInfoDao cardLevelPrivilegeConfigInfoDao;

    public void batchInsert(List<CardLevelPrivilegeConfigInfo> mappings) {
        cardLevelPrivilegeConfigInfoDao.insertBatch(mappings);
    }

    public void delete(Long cardId, Integer cardLevel) {
        cardLevelPrivilegeConfigInfoDao.delete(new LambdaQueryWrapper<CardLevelPrivilegeConfigInfo>()
                .eq(CardLevelPrivilegeConfigInfo::getCardId, cardId)
                .eq(CardLevelPrivilegeConfigInfo::getCardLevel, cardLevel));
    }

    public void deleteByCardId(Long cardId, String operator) {
        LambdaUpdateWrapper<CardLevelPrivilegeConfigInfo> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(CardLevelPrivilegeConfigInfo::getId, cardId);
        wrapper.set(CardLevelPrivilegeConfigInfo::getIsDelete, Constant.ONE)
                .set(CardLevelPrivilegeConfigInfo::getModifyUser, operator);
        cardLevelPrivilegeConfigInfoDao.update(null, wrapper);
    }
}
