package com.ly.titc.pms.member.biz;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ly.titc.common.constants.Constant;
import com.ly.titc.pms.member.dal.dao.CardLevelRelegationRuleDetailDao;
import com.ly.titc.pms.member.dal.dao.CardLevelRelegationRuleInfoDao;
import com.ly.titc.pms.member.dal.entity.po.CardLevelRelegationRuleDetailInfo;
import com.ly.titc.pms.member.dal.entity.po.CardLevelRelegationRuleInfo;
import com.ly.titc.pms.member.dal.entity.po.CardLevelUpgradeRuleDetailInfo;
import com.ly.titc.pms.member.dal.entity.po.CardLevelUpgradeRuleInfo;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * 卡保级规则配置
 *
 * <AUTHOR>
 * @date 2025/6/25 11:47
 */
@Component
public class CardLevelRelegationRuleBiz {

    @Resource
    private CardLevelRelegationRuleInfoDao cardLevelRelegationRuleInfoDao;

    @Resource
    private CardLevelRelegationRuleDetailDao cardLevelRelegationRuleDetailDao;

    public CardLevelRelegationRuleInfo getById(Long id) {
        return cardLevelRelegationRuleInfoDao.selectById(id);
    }

    @Transactional(rollbackFor = Exception.class)
    public void insert(CardLevelRelegationRuleInfo info, List<CardLevelRelegationRuleDetailInfo> relegationRuleDetails) {
        cardLevelRelegationRuleInfoDao.insert(info);
        // 全删全插
        LambdaUpdateWrapper<CardLevelRelegationRuleDetailInfo> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(CardLevelRelegationRuleDetailInfo::getRelegationRuleId, info.getId());
        cardLevelRelegationRuleDetailDao.delete(wrapper);
        cardLevelRelegationRuleDetailDao.insertBatch(relegationRuleDetails);
    }

    public List<CardLevelRelegationRuleInfo> listByCardId(Long cardId) {
        LambdaQueryWrapper<CardLevelRelegationRuleInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CardLevelRelegationRuleInfo::getCardId, cardId);
        return cardLevelRelegationRuleInfoDao.selectList(wrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    public void update(CardLevelRelegationRuleInfo info,
                       List<CardLevelRelegationRuleDetailInfo> relegationRuleDetails) {
        LambdaUpdateWrapper<CardLevelRelegationRuleInfo> wrapper = new LambdaUpdateWrapper<CardLevelRelegationRuleInfo>()
                .eq(CardLevelRelegationRuleInfo::getId, info.getId())
                .set(CardLevelRelegationRuleInfo::getName, info.getName())
                .set(CardLevelRelegationRuleInfo::getSourceLevel, info.getSourceLevel())
                .set(CardLevelRelegationRuleInfo::getTargetLevel, info.getTargetLevel())
                .set(CardLevelRelegationRuleInfo::getRelegationSuccessfulPerformType, info.getRelegationSuccessfulPerformType())
                .set(CardLevelRelegationRuleInfo::getRelegationFailureRule, info.getRelegationFailureRule())
                .set(CardLevelRelegationRuleInfo::getCycleType, info.getCycleType())
                .set(CardLevelRelegationRuleInfo::getDescription, info.getDescription())
                .set(CardLevelRelegationRuleInfo::getState, info.getState())
                .set(CardLevelRelegationRuleInfo::getSort, info.getSort())
                .set(CardLevelRelegationRuleInfo::getModifyUser, info.getModifyUser());
        cardLevelRelegationRuleInfoDao.update(null, wrapper);
        // 全删全插
        cardLevelRelegationRuleDetailDao.delete(new LambdaUpdateWrapper<CardLevelRelegationRuleDetailInfo>()
                .eq(CardLevelRelegationRuleDetailInfo::getRelegationRuleId, info.getId()));
        cardLevelRelegationRuleDetailDao.insertBatch(relegationRuleDetails);
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteByCardId(Long cardId, String operator) {
        LambdaUpdateWrapper<CardLevelRelegationRuleInfo> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(CardLevelRelegationRuleInfo::getId, cardId);
        wrapper.set(CardLevelRelegationRuleInfo::getIsDelete, Constant.ONE)
                .set(CardLevelRelegationRuleInfo::getModifyUser, operator);
        cardLevelRelegationRuleInfoDao.update(null, wrapper);

        LambdaUpdateWrapper<CardLevelRelegationRuleDetailInfo> detailWrapper = new LambdaUpdateWrapper<>();
        detailWrapper.eq(CardLevelRelegationRuleDetailInfo::getId, cardId);
        detailWrapper.set(CardLevelRelegationRuleDetailInfo::getIsDelete, Constant.ONE)
                .set(CardLevelRelegationRuleDetailInfo::getModifyUser, operator);
        cardLevelRelegationRuleDetailDao.update(null, detailWrapper);
    }
}
