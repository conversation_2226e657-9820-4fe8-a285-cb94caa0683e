package com.ly.titc.pms.member.biz;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ly.titc.pms.member.dal.dao.PrivilegeApplicableDataMappingDao;
import com.ly.titc.pms.member.dal.dao.PrivilegeConfigInfoDao;
import com.ly.titc.pms.member.dal.entity.po.PrivilegeApplicableDataMapping;
import com.ly.titc.pms.member.dal.entity.po.PrivilegeConfigInfo;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 权益设置
 *
 * <AUTHOR>
 * @date 2025/6/25 11:53
 */
@Component
public class PrivilegeConfigBiz {

    @Resource
    private PrivilegeConfigInfoDao privilegeConfigInfoDao;

    @Resource
    private PrivilegeApplicableDataMappingDao privilegeApplicableDataMappingDao;

    public List<PrivilegeConfigInfo> listByIds(List<Long> privilegeIds) {
        LambdaQueryWrapper<PrivilegeConfigInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(PrivilegeConfigInfo::getId, privilegeIds);
        return privilegeConfigInfoDao.selectList(wrapper);
    }

    public List<PrivilegeApplicableDataMapping> listMappingByIds(List<Long> privilegeIds) {
        LambdaQueryWrapper<PrivilegeApplicableDataMapping> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(PrivilegeApplicableDataMapping::getId, privilegeIds);
        return privilegeApplicableDataMappingDao.selectList(wrapper);
    }
}
