package com.ly.titc.pms.member.biz;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ly.titc.pms.member.dal.dao.MemberCheckInRecordDao;
import com.ly.titc.pms.member.dal.entity.po.MemberCheckInRecord;
import com.ly.titc.pms.member.entity.dto.PageMemberCheckInParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author：rui
 * @name：MemberCheckInRecordBiz
 * @Date：2024-12-11 22:00
 * @Filename：MemberCheckInRecordBiz
 */
@Slf4j
@Component
public class MemberCheckInRecordBiz extends AbstractBiz<MemberCheckInRecord> {

    @Resource
    protected MemberCheckInRecordDao dao;

    public void add(MemberCheckInRecord record) {
        dao.insert(record);
    }

    public Page<MemberCheckInRecord> pageCheckInRecord(PageMemberCheckInParam param) {
        return dao.selectPage(new Page<>(param.getPageIndex(), param.getPageSize()),
                Wrappers.lambdaQuery(MemberCheckInRecord.class)
                        .eq(StringUtils.isNotEmpty(param.getMemberNo()), MemberCheckInRecord::getMemberNo, param.getMemberNo())
                        .eq(StringUtils.isNotEmpty(param.getHotelCode()), MemberCheckInRecord::getHotelCode, param.getHotelCode())
                        .between(StringUtils.isNotEmpty(param.getCheckInBeginTime()) && StringUtils.isNotEmpty(param.getCheckInEndTime()), MemberCheckInRecord::getCheckInDate, param.getCheckInBeginTime(), param.getCheckInEndTime())
                        .between(StringUtils.isNotEmpty(param.getCheckOutBeginTime()) && StringUtils.isNotEmpty(param.getCheckOutEndTime()), MemberCheckInRecord::getCheckOutDate, param.getCheckOutBeginTime(), param.getCheckOutEndTime()));
    }

    public List<MemberCheckInRecord> listRecord(String memberNo, String hotelCode) {
        LambdaQueryWrapper<MemberCheckInRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MemberCheckInRecord::getMemberNo, memberNo)
                .eq(StringUtils.isNotBlank(hotelCode), MemberCheckInRecord::getHotelCode, hotelCode).orderByDesc(MemberCheckInRecord::getGmtCreate);
        return dao.selectList(wrapper);
    }

    public MemberCheckInRecord getByGuestOrderNo(String blocCode, String hotelCode, String memberNo, String guestOrderNo) {
        LambdaQueryWrapper<MemberCheckInRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MemberCheckInRecord::getMemberNo, memberNo)
                .eq(MemberCheckInRecord::getBlocCode, blocCode)
                .eq(StringUtils.isNotBlank(hotelCode), MemberCheckInRecord::getHotelCode, hotelCode)
                .eq(MemberCheckInRecord::getGuestOrderNo, guestOrderNo)
                .orderByDesc(MemberCheckInRecord::getGmtCreate);
        return dao.selectOne(wrapper);
    }

    public MemberCheckInRecord getLastByMemberNo(String memberNo, String hotelCode) {
        LambdaQueryWrapper<MemberCheckInRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MemberCheckInRecord::getMemberNo, memberNo)
                .eq(MemberCheckInRecord::getHotelCode, hotelCode)
                .orderByDesc(MemberCheckInRecord::getGmtCreate)
                .last("limit 1");
        return dao.selectOne(wrapper);
    }

    public MemberCheckInRecord getLastByMemberNo(String memberNo) {
        LambdaQueryWrapper<MemberCheckInRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MemberCheckInRecord::getMemberNo, memberNo)
                .orderByDesc(MemberCheckInRecord::getGmtCreate)
                .last("limit 1");
        return dao.selectOne(wrapper);
    }

    public List<String> getCheckInHotelByMemberNo(String memberNo) {
        LambdaQueryWrapper<MemberCheckInRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MemberCheckInRecord::getMemberNo, memberNo).groupBy(MemberCheckInRecord::getHotelCode);
        List<MemberCheckInRecord> memberCheckInRecords = dao.selectList(wrapper);
        return memberCheckInRecords.stream().map(MemberCheckInRecord::getHotelCode).collect(Collectors.toList());
    }
}
