package com.ly.titc.pms.member.entity.dto;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2025/5/30 14:14
 */
@Data
@Accessors(chain = true)
public class PageMemberCheckInParam {

    /**
     * 集团code
     */
    private String blocCode;

    /**
     * 会员编号
     */
    private String memberNo;

    /**
     * 酒店编码
     */
    private String hotelCode;

    /**
     * 入住时间--开始
     */
    private String checkInBeginTime;

    /**
     * 入住时间-- 结束
     */
    private String checkInEndTime;

    /**
     * 离店时间-开始
     */
    private String checkOutBeginTime;

    /**
     * 离店时间--结束
     */
    private String checkOutEndTime;

    private Integer pageIndex = 1;

    private Integer pageSize = 20;
}
