package com.ly.titc.pms.member.biz;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ly.titc.common.constants.Constant;
import com.ly.titc.pms.member.dal.dao.CardLevelUpgradeRuleInfoDao;
import com.ly.titc.pms.member.dal.dao.CardLevelUpgradeRuleDetailDao;
import com.ly.titc.pms.member.dal.entity.po.CardLevelRelegationRuleInfo;
import com.ly.titc.pms.member.dal.entity.po.CardLevelUpgradeRuleDetailInfo;
import com.ly.titc.pms.member.dal.entity.po.CardLevelUpgradeRuleInfo;
import com.ly.titc.pms.member.dal.entity.po.CardNoRuleInfo;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * 卡升级规则配置
 *
 * <AUTHOR>
 * @date 2025/6/25 11:47
 */
@Component
public class CardLevelUpgradeRuleBiz {

    @Resource
    private CardLevelUpgradeRuleInfoDao cardLevelUpgradeRuleInfoDao;

    @Resource
    private CardLevelUpgradeRuleDetailDao cardLevelUpgradeRuleDetailDao;

    public List<CardLevelUpgradeRuleInfo> listByCardId(Long cardId) {
        LambdaQueryWrapper<CardLevelUpgradeRuleInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CardLevelUpgradeRuleInfo::getCardId, cardId);
        return cardLevelUpgradeRuleInfoDao.selectList(wrapper);
    }

    public CardLevelUpgradeRuleInfo getById(Long id) {
        return cardLevelUpgradeRuleInfoDao.selectById(id);
    }

    @Transactional(rollbackFor = Exception.class)
    public void insert(CardLevelUpgradeRuleInfo info, List<CardLevelUpgradeRuleDetailInfo> upgradeRuleDetails) {
        cardLevelUpgradeRuleInfoDao.insert(info);
        // 全删全插
        LambdaUpdateWrapper<CardLevelUpgradeRuleDetailInfo> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(CardLevelUpgradeRuleDetailInfo::getUpgradeRuleId, info.getId());
        cardLevelUpgradeRuleDetailDao.delete(wrapper);
        cardLevelUpgradeRuleDetailDao.insertBatch(upgradeRuleDetails);
    }

    @Transactional(rollbackFor = Exception.class)
    public void update(CardLevelUpgradeRuleInfo info, List<CardLevelUpgradeRuleDetailInfo> upgradeRuleDetails) {
        LambdaUpdateWrapper<CardLevelUpgradeRuleInfo> wrapper = new LambdaUpdateWrapper<CardLevelUpgradeRuleInfo>()
                .eq(CardLevelUpgradeRuleInfo::getId, info.getId())
                .set(CardLevelUpgradeRuleInfo::getName, info.getName())
                .set(CardLevelUpgradeRuleInfo::getSourceLevel, info.getSourceLevel())
                .set(CardLevelUpgradeRuleInfo::getTargetLevel, info.getTargetLevel())
                .set(CardLevelUpgradeRuleInfo::getUpgradeType, info.getUpgradeType())
                .set(CardLevelUpgradeRuleInfo::getUpgradeSuccessfulPerformType, info.getUpgradeSuccessfulPerformType())
                .set(CardLevelUpgradeRuleInfo::getCycleType, info.getCycleType())
                .set(CardLevelUpgradeRuleInfo::getDescription, info.getDescription())
                .set(CardLevelUpgradeRuleInfo::getState, info.getState())
                .set(CardLevelUpgradeRuleInfo::getSort, info.getSort());
        cardLevelUpgradeRuleInfoDao.update(null, wrapper);
        // 全删全插
        cardLevelUpgradeRuleDetailDao.delete(new LambdaUpdateWrapper<CardLevelUpgradeRuleDetailInfo>()
                .eq(CardLevelUpgradeRuleDetailInfo::getUpgradeRuleId, info.getId()));
        cardLevelUpgradeRuleDetailDao.insertBatch(upgradeRuleDetails);
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteByCardId(Long cardId, String operator) {
        LambdaUpdateWrapper<CardLevelUpgradeRuleInfo> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(CardLevelUpgradeRuleInfo::getId, cardId);
        wrapper.set(CardLevelUpgradeRuleInfo::getIsDelete, Constant.ONE)
                .set(CardLevelUpgradeRuleInfo::getModifyUser, operator);
        cardLevelUpgradeRuleInfoDao.update(null, wrapper);

        LambdaUpdateWrapper<CardLevelUpgradeRuleDetailInfo> detailWrapper = new LambdaUpdateWrapper<>();
        detailWrapper.eq(CardLevelUpgradeRuleDetailInfo::getId, cardId);
        detailWrapper.set(CardLevelUpgradeRuleDetailInfo::getIsDelete, Constant.ONE)
                .set(CardLevelUpgradeRuleDetailInfo::getModifyUser, operator);
        cardLevelUpgradeRuleDetailDao.update(null, detailWrapper);
    }
}
