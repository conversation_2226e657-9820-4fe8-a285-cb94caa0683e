package com.ly.titc.pms.member.dal.entity.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 标签
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-07
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("member_tag_config_info")
public class MemberTagConfigInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 主类型 1:集团;2:门店;3:艺龙
     */
    private Integer masterType;

    /**
     * 主类型编码 ELONG 集团编码 门店编码
     */
    private String masterCode;

    /**
     * 标签名称
     */
    private String name;

    /**
     * 标签分类
     */
    private Integer type;


    /**
     * 自动删除 0 否 1 是
     */
    private Integer autoDelete;

    /**
     * 打标分类 1 自动打标 2 手动达标
     */
    private Integer markType;

    /**
     * 满足条件 ALL - 全部  ANY-满足任何一个条件
     */
    private String satisfyPerformType;

    /**
     * 标签描述
     */
    private String remark;

    /**
     * 统计周期
     */
    private Integer cycleType;

    /**
     * 状态；0 无效 1 正常 
     */
    private Integer state;

    /**
     * 排序值
     */
    private Integer sort;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 修改人
     */
    private String modifyUser;

    /**
     * 是否删除；0:未删除；1：已删除
     */
    @TableLogic
    private Integer isDelete;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;
}
