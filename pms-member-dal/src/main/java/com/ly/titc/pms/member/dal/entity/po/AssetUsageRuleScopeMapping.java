package com.ly.titc.pms.member.dal.entity.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 资产使用规则范围表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-11
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("asset_usage_rule_scope_mapping")
public class AssetUsageRuleScopeMapping implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 主体类型（创建） 1:集团 2:门店 3：艺龙
     */
    private Integer masterType;

    /**
     * 主体类型编码（创建） ELONG 集团编码 门店编码
     */
    private String masterCode;

    /**
     * 资产类型 store 储值，point 积分
     */
    private String assetType;

    /**
     * 规则ID
     */
    private Long ruleId;

    /**
     * 使用渠道 线下酒店：PMS、CRM  微订房：微订房公众号、微订房小程序
     */
    private String scopePlatformChannel;

    /**
     * 配置的适用来源 CLUB(集团组) BLOC (集团)，HOTEL(门店)
     */
    private String scopeSource;

    /**
     * 适用来源code
     */
    private String scopeSourceCode;

    /**
     * 酒馆组编码（适用来源）
     */
    private String clubCode;

    /**
     * 集团编码（适用来源）
     */
    private String blocCode;

    /**
     * 酒店编码（适用来源）
     */
    private String hotelCode;

    /**
     * 规则模式：SINGLE ：唯一  MULTI：混合
     */
    private String ruleMode;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 修改人
     */
    private String modifyUser;

    /**
     * 是否删除；0:未删除；1：已删除
     */
    @TableLogic
    private Integer isDelete;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;
}
