package com.ly.titc.pms.member.dal.entity.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 储值设置表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-09
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("store_config_info")
public class StoreConfigInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 主类型 1:集团;2:门店;3:艺龙
     */
    private Integer masterType;

    /**
     * 主类型编码 集团组code  集团code 门店code
     */
    private String masterCode;

    /**
     * 酒馆组code ELONG (冗余)
     */
    private String clubCode;

    /**
     * 集团code （冗余）
     */
    private String blocCode;

    /**
     * 酒店code （冗余）
     */
    private String hotelCode;

    /**
     * 储值支付是否需要验证 (0-否, 1-是)
     */
    private Integer isPayVerifyRequired;

    /**
     * 是否允许酒店修改验证设置 (仅集团层级有效) 0 否 1 允许
     */
    private Integer allowPayVerifyModify;

    /**
     * 是否支持他卡付款 (0-否, 1-是)
     */
    private Integer isSupportOtherCard;

    /**
     * 是否允许酒店修改他卡付款设置 (仅集团层级有效)
     * 0 否 1 允许
     */
    private Integer allowOtherCardModify;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 修改人
     */
    private String modifyUser;

    /**
     * 是否删除；0:未删除；1：已删除
     */
    @TableLogic
    private Integer isDelete;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;
}
