package com.ly.titc.pms.member.dal.entity.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 会员卡等级升级规则详细信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("card_level_upgrade_rule_detail_info")
public class CardLevelUpgradeRuleDetailInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 会员卡模版ID
     */
    private Long cardId;

    /**
     * 升级规则id
     */
    private Long upgradeRuleId;

    /**
     * 条件类型： 0-入住次数 1-入住房晚 2-充值金额 3-消费金额（不含赠送） 4-积分 5-平均房费 6-未入住天数 7-注册天数  8-成长值
     */
    private Integer conditionType;

    /**
     * 条件值
     */
    private String conditionValue;

    /**
     * 计算方式计算方式 1 大于等于 2 大于 3 小于等于 4 小于
     */
    private Integer calculateType;

    /**
     * 是否限制渠道：0-不限制，1-限制
     */
    private Integer isRestrictChannel;

    /**
     * 限制渠道；多个渠道英文分号;分隔
     */
    private String restrictChannelCodes;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 修改人
     */
    private String modifyUser;

    /**
     * 是否删除；0:未删除；1：已删除
     */
    @TableLogic
    private Integer isDelete;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;
}
