package com.ly.titc.pms.member.dal.entity.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 会员等级保级规则信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-07
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("card_level_relegation_rule_info")
public class CardLevelRelegationRuleInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 主类型 1:集团;2:门店;3:艺龙
     */
    private Integer masterType;

    /**
     * 主类型编码 ELONG 集团编码 门店编码
     */
    private String masterCode;

    /**
     * 规则名称
     */
    private String name;

    /**
     * 会员卡模版ID
     */
    private Long cardId;

    /**
     * 会员保级等级
     */
    private Integer sourceLevel;

    /**
     * 保级成功执行类型：ALL-全部条件;ANY-满足任一个条件
     */
    private String relegationSuccessfulPerformType;

    /**
     * 保级失败规则；1-降至上一等级、2-降至最低等级、3-降至满足等级、4-降至指定等级
     */
    private Integer relegationFailureRule;

    /**
     * 保级失败降至指定等级
     */
    private Integer targetLevel;

    /**
     * 统计周期
     */
    private Integer cycleType;

    /**
     * 描述
     */
    private String description;

    /**
     * 状态 0 无效 1 正常
     */
    private Integer state;

    /**
     * 排序值，越小越靠前
     */
    private Integer sort;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 修改人
     */
    private String modifyUser;

    /**
     * 是否删除；0:未删除；1：已删除
     */
    @TableLogic
    private Integer isDelete;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;
}
