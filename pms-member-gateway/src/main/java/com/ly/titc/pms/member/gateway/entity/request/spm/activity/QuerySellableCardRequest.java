package com.ly.titc.pms.member.gateway.entity.request.spm.activity;

import com.ly.titc.pms.member.gateway.entity.request.BaseRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 查询可售卡
 *
 * <AUTHOR>
 * @date 2025/1/9 15:15
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class QuerySellableCardRequest extends BaseRequest {

    /**
     * 会员号
     */
    private String memberNo;

}
