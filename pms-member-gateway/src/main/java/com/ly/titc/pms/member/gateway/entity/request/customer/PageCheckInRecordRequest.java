package com.ly.titc.pms.member.gateway.entity.request.customer;

import com.ly.titc.pms.member.gateway.entity.request.PageBaseRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

/**
 * 分页查询入住记录
 *
 * <AUTHOR>
 * @date 2024/12/13 14:50
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PageCheckInRecordRequest extends PageBaseRequest {

    /**
     * 客户编号
     */
    @NotBlank(message = "客户编号不能为空")
    private String customerNo;

    /**
     * 订单类型 Regular-普通房,Hourly-钟点房,Self_Use-自用房,Free-免费房
     */
    private String orderType;

    /**
     * 预订单号
     */
    private String reserveOrderNo;

    /**
     * 房单编号
     */
    private String roomOrderNo;

    /**
     * 客单编号
     */
    private String guestOrderNo;

    /**
     * 渠道名称
     */
    private String channelName;

    /**
     * 房间号
     */
    private String roomNo;

    /**
     * 房型名称
     */
    private String roomTypeName;

    /**
     * 入住开始时间
     */
    private String checkInStartDate;

    /**
     * 入住结束时间
     */
    private String checkInEndDate;

    /**
     * 离店开始时间
     */
    private String checkOutStartDate;

    /**
     * 离店结束时间
     */
    private String checkOutEndDate;

}
