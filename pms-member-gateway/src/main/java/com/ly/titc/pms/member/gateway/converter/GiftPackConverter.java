package com.ly.titc.pms.member.gateway.converter;

import com.ly.titc.pms.customer.dubbo.entity.response.customer.CustomerInfoResp;
import com.ly.titc.pms.member.gateway.entity.request.spm.giftpack.*;
import com.ly.titc.pms.member.gateway.entity.response.spm.giftpack.*;
import com.ly.titc.pms.member.mediator.entity.dto.member.MemberDetailDto;
import com.ly.titc.pms.spm.dubbo.entity.dto.ReceiverInfoDto;
import com.ly.titc.pms.spm.dubbo.entity.request.giftpack.*;
import com.ly.titc.pms.spm.dubbo.entity.response.giftpack.GiftPackBatchDetailResp;
import com.ly.titc.pms.spm.dubbo.entity.response.giftpack.GiftPackBatchInfoResp;
import com.ly.titc.pms.spm.dubbo.entity.response.giftpack.GiftPackInfoResp;
import com.ly.titc.pms.spm.dubbo.entity.response.giftpack.PageGiftPackInfoResp;
import com.ly.titc.pms.spm.dubbo.enums.ReceiverTypeEnum;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;
import java.util.Map;
import java.util.Objects;


@Mapper(componentModel = "spring")
public interface GiftPackConverter extends ApplicableRuleConverter {

    @Mapping(target = "sourceClient", constant = "ECRM")
    @Mapping(target = "hotelCode", ignore = true)
    SaveGiftPackInfoReq convertSaveGiftPackInfoReq(SaveGiftPackInfoRequest request);

    @Mapping(target = "sourceClient", ignore = true)
    @Mapping(target = "hotelCode", ignore = true)
    @Mapping(target = "currPage", source = "request.pageIndex")
    PageGiftPackInfoReq convertPageGiftPackInfoReq(PageGiftPackInfoRequest request);

    PageGiftPackInfoResponse convertPageGiftPackInfoResponse(PageGiftPackInfoResp resp);
    List<PageGiftPackInfoResponse> convertPageGiftPackInfoResponses(List<PageGiftPackInfoResp> dataList);

    @Mapping(target = "masterType", constant = "1")
    @Mapping(target = "masterCode", source = "blocCode")
    UpdateGiftPackStateReq convertUpdateGiftPackStateReq(UpdateGiftPackStateRequest request);

    @Mapping(target = "masterType", constant = "1")
    @Mapping(target = "masterCode", source = "blocCode")
    GetGiftPackInfoReq convertGetGiftPackInfoReq(GetGiftPackInfoRequest request);

    GiftPackInfoResponse convertGiftPackInfoResponse(GiftPackInfoResp resp);

    @Mapping(target = "masterType", constant = "1")
    @Mapping(target = "masterCode", source = "blocCode")
    DeleteGiftPackReq convertDeleteGiftPackReq(DeleteGiftPackRequest request);

    @Mapping(target = "receiverInfos",ignore = true)
    @Mapping(target = "hotelCode", ignore = true)
    @Mapping(target = "sourceClient", constant = "ECRM")
    @Mapping(target = "grantEvent", expression = "java(com.ly.titc.pms.spm.dubbo.enums.GrantEventEnum.MANUAL_GRANT.getEvent())")
    GrantGiftPackReq convertGrantGiftPackReq(GrantGiftPackRequest request);

    ReceiverInfoDto convertReceiverInfo(GrantGiftPackRequest request);

    ReceiverInfoDto convertReceiverInfo(String receiverType, String receiverCode, String customerHotelCode, Integer grantNum);

    @Mapping(target = "trackingId", ignore = true)
    @Mapping(target = "grantType", constant = "2")
    @Mapping(target = "receiverInfos",ignore = true)
    @Mapping(target = "hotelCode", ignore = true)
    @Mapping(target = "sourceClient", constant = "ECRM")
    @Mapping(target = "grantEvent", expression = "java(com.ly.titc.pms.spm.dubbo.enums.GrantEventEnum.MANUAL_GRANT.getEvent())")
    GrantGiftPackReq convertGrantGiftPackReq(String blocCode,String operator,String giftPackNo,Integer version,Integer grantNum);

    @Mapping(target = "sourceClient", ignore = true)
    @Mapping(target = "receiverType", expression = "java(com.ly.titc.pms.spm.dubbo.enums.ReceiverTypeEnum.MEMBER.getCode())")
    @Mapping(target = "receiverCode", source = "memberNo")
    @Mapping(target = "hotelCode", ignore = true)
    @Mapping(target = "currPage", source = "pageIndex")
    PageGiftPackBatchInfoReq convertPageGiftPackBatchInfoReq(PageGiftPackBatchRequest req);

    @Mapping(target = "operator", source = "createUser")
    GiftPackBatchListResponse convertGiftPackBatchListResponse(GiftPackBatchInfoResp resp);

    @Mapping(target = "currPage", source = "pageIndex")
    PageGiftPackGrantDetailReq convertPageGiftPackGrantDetailReq(PageGiftPackBatchDetailRequest request);

    GetBatchInfoReq convertGetBatchInfoReq(GetBatchInfoRequest request);


    @Mapping(target = "mobile", ignore = true)
    @Mapping(target = "memberNo", ignore = true)
    @Mapping(target = "customerNo", ignore = true)
    @Mapping(target = "customerName", ignore = true)
    GiftPackBatchDetailResponse convertGiftPackBatchDetailResponse(GiftPackBatchDetailResp giftPackBatchDetailResp);
    default GiftPackBatchDetailResponse buildGiftPackBatchDetailResponse(GiftPackBatchDetailResp giftPackBatchDetailResp,
                                                                     Map<String, MemberDetailDto> memberDetailMap,
                                                                     Map<String, CustomerInfoResp> customerInfoMap){
        GiftPackBatchDetailResponse response = this.convertGiftPackBatchDetailResponse(giftPackBatchDetailResp);
        if(Objects.equals(giftPackBatchDetailResp.getReceiverType(), ReceiverTypeEnum.MEMBER.getCode())){
            MemberDetailDto memberDetailDto = memberDetailMap.get(giftPackBatchDetailResp.getReceiverCode());
            if(Objects.nonNull(memberDetailDto)){
                response.setMobile(memberDetailDto.getMobile());
                response.setCustomerName(memberDetailDto.getRealName());
                response.setMemberNo(giftPackBatchDetailResp.getReceiverCode());
            }
        }else{
            CustomerInfoResp customerInfoResp = customerInfoMap.get(giftPackBatchDetailResp.getReceiverCode());
            if(Objects.nonNull(customerInfoResp)){
                response.setMobile(customerInfoResp.getMobile());
                response.setCustomerName(customerInfoResp.getRealName());
                response.setCustomerNo(giftPackBatchDetailResp.getReceiverCode());
                response.setMemberNo(customerInfoResp.getMemberNo());
            }
        }
        return response;
    }

    @Mapping(target = "memberName", source = "customerName")
    GiftPackBatchDetailExportResponse convertGiftPackBatchDetailExportResponse(GiftPackBatchDetailResponse response);
    List<GiftPackBatchDetailExportResponse> convertGiftPackBatchDetailExportResponses(List<GiftPackBatchDetailResponse> responses);
}
