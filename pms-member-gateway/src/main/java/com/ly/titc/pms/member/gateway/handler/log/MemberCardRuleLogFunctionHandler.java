package com.ly.titc.pms.member.gateway.handler.log;

import com.alibaba.fastjson.JSONObject;
import com.ly.titc.pms.ecrm.dubbo.entity.response.member.*;
import com.ly.titc.pms.member.com.annotation.LogRecordFunction;
import com.ly.titc.pms.member.com.enums.*;
import com.ly.titc.pms.member.com.lang.TwoTuple;
import com.ly.titc.pms.member.com.utils.ConfigCenterUtil;
import com.ly.titc.pms.member.dubbo.enums.MasterTypeEnum;
import com.ly.titc.pms.member.gateway.entity.request.member.card.*;
import com.ly.titc.pms.member.mediator.rpc.dubbo.ecrm.MemberCardInfoDecorator;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Author：rui
 * @name：MemberCardRuleLogFunctionHandler
 * @Date：2024-11-15 14:17
 * @Filename：MemberCardRuleLogFunctionHandler
 */
@LogRecordFunction("memberCardRule")
public class MemberCardRuleLogFunctionHandler extends AbstractModuleServiceHandler implements ApplicationContextAware {

    private static MemberCardInfoDecorator decorator;

    @Override
    public List<String> execute(String blocCode, String hotelCode, String name, String trackingId) {
        return null;
    }

    @Override
    public Integer getModuleId() {
        return ModuleEnum.MEMBER_RULE_MANAGE.getCode();
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        decorator = applicationContext.getBean(MemberCardInfoDecorator.class);
    }


    @LogRecordFunction("addUpgradeRule")
    public static String addUpgradeRule(SaveCardLevelUpgradeRuleRequest request) {
        String name = request.getName();
        JSONObject json = new JSONObject();
        json.put("name", name);
        json.put("blocCode", request.getBlocCode());
        List<String> records = new ArrayList<>();
        StringBuilder stringBuilder = new StringBuilder("");
        stringBuilder.append(String.format("新增【%s】", name));
        records.add(stringBuilder.toString());
        json.put("records", records);
        return json.toJSONString();
    }

    @LogRecordFunction("updateUpgradeRule")
    public static String updateUpgradeRule(SaveCardLevelUpgradeRuleRequest request) {
        String name = request.getName();
        JSONObject json = new JSONObject();
        json.put("name", name);
        json.put("blocCode", request.getBlocCode());
        List<String> records = new ArrayList<>();
        records.add(String.format("修改【%s】", name));
        MemberCardLevelUpgradeRuleResp resp = decorator.getUpgradeRule(request.getId());
        if (!StringUtils.equals(request.getName(), resp.getName())) {
            records.add(String.format("【规则名称】%s->%s", resp.getName(), request.getName()));
        }
        if (request.getCardId() != null && !request.getCardId().equals(resp.getCardId())) {
            TwoTuple<Integer, String> masterTuple = ConfigCenterUtil.getMasterByBloc(request.getBlocCode());
            List<MemberCardConfigResp> cardConfigRespList = decorator.listCardConfig(masterTuple.getFirst(), request.getBlocCode());
            Map<Long, String> map = cardConfigRespList.stream().collect(Collectors.toMap(MemberCardConfigResp::getId, MemberCardConfigResp::getCardName));
            records.add(String.format("【会员卡】%s->%s", map.get(resp.getCardId()), map.get(request.getCardId())));
        }
        if (request.getSourceLevel() != null && !request.getSourceLevel().equals(resp.getSourceLevel())) {
            records.add(String.format("【原会员等级】%s->%s", resp.getSourceLevel(), request.getSourceLevel()));
        }
        if (request.getTargetLevel() != null && !request.getTargetLevel().equals(resp.getTargetLevel())) {
            records.add(String.format("【目标会员等级】%s->%s", resp.getTargetLevel(), request.getTargetLevel()));
        }
        if (request.getSort() != null && !request.getSort().equals(resp.getSort())) {
            records.add(String.format("【排序】%s->%s", resp.getSort(), request.getSort()));
        }
        if (request.getDescription() != null && !request.getDescription().equals(resp.getDescription())) {
            records.add(String.format("【描述】%s->%s", resp.getDescription(), request.getDescription()));
        }
        if (request.getSuccessfulPerformType() != null && !request.getSuccessfulPerformType().equals(resp.getUpgradeSuccessfulPerformType())) {
            records.add(String.format("【统计逻辑】%s->%s", SuccessfulPerformTypeEnum.getDescByType(resp.getUpgradeSuccessfulPerformType()), SuccessfulPerformTypeEnum.getDescByType(request.getSuccessfulPerformType())));
        }
        if (request.getCycleType() != null && !request.getCycleType().equals(resp.getCycleType())) {
            records.add(String.format("【升级周期】%s->%s", CycleTypeEnum.getNameByType(resp.getCycleType()), CycleTypeEnum.getNameByType(request.getCycleType())));
        }
        List<Long> originalITemIds = resp.getDetails().stream().map(MemberCardLevelUpgradeRuleDetailResp::getId).collect(Collectors.toList());
        Map<Long, MemberCardLevelUpgradeRuleDetailResp> originalMap = resp.getDetails().stream().collect(Collectors.toMap(MemberCardLevelUpgradeRuleDetailResp::getId, item -> item));
        List<CardLevelUpgradeRuleDetailRequest> detailRequests = request.getDetails();
        List<CardLevelUpgradeRuleDetailRequest> addItem = detailRequests.stream().filter(item -> Objects.isNull(item.getId())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(addItem)) {
            List<String> s = new ArrayList<>();
            for (CardLevelUpgradeRuleDetailRequest cardLevelUpgradeRuleDetailRequest : addItem) {
                s.add(String.format("%s%s%s", ConditionTypeEnum.getNameByType(cardLevelUpgradeRuleDetailRequest.getConditionType()), CalculateTypeEnum.getNameByType(cardLevelUpgradeRuleDetailRequest.getCalculateType()), cardLevelUpgradeRuleDetailRequest.getConditionValue()));
            }
            records.add(String.format("【新增统计规则】%s", String.join("、", s)));
        }
        List<CardLevelUpgradeRuleDetailRequest> deleteItem = detailRequests.stream().filter(item -> Objects.nonNull(item.getId()) && !originalITemIds.contains(item.getId())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(deleteItem)) {
            List<String> s = new ArrayList<>();
            for (CardLevelUpgradeRuleDetailRequest cardLevelUpgradeRuleDetailRequest : deleteItem) {
                s.add(String.format("%s%s%s", ConditionTypeEnum.getNameByType(cardLevelUpgradeRuleDetailRequest.getConditionType()), CalculateTypeEnum.getNameByType(cardLevelUpgradeRuleDetailRequest.getCalculateType()), cardLevelUpgradeRuleDetailRequest.getConditionValue()));
            }
            records.add(String.format("【删除统计规则】%s", String.join("、", s)));
        }
        List<CardLevelUpgradeRuleDetailRequest> updateItem = detailRequests.stream().filter(item -> Objects.nonNull(item.getId()) && originalITemIds.contains(item.getId())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(updateItem)) {
            List<String> s = new ArrayList<>();
            for (CardLevelUpgradeRuleDetailRequest cardLevelUpgradeRuleDetailRequest : updateItem) {
                if (!cardLevelUpgradeRuleDetailRequest.getConditionType().equals(originalMap.get(cardLevelUpgradeRuleDetailRequest.getId()).getConditionType())
                        || !cardLevelUpgradeRuleDetailRequest.getCalculateType().equals(originalMap.get(cardLevelUpgradeRuleDetailRequest.getId()).getCalculateType())
                        || !cardLevelUpgradeRuleDetailRequest.getConditionValue().equals(originalMap.get(cardLevelUpgradeRuleDetailRequest.getId()).getConditionValue())) {
                    s.add(String.format("%s%s%s->%s%s%s", ConditionTypeEnum.getNameByType(cardLevelUpgradeRuleDetailRequest.getConditionType()), CalculateTypeEnum.getNameByType(cardLevelUpgradeRuleDetailRequest.getCalculateType()), cardLevelUpgradeRuleDetailRequest.getConditionValue(),
                            ConditionTypeEnum.getNameByType(originalMap.get(cardLevelUpgradeRuleDetailRequest.getId()).getConditionType()), CalculateTypeEnum.getNameByType(originalMap.get(cardLevelUpgradeRuleDetailRequest.getId()).getCalculateType()), originalMap.get(cardLevelUpgradeRuleDetailRequest.getId()).getConditionValue()));
                }
            }
            if(CollectionUtils.isNotEmpty(s)) {
                records.add(String.format("【修改统计规则】%s", String.join("、", s)));
            }
        }

        json.put("records", records);
        return json.toJSONString();
    }

    @LogRecordFunction("deleteUpgradeRule")
    public static String deleteUpgradeRule(DeleteRuleRequest request) {
        String name = request.getName();
        JSONObject json = new JSONObject();
        json.put("name", name);
        json.put("blocCode", request.getBlocCode());
        List<String> records = new ArrayList<>();
        records.add(String.format("删除【%s】", name));
        json.put("records", records);
        return json.toJSONString();
    }

    @LogRecordFunction("disableUpgradeRule")
    public static String disableUpgradeRule(ActionRuleBaseRequest request) {
        String name = request.getName();
        JSONObject json = new JSONObject();
        json.put("name", name);
        json.put("blocCode", request.getBlocCode());
        List<String> records = new ArrayList<>();
        records.add(String.format("停用【%s】", name));
        json.put("records", records);
        return json.toJSONString();
    }

    @LogRecordFunction("enableUpgradeRule")
    public static String enableUpgradeRule(ActionRuleBaseRequest request) {
        String name = request.getName();
        JSONObject json = new JSONObject();
        json.put("name", name);
        json.put("blocCode", request.getBlocCode());
        List<String> records = new ArrayList<>();
        records.add(String.format("启用【%s】", name));
        json.put("records", records);
        return json.toJSONString();
    }

    @LogRecordFunction("addRelegationRule")
    public static String addRelegationRule(SaveCardLevelRelegationRuleRequest request) {
        String name = request.getName();
        JSONObject json = new JSONObject();
        json.put("name", name);
        json.put("blocCode", request.getBlocCode());
        List<String> records = new ArrayList<>();
        records.add(String.format("修改【%s】", name));
        json.put("records", records);
        return json.toJSONString();
    }

    @LogRecordFunction("updateRelegationRule")
    public static String updateRelegationRule(SaveCardLevelRelegationRuleRequest request) {
        String name = request.getName();
        JSONObject json = new JSONObject();
        json.put("name", name);
        json.put("blocCode", request.getBlocCode());
        List<String> records = new ArrayList<>();
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(String.format("修改【%s】", name));
        MemberCardLevelRelegationRuleResp resp = decorator.getRelegationRule(request.getId());
        if (!StringUtils.equals(request.getName(), resp.getName())) {
            stringBuilder.append(String.format("【规则名称】%s->%s", resp.getName(), request.getName()));
        }
        if (request.getCardId() != null && !request.getCardId().equals(resp.getCardId())) {
            TwoTuple<Integer, String> masterTuple = ConfigCenterUtil.getMasterByBloc(request.getBlocCode());
            List<MemberCardConfigResp> cardConfigRespList = decorator.listCardConfig(masterTuple.getFirst(), request.getBlocCode());
            Map<Long, String> map = cardConfigRespList.stream().collect(Collectors.toMap(MemberCardConfigResp::getId, MemberCardConfigResp::getCardName));
            stringBuilder.append(String.format("【会员卡】%s->%s", map.get(resp.getCardId()), map.get(request.getCardId())));
        }
        if (request.getSourceLevel() != null && !request.getSourceLevel().equals(resp.getSourceLevel())) {
            stringBuilder.append(String.format("【原会员等级】%s->%s", resp.getSourceLevel(), request.getSourceLevel()));
        }
        if (request.getTargetLevel() != null && !request.getTargetLevel().equals(resp.getTargetLevel())) {
            stringBuilder.append(String.format("【目标会员等级】%s->%s", resp.getTargetLevel(), request.getTargetLevel()));
        }
        if (request.getSort() != null && !request.getSort().equals(resp.getSort())) {
            stringBuilder.append(String.format("【排序】%s->%s", resp.getSort(), request.getSort()));
        }
        if (request.getDescription() != null && !request.getDescription().equals(resp.getDescription())) {
            stringBuilder.append(String.format("【描述】%s->%s", resp.getDescription(), request.getDescription()));
        }
        if (request.getSuccessfulPerformType() != null && !request.getSuccessfulPerformType().equals(resp.getRelegationSuccessfulPerformType())) {
            stringBuilder.append(String.format("【统计逻辑】%s->%s", SuccessfulPerformTypeEnum.getDescByType(resp.getRelegationSuccessfulPerformType()), SuccessfulPerformTypeEnum.getDescByType(request.getSuccessfulPerformType())));
        }
        if (request.getCycleType() != null && !request.getCycleType().equals(resp.getCycleType())) {
            stringBuilder.append(String.format("【升级周期】%s->%s", CycleTypeEnum.getNameByType(resp.getCycleType()), CycleTypeEnum.getNameByType(request.getCycleType())));
        }
        List<Long> originalITemIds = resp.getDetails().stream().map(MemberCardLevelRelegationRuleDetailResp::getId).collect(Collectors.toList());
        Map<Long, MemberCardLevelRelegationRuleDetailResp> originalMap = resp.getDetails().stream().collect(Collectors.toMap(MemberCardLevelRelegationRuleDetailResp::getId, item -> item));
        List<CardLevelRelegationRuleDetailRequest> detailRequests = request.getDetails();
        List<CardLevelRelegationRuleDetailRequest> addItem = detailRequests.stream().filter(item -> Objects.isNull(item.getId())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(addItem)) {
            List<String> s = new ArrayList<>();
            for (CardLevelRelegationRuleDetailRequest cardLevelUpgradeRuleDetailRequest : addItem) {
                s.add(String.format("%s%s%s", ConditionTypeEnum.getNameByType(cardLevelUpgradeRuleDetailRequest.getConditionType()), CalculateTypeEnum.getNameByType(cardLevelUpgradeRuleDetailRequest.getCalculateType()), cardLevelUpgradeRuleDetailRequest.getConditionValue()));
            }
            stringBuilder.append(String.format("【新增统计规则】%s", String.join("、", s)));
        }
        List<CardLevelRelegationRuleDetailRequest> deleteItem = detailRequests.stream().filter(item -> Objects.nonNull(item.getId()) && !originalITemIds.contains(item.getId())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(deleteItem)) {
            List<String> s = new ArrayList<>();
            for (CardLevelRelegationRuleDetailRequest cardLevelUpgradeRuleDetailRequest : deleteItem) {
                s.add(String.format("%s%s%s", ConditionTypeEnum.getNameByType(cardLevelUpgradeRuleDetailRequest.getConditionType()), CalculateTypeEnum.getNameByType(cardLevelUpgradeRuleDetailRequest.getCalculateType()), cardLevelUpgradeRuleDetailRequest.getConditionValue()));
            }
            stringBuilder.append(String.format("【删除统计规则】%s", String.join("、", s)));
        }
        List<CardLevelRelegationRuleDetailRequest> updateItem = detailRequests.stream().filter(item -> Objects.nonNull(item.getId()) && originalITemIds.contains(item.getId())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(updateItem)) {
            List<String> s = new ArrayList<>();
            for (CardLevelRelegationRuleDetailRequest cardLevelUpgradeRuleDetailRequest : updateItem) {
                if (!cardLevelUpgradeRuleDetailRequest.getConditionType().equals(originalMap.get(cardLevelUpgradeRuleDetailRequest.getId()).getConditionType())
                        || !cardLevelUpgradeRuleDetailRequest.getCalculateType().equals(originalMap.get(cardLevelUpgradeRuleDetailRequest.getId()).getCalculateType())
                        || !cardLevelUpgradeRuleDetailRequest.getConditionValue().equals(originalMap.get(cardLevelUpgradeRuleDetailRequest.getId()).getConditionValue())) {
                    s.add(String.format("%s%s%s->%s%s%s", ConditionTypeEnum.getNameByType(cardLevelUpgradeRuleDetailRequest.getConditionType()), CalculateTypeEnum.getNameByType(cardLevelUpgradeRuleDetailRequest.getCalculateType()), cardLevelUpgradeRuleDetailRequest.getConditionValue(),
                            ConditionTypeEnum.getNameByType(originalMap.get(cardLevelUpgradeRuleDetailRequest.getId()).getConditionType()), CalculateTypeEnum.getNameByType(originalMap.get(cardLevelUpgradeRuleDetailRequest.getId()).getCalculateType()), originalMap.get(cardLevelUpgradeRuleDetailRequest.getId()).getConditionValue()));
                }
            }
            if(CollectionUtils.isNotEmpty(s)) {
                stringBuilder.append(String.format("【修改统计规则】%s", String.join("、", s)));
            }
        }

        records.add(stringBuilder.toString());
        json.put("records", records);
        return json.toJSONString();
    }

    @LogRecordFunction("deleteRelegationRule")
    public static String deleteRelegationRule(DeleteRuleRequest request) {
        String name = request.getName();
        JSONObject json = new JSONObject();
        json.put("name", name);
        json.put("blocCode", request.getBlocCode());
        List<String> records = new ArrayList<>();
        records.add(String.format("删除【%s】", name));
        json.put("records", records);
        return json.toJSONString();
    }

    @LogRecordFunction("disableRelegationRule")
    public static String disableRelegationRule(ActionRuleBaseRequest request) {
        String name = request.getName();
        JSONObject json = new JSONObject();
        json.put("name", name);
        json.put("blocCode", request.getBlocCode());
        List<String> records = new ArrayList<>();
        records.add(String.format("停用【%s】", name));
        json.put("records", records);
        return json.toJSONString();
    }

    @LogRecordFunction("enableRelegationRule")
    public static String enableRelegationRule(ActionRuleBaseRequest request) {
        String name = request.getName();
        JSONObject json = new JSONObject();
        json.put("name", name);
        json.put("blocCode", request.getBlocCode());
        List<String> records = new ArrayList<>();
        records.add(String.format("启用【%s】", name));
        json.put("records", records);
        return json.toJSONString();
    }
}
