package com.ly.titc.pms.member.gateway.entity.request.member.profile;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Author：rui
 * @name：MemberProfileTagRequest
 * @Date：2024-12-2 14:59
 * @Filename：MemberProfileTagRequest
 */
@Data
public class MemberProfileTagRequest {


    /**
     * 标签ID
     */
    @NotNull(message = "标签ID不可为空")
    private Long tagId;

    /**
     * 标签名称
     */
    @NotBlank(message = "标签名称不可为空")
    private String tagName;

    /**
     * 排序
     */
    @NotNull(message = "排序不可为空")
    private Integer sort;
}
