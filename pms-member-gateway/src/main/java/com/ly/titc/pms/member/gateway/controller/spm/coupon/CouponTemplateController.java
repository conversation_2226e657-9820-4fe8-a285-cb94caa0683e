package com.ly.titc.pms.member.gateway.controller.spm.coupon;

import com.ly.titc.cdm.dubbo.entity.response.plan.HotelRatePlanListResp;
import com.ly.titc.common.constants.Constant;
import com.ly.titc.common.entity.Pageable;
import com.ly.titc.common.entity.Response;
import com.ly.titc.mdm.entity.response.hotel.PageHotelsResp;
import com.ly.titc.mdm.entity.response.hotel.SelectHotelResp;
import com.ly.titc.mdm.entity.response.hotel.room.type.RoomTypeBaseInfoResp;
import com.ly.titc.mdm.entity.response.hotel.room.type.RoomTypeInfoResp;
import com.ly.titc.mdm.enums.StateEnum;
import com.ly.titc.pms.account.dubbo.entity.response.ExpenseInfoResp;
import com.ly.titc.pms.account.dubbo.entity.response.SettlementItemInfoResp;
import com.ly.titc.pms.account.dubbo.enums.PostingTypeEnum;
import com.ly.titc.pms.member.gateway.converter.CouponTemplateConverter;
import com.ly.titc.pms.member.gateway.entity.request.BaseRequest;
import com.ly.titc.pms.member.gateway.entity.request.spm.coupon.PageCouponApplicableHotelRequest;
import com.ly.titc.pms.member.gateway.entity.request.spm.coupon.PageCouponRatePlanRequest;
import com.ly.titc.pms.member.gateway.entity.request.spm.coupon.PageCouponRoomTypeRequest;
import com.ly.titc.pms.member.gateway.entity.request.spm.coupon.template.*;
import com.ly.titc.pms.member.gateway.entity.response.spm.coupon.CouponApplicableHotelResponse;
import com.ly.titc.pms.member.gateway.entity.response.spm.coupon.CouponApplicableRatePlanResponse;
import com.ly.titc.pms.member.gateway.entity.response.spm.coupon.CouponApplicableRoomTypeResponse;
import com.ly.titc.pms.member.gateway.entity.response.spm.coupon.template.CouponTemplateInfoResponse;
import com.ly.titc.pms.member.gateway.entity.response.spm.coupon.template.CouponTemplateListResponse;
import com.ly.titc.pms.member.gateway.entity.response.spm.coupon.template.CouponTemplateTreeResponse;
import com.ly.titc.pms.member.gateway.entity.response.spm.coupon.template.SaveCouponTemplateInfoResponse;
import com.ly.titc.pms.member.gateway.entity.response.spm.coupon.template.dto.*;
import com.ly.titc.pms.member.gateway.thread.local.UserInfoThreadHolder;
import com.ly.titc.pms.member.mediator.entity.dto.user.UserInfoDto;
import com.ly.titc.pms.member.mediator.rpc.dsf.mdm.HotelDecorator;
import com.ly.titc.pms.member.mediator.rpc.dsf.mdm.RoomTypeDecorator;
import com.ly.titc.pms.member.mediator.rpc.dubbo.account.AccountExpenseDecorator;
import com.ly.titc.pms.member.mediator.rpc.dubbo.account.AccountSettlementDecorator;
import com.ly.titc.pms.member.mediator.rpc.dubbo.cdm.RatePlanDecorator;
import com.ly.titc.pms.member.mediator.rpc.dubbo.coupon.CouponDecorator;
import com.ly.titc.pms.member.mediator.rpc.dubbo.coupon.CouponTemplateDecorator;
import com.ly.titc.pms.spm.dubbo.entity.dto.rule.use.CouponTemplateUseRuleDto;
import com.ly.titc.pms.spm.dubbo.entity.dto.rule.use.applicable.*;
import com.ly.titc.pms.spm.dubbo.entity.request.coupon.GetCouponInfoReq;
import com.ly.titc.pms.spm.dubbo.entity.request.coupon.template.*;
import com.ly.titc.pms.spm.dubbo.entity.response.coupon.ApplicableHotelResp;
import com.ly.titc.pms.spm.dubbo.entity.response.coupon.template.CouponTemplateDetailResp;
import com.ly.titc.pms.spm.dubbo.entity.response.coupon.template.CouponTemplateListResp;
import com.ly.titc.pms.spm.dubbo.entity.response.coupon.template.SaveCouponTemplateInfoResp;
import com.ly.titc.pms.spm.dubbo.enums.ApplicableScopeTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 优惠券模版接口
 *
 * @Description:
 * @Author: huaizhu.zhong
 * @Date: 2024-11-12
 */
@Slf4j
@RestController
@RequestMapping("/coupon/template")
public class CouponTemplateController {

    @Resource(type = HotelDecorator.class)
    private HotelDecorator hotelDecorator;
    @Resource(type = CouponDecorator.class)
    private CouponDecorator couponDecorator;
    @Resource(type = RoomTypeDecorator.class)
    private RoomTypeDecorator roomTypeDecorator;
    @Resource(type = RatePlanDecorator.class)
    private RatePlanDecorator ratePlanDecorator;
    @Resource(type = CouponTemplateDecorator.class)
    private CouponTemplateDecorator couponTemplateDecorator;
    @Resource(type = AccountExpenseDecorator.class)
    private AccountExpenseDecorator accountExpenseDecorator;
    @Resource(type = AccountSettlementDecorator.class)
    private AccountSettlementDecorator accountSettlementDecorator;
    @Resource(type = CouponTemplateConverter.class)
    private CouponTemplateConverter couponTemplateConverter;

    /**
     * 优惠券模版列表(分页)
     */
    @PostMapping("/page/list")
    public Response<Pageable<CouponTemplateListResponse>> pageCouponTemplateList(@Valid @RequestBody PageTemplateListRequest request) {
        GetCouponTemplateListReq req = couponTemplateConverter.convertGetCouponTemplateListReq(request);
        Pageable<CouponTemplateListResp> pageable = couponTemplateDecorator.pageCouponTemplateList(req);
        return Response.success(Pageable.convert(pageable, couponTemplateConverter.convertCouponTemplateList(pageable.getDatas())));
    }

    /**
     * 优惠券模版列表
     */
    @PostMapping("/list")
    public Response<List<CouponTemplateListResponse>> listCouponTemplate(@Valid @RequestBody PageTemplateListRequest request) {
        GetCouponTemplateListReq req = couponTemplateConverter.convertGetCouponTemplateListReq(request);
        req.setPageSize(Integer.MAX_VALUE);
        List<CouponTemplateListResp> responseList = couponTemplateDecorator.listCouponTemplate(req);
        return Response.success(couponTemplateConverter.convertCouponTemplateList(responseList));
    }

    /**
     * 优惠券模版列表数
     */
    @PostMapping("/list/tree")
    public Response<List<CouponTemplateTreeResponse>> listCouponTemplateTree(@Valid @RequestBody BaseRequest request) {
        GetCouponTemplateListReq req = couponTemplateConverter.convertGetCouponTemplateListReqByBase(request);
        req.setCurrPage(Constant.ONE);
        req.setPageSize(Integer.MAX_VALUE);
        List<CouponTemplateListResp> ans = couponTemplateDecorator.listCouponTemplate(req);
        return Response.success(couponTemplateConverter.convertCouponTemplateTree(ans));
    }

    /**
     * 查询优惠券模版明细
     */
    @PostMapping("/detail")
    public Response<CouponTemplateInfoResponse> getCouponTemplateDetail(@Valid @RequestBody GetTemplateDetailRequest request) {
        GetCouponTemplateDetailReq req = couponTemplateConverter.convertGetCouponTemplateDetailReq(request);
        CouponTemplateDetailResp resp = couponTemplateDecorator.getCouponTemplateDetail(req);
        return Response.success(this.buildCouponTemplateInfoResponse(request.getBlocCode(), resp));
    }

    /**
     * 保存优惠券模版(创建(复制)/更新)
     */
    @PostMapping("/save")
    public Response<SaveCouponTemplateInfoResponse> saveCouponTemplate(@Valid @RequestBody SaveTemplateRequest request) {
        UserInfoDto user = UserInfoThreadHolder.getUser();
        SaveCouponTemplateReq req = couponTemplateConverter.buildSaveCouponTemplateReq(request, user);
        SaveCouponTemplateInfoResp resp = couponTemplateDecorator.saveCouponTemplate(req);
        SaveCouponTemplateInfoResponse response = couponTemplateConverter.convertSaveCouponTemplateInfoResponse(resp);
        return Response.success(response);
    }

    /**
     * 模版删除
     */
    @PostMapping("/delete")
    public Response<String> deleteCouponTemplate(@Valid @RequestBody DeleteCouponTemplateRequest request) {
        DeleteCouponTemplateReq req = couponTemplateConverter.convertDeleteCouponTemplateReq(request);
        couponTemplateDecorator.deleteCouponTemplate(req);
        return Response.success();
    }

    /**
     * 模版作废
     */
    @PostMapping("/repeal")
    public Response<String> repealCouponTemplate(@Valid @RequestBody RepealTemplateRequest request) {
        UpdateCouponTemplateStateReq req = couponTemplateConverter.convertRepealCouponTemplateReq(request);
        couponTemplateDecorator.repealCouponTemplate(req);
        return Response.success();
    }

    /**
     * 获取优惠券模版适用的酒店
     */
    @PostMapping("/applicable/hotel/list")
    public Response<Set<String>> listApplicableHotels(@Valid @RequestBody GetApplicableHotelRequest request) {
        GetApplicableHotelReq req = couponTemplateConverter.convertGetApplicableHotelReq(request);
        ApplicableHotelResp resp = couponTemplateDecorator.getApplicableHotelInfo(req);
        return Response.success(resp.getHotelCodes());
    }


    /**
     * 查询优惠券适用酒店列表
     */
    @PostMapping("/page/applicable/hotel/list")
    public Response<Pageable<CouponApplicableHotelResponse>> pageCouponApplicableHotel(@Valid @RequestBody PageCouponApplicableHotelRequest request) {
        ApplicableHotelResp applicableHotel;
        if (!StringUtils.isEmpty(request.getCouponCode())) {
            GetCouponInfoReq getCouponInfoReq = couponTemplateConverter.convertGetCouponInfoReq(request);
            applicableHotel = couponDecorator.getApplicableHotel(getCouponInfoReq);
        } else {
            GetApplicableHotelReq req = couponTemplateConverter.convertGetApplicableHotelReqByPage(request);
            applicableHotel = couponTemplateDecorator.getApplicableHotelInfo(req);
        }
        Integer state = StateEnum.VALID.getValue();
        List<PageHotelsResp> pageHotels = hotelDecorator.selectHotelsByFuzzy(request.getBlocCode(), request.getLkNameOrCode(), state);
        if (!applicableHotel.getIsAllHotel()) {
            pageHotels = pageHotels.stream().filter(pageHotelsResp ->
                    applicableHotel.getHotelCodes().contains(pageHotelsResp.getHotelCode())).collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(pageHotels)) {
            return Response.success(Pageable.empty());
        }
        List<CouponApplicableHotelResponse> ans = couponTemplateConverter.convertCouponApplicableHotels(pageHotels);
        if (CollectionUtils.isEmpty(ans)) {
            return Response.success(Pageable.empty());
        }
        return Response.success(this.buildPageable(request.getPageIndex(), request.getPageSize(), ans.size(), ans));
    }

    /**
     * 查询优惠券适用房型列表
     */
    @PostMapping("/page/applicable/roomType/list")
    public Response<Pageable<CouponApplicableRoomTypeResponse>> pageCouponApplicableHotelRoomType(@Valid @RequestBody PageCouponRoomTypeRequest request) {
        String blocCode = request.getBlocCode();
        Integer state = StateEnum.VALID.getValue();
        String hotelNameOrCode = request.getHotelNameOrCode();
        String roomTypeNameOrCode = request.getRoomTypeNameOrCode();
        //查询酒店信息
        List<PageHotelsResp> pageHotels = hotelDecorator.selectHotelsByFuzzy(blocCode,hotelNameOrCode,state);
        if (CollectionUtils.isEmpty(pageHotels)) {
            return Response.success(Pageable.empty());
        }
        List<Long> hotelVids = pageHotels.stream().map(item-> Long.valueOf(item.getHotelCode())).distinct().collect(Collectors.toList());
        //查询酒店下的房型信息
        List<RoomTypeBaseInfoResp> roomTypeInfos = roomTypeDecorator.selectRoomTypesByFuzzy(blocCode,hotelVids,roomTypeNameOrCode, state);
        if (CollectionUtils.isEmpty(roomTypeInfos)) {
            return Response.success(Pageable.empty());
        }
        //查询优惠券信息
        CouponTemplateDetailResp resp = couponTemplateDecorator.getCouponTemplateDetail(
                couponTemplateConverter.convertGetCouponTemplateDetailReqByRoomType(request));
        //组装数据
        List<CouponApplicableRoomTypeResponse> ans = couponTemplateConverter.convertCouponApplicableRoomTypes(resp.getUseRuleInfo(),
                roomTypeInfos,pageHotels);
        if (CollectionUtils.isEmpty(ans)) {
            return Response.success(Pageable.empty());
        }
        return Response.success(this.buildPageable(request.getPageIndex(), request.getPageSize(), ans.size(), ans));
    }

    /**
     * 查询优惠券适用价格方案列表
     */
    @PostMapping("/page/applicable/ratePlan/list")
    public Response<Pageable<CouponApplicableRatePlanResponse>> pageCouponApplicableHotelRatePlanList(@Valid @RequestBody PageCouponRatePlanRequest request) {
        String blocCode = request.getBlocCode();
        Integer state = StateEnum.VALID.getValue();
        String hotelNameOrCode = request.getHotelNameOrCode();
        String ratePlanNameOrCode = request.getRatePlanNameOrCode();
        //查询酒店信息
        List<PageHotelsResp> pageHotels = hotelDecorator.selectHotelsByFuzzy(blocCode, hotelNameOrCode, state);
        if (CollectionUtils.isEmpty(pageHotels)) {
            return Response.success(Pageable.empty());
        }
        //查询酒店的价格方案
        List<String> hotelCodes = pageHotels.stream().map(PageHotelsResp::getHotelCode).distinct().collect(Collectors.toList());
        List<HotelRatePlanListResp> ratePlanInfos = ratePlanDecorator.listRatePlan(blocCode, ratePlanNameOrCode, hotelCodes, state);
        if (CollectionUtils.isEmpty(ratePlanInfos)) {
            return Response.success(Pageable.empty());
        }
        //查询模版规则
        CouponTemplateDetailResp resp = couponTemplateDecorator.getCouponTemplateDetail(
                couponTemplateConverter.convertGetCouponTemplateDetailReqByRatePlan(request));
        List<CouponApplicableRatePlanResponse> ans = couponTemplateConverter.convertCouponApplicableRatePlans(resp.getUseRuleInfo(),
                ratePlanInfos,pageHotels);
        if (CollectionUtils.isEmpty(ans)) {
            return Response.success(Pageable.empty());
        }
        return Response.success(this.buildPageable(request.getPageIndex(), request.getPageSize(), ans.size(), ans));
    }


    private Map<Long, String> getHotelInfoMap(String blocCode, List<String> hotelCodes) {
        List<SelectHotelResp> selectHotels = hotelDecorator.selectHotels(blocCode, null, "", hotelCodes);
        return selectHotels.stream().collect(Collectors.toMap(SelectHotelResp::getHotelVid, SelectHotelResp::getHotelName, (o1, o2) -> o2));
    }

    private Map<String, String> getRoomTypeInfoMap(String blocCode) {
        List<RoomTypeBaseInfoResp> roomTypeBaseInfos = roomTypeDecorator.selectRoomTypesByBlocCode(blocCode, StateEnum.VALID.getValue());
        return roomTypeBaseInfos.stream().collect(Collectors.toMap(RoomTypeBaseInfoResp::getRoomTypeCode, RoomTypeBaseInfoResp::getRoomTypeName, (o1, o2) -> o2));
    }

    private TemplateHotelRoomTypeInfoDto buildApplicableHotelRoomTypeInfo(String blocCode,CouponTemplateUseRuleDto templateUseRuleDto) {
        TemplateHotelRoomTypeInfoDto response = new TemplateHotelRoomTypeInfoDto();
        ApplicableHotelRoomTypeInfoDto hotelApplicableRoomTypeInfo = templateUseRuleDto.getHotelApplicableRoomTypeInfo();
        if(Objects.isNull(hotelApplicableRoomTypeInfo)){
            return response;
        }
        List<String> hotelCodes = new ArrayList<>();
        List<ApplicableHotelRoomTypeDataInfoDto> roomTypeRuleDataInfos = new ArrayList<>();
        List<RoomTypeInfoResp> roomTypeInfoRespList = new ArrayList<>();
        if (!Objects.equals(hotelApplicableRoomTypeInfo.getApplicableScopeType(), ApplicableScopeTypeEnum.ALL.getScopeType())) {
            roomTypeRuleDataInfos = hotelApplicableRoomTypeInfo.getHotelRoomTypeInfos();
            hotelCodes = roomTypeRuleDataInfos.stream().map(ApplicableHotelRoomTypeDataInfoDto::getHotelCode).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(hotelCodes)) {
                roomTypeInfoRespList = roomTypeDecorator.listByHotelCodes(blocCode, hotelCodes);
            }
        }
        response.setApplicableScopeType(hotelApplicableRoomTypeInfo.getApplicableScopeType());
        if (!CollectionUtils.isEmpty(hotelCodes)) {
            Map<Long, String> hotelInfoMap = this.getHotelInfoMap(blocCode, hotelCodes);
            Map<String, String> roomTypeCodeAndNameMap = this.getRoomTypeInfoMap(blocCode);
            Map<Long, List<RoomTypeInfoResp>> hotelRoomTypeMap = roomTypeInfoRespList.stream().collect(Collectors.groupingBy(RoomTypeInfoResp::getHotelVid));
            response.setHotelRoomTypeInfos(
                    roomTypeRuleDataInfos.stream().map(item -> {
                        TemplateHotelRoomTypeDataInfoDto roomTypeDataInfoDto = new TemplateHotelRoomTypeDataInfoDto();
                        roomTypeDataInfoDto.setHotelCode(item.getHotelCode());
                        roomTypeDataInfoDto.setHotelName(hotelInfoMap.getOrDefault(Long.valueOf(item.getHotelCode()), ""));
                        roomTypeDataInfoDto.setRoomTypeNum(hotelRoomTypeMap.getOrDefault(Long.valueOf(item.getHotelCode()),
                                Collections.emptyList()).size());
                        if (!CollectionUtils.isEmpty(item.getRoomTypeInfos())) {
                            List<String> hotelRoomTypeCodes = item.getRoomTypeInfos().stream().map(CommonInfoDto::getCode).collect(Collectors.toList());
                            roomTypeDataInfoDto.setRoomTypeInfos(couponTemplateConverter.buildCommonNameInfos(hotelRoomTypeCodes, roomTypeCodeAndNameMap));
                        }
                        return roomTypeDataInfoDto;
                    }).collect(Collectors.toList())
            );
        }
        return response;
    }

    private TemplateHotelRatePlanInfoDto buildApplicableHotelRatePlanInfo(String blocCode, CouponTemplateUseRuleDto templateUseRuleDto) {
        TemplateHotelRatePlanInfoDto response = new TemplateHotelRatePlanInfoDto();
        ApplicableHotelRatePlanInfoDto hotelApplicableRatePlanInfos = templateUseRuleDto.getHotelApplicableRatePlanInfos();
        if(Objects.isNull(hotelApplicableRatePlanInfos)){
            return response;
        }
        List<String> hotelCodes = new ArrayList<>();
        List<HotelRatePlanListResp> hotelRatePlanList = new ArrayList<>();
        List<ApplicableHotelRatePlanDataInfoDto> ratePlanRuleDataInfos = new ArrayList<>();
        if (!Objects.equals(hotelApplicableRatePlanInfos.getApplicableScopeType(), ApplicableScopeTypeEnum.ALL.getScopeType())) {
            ratePlanRuleDataInfos = hotelApplicableRatePlanInfos.getHotelRatePlanInfos();
            hotelCodes = ratePlanRuleDataInfos.stream().map(ApplicableHotelRatePlanDataInfoDto::getHotelCode).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(hotelCodes)) {
                hotelRatePlanList = ratePlanDecorator.listRatePlan(blocCode, null, hotelCodes, StateEnum.VALID.getValue());
            }
        }
        response.setApplicableScopeType(hotelApplicableRatePlanInfos.getApplicableScopeType());
        if (!CollectionUtils.isEmpty(hotelCodes)) {
            Map<Long, String> hotelInfoMap = this.getHotelInfoMap(blocCode, hotelCodes);
            Map<String, String> ratePlanInfoMap = hotelRatePlanList.stream().collect(Collectors.toMap(HotelRatePlanListResp::getRatePlanCode, HotelRatePlanListResp::getRatePlanName, (o1, o2) -> o2));
            Map<String, List<HotelRatePlanListResp>> hotelRatePlanInfoMap = hotelRatePlanList.stream().collect(Collectors.groupingBy(HotelRatePlanListResp::getHotelCode));
            response.setHotelRatePlanInfos(
                    ratePlanRuleDataInfos.stream().map(item -> {
                        TemplateHotelRatePlanDataInfoDto ratePlanDataInfoDto = new TemplateHotelRatePlanDataInfoDto();
                        ratePlanDataInfoDto.setHotelCode(item.getHotelCode());
                        ratePlanDataInfoDto.setHotelName(hotelInfoMap.getOrDefault(Long.valueOf(item.getHotelCode()), ""));
                        ratePlanDataInfoDto.setRatePlanNum(hotelRatePlanInfoMap.getOrDefault(item.getHotelCode(), Collections.emptyList()).size());
                        if (!CollectionUtils.isEmpty(item.getRatePlanInfos())) {
                            List<String> hotelRatePlanCodes = item.getRatePlanInfos().stream().map(CommonInfoDto::getCode).collect(Collectors.toList());
                            ratePlanDataInfoDto.setRatePlanInfos(couponTemplateConverter.buildCommonNameInfos(hotelRatePlanCodes, ratePlanInfoMap));
                        }
                        return ratePlanDataInfoDto;
                    }).collect(Collectors.toList())
            );
        }
        return response;
    }


    private CouponTemplateInfoResponse buildCouponTemplateInfoResponse(String blocCode, CouponTemplateDetailResp resp) {
        CouponTemplateInfoResponse couponTemplateInfoResponse = couponTemplateConverter.convertCouponTemplateInfoResponse(resp);
        couponTemplateInfoResponse.setPostingItemName(buildPostingItemName(blocCode, resp));
        couponTemplateInfoResponse.setUseRuleInfo(this.buildCouponTemplateUseRuleDto(blocCode, resp.getUseRuleInfo()));
        couponTemplateInfoResponse.setGrantRuleInfo(couponTemplateConverter.convertCouponTemplateGrantRuleDto(resp.getGrantRuleInfo()));
        return couponTemplateInfoResponse;
    }

    private String buildPostingItemName(String blocCode, CouponTemplateDetailResp resp) {
        if (Objects.isNull(resp.getAccountingRuleInfo())
                || StringUtils.isEmpty(resp.getAccountingRuleInfo().getPostingItemCode())
                || Objects.equals(resp.getAccountingRuleInfo().getPostingType(), NumberUtils.INTEGER_ZERO)) {
            return Strings.EMPTY;
        }
        // 0-不入账 1-消费项 2-结算项
        if (Objects.equals(resp.getAccountingRuleInfo().getPostingType(), PostingTypeEnum.EXPENSE.getType())) {
            ExpenseInfoResp expense = accountExpenseDecorator.getExpense(blocCode, resp.getAccountingRuleInfo().getPostingItemCode());
            return Optional.ofNullable(expense).map(ExpenseInfoResp::getItemName).orElse(Strings.EMPTY);
        }
        SettlementItemInfoResp settlementInfo = accountSettlementDecorator.getSettlementInfo(blocCode, resp.getAccountingRuleInfo().getPostingItemCode());
        return Optional.ofNullable(settlementInfo).map(SettlementItemInfoResp::getItemName).orElse(Strings.EMPTY);
    }

    private TemplateUseRuleInfoDto buildCouponTemplateUseRuleDto(String blocCode, CouponTemplateUseRuleDto templateUseRuleDto) {
        TemplateUseRuleInfoDto templateUseRuleInfoDto = new TemplateUseRuleInfoDto();
        templateUseRuleInfoDto.setIsStacking(templateUseRuleDto.getIsStacking());
        templateUseRuleInfoDto.setRoomMinPrice(templateUseRuleDto.getRoomMinPrice());
        templateUseRuleInfoDto.setOrderMinPrice(templateUseRuleDto.getOrderMinPrice());
        templateUseRuleInfoDto.setAheadReserveDay(templateUseRuleDto.getAheadReserveDay());
        templateUseRuleInfoDto.setUnApplicableWeekInfos(templateUseRuleDto.getUnWeeks());
        templateUseRuleInfoDto.setUnApplicableTimeBucketInfos(
                couponTemplateConverter.buildTemplateTimeBucketInfoDto(templateUseRuleDto.getUnTimeBuckets()));
        templateUseRuleInfoDto.setApplicablePlatformChannels(templateUseRuleDto.getChannelInfos());
        templateUseRuleInfoDto.setApplicableUsersDto(templateUseRuleDto.getUsersInfo());
        templateUseRuleInfoDto.setHotelInfos(templateUseRuleDto.getHotelInfo());
        //添加酒店房型
        templateUseRuleInfoDto.setHotelRoomTypeInfos(buildApplicableHotelRoomTypeInfo(blocCode,templateUseRuleDto));
        //添加集团房型
        templateUseRuleInfoDto.setBlocRoomTypeInfos(templateUseRuleDto.getBlocRoomTypeInfos());
        //添加酒店房型
        templateUseRuleInfoDto.setHotelRatePlanInfos(buildApplicableHotelRatePlanInfo(blocCode,templateUseRuleDto));
        //添加集团房型
        templateUseRuleInfoDto.setBlocRatePlanInfos(couponTemplateConverter.convertTemplateBlocRatePlanInfos(templateUseRuleDto.getBlocRatePlanInfos()));
        return templateUseRuleInfoDto;
    }


    private <T> Pageable<T> buildPageable(Integer pageIndex, Integer pageSize, Integer total, List<T> datas) {
        datas = datas.stream().skip((long) (pageIndex - 1) * pageSize).limit(pageSize).collect(Collectors.toList());
        return new Pageable<>(pageIndex, Pageable.getTotalPage(pageSize, total), total, datas);
    }

}
