package com.ly.titc.pms.member.gateway.entity.request.spm.activity;

import com.ly.titc.pms.member.gateway.entity.request.spm.activity.dto.MemberCardSaleGearsDto;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 保存售卡活动
 *
 * <AUTHOR>
 * @date 2024/12/31 14:44
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SaveSaleCardActivityRequest extends MemberActivityCommonRequest {

    /**
     * 售卡活动
     */
    @Valid
    @NotEmpty(message = "售卡活动必填")
    private List<MemberCardSaleGearsDto> carriers;

    /**
     * 会员卡Id(售卡和升级必填)
     */
    private String cardId;

    /**
     * 会员卡名称(售卡和升级必填)
     */
    private String cardName;
}
