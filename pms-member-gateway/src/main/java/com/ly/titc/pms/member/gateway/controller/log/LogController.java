package com.ly.titc.pms.member.gateway.controller.log;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.ly.titc.cc.dubbo.entity.request.log.PageRecordsReq;
import com.ly.titc.cc.dubbo.entity.response.log.RecordResp;
import com.ly.titc.common.constants.Constant;
import com.ly.titc.common.entity.Pageable;
import com.ly.titc.common.entity.Response;
import com.ly.titc.common.util.LocalDateUtil;
import com.ly.titc.pms.member.com.constant.CommonConstant;
import com.ly.titc.pms.member.com.enums.ActionEnum;
import com.ly.titc.pms.member.com.enums.ModuleEnum;
import com.ly.titc.pms.member.gateway.entity.request.log.FuzzyQueryRequest;
import com.ly.titc.pms.member.gateway.entity.request.log.PageLogRequest;
import com.ly.titc.pms.member.gateway.entity.response.log.ActionResponse;
import com.ly.titc.pms.member.gateway.entity.response.log.LogResponse;
import com.ly.titc.pms.member.gateway.entity.response.log.ModuleResponse;
import com.ly.titc.pms.member.gateway.handler.log.AbstractModuleServiceHandler;
import com.ly.titc.pms.member.gateway.handler.log.ModuleServiceHandlerManager;
import com.ly.titc.pms.member.mediator.rpc.dubbo.common.OperationLogDubboInterface;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.time.ZoneOffset;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 操作日志
 * @Author：rui
 * @name：LogController
 * @Date：2023-11-21 17:09
 * @Filename：LogController
 */
@Slf4j
@RestController
@RequestMapping(value = "log")
public class LogController {

    @Resource(name = "logInterface")
    private OperationLogDubboInterface operationLogDecorator;

    /**
     * 日志分页
     *
     * @param request
     * @return
     */
    @PostMapping("/pageLog")
    public Response<Pageable<LogResponse>> pageLog(@Valid @RequestBody PageLogRequest request) {
        Pageable<LogResponse> result = new Pageable<>();

        PageRecordsReq req = prepareRequest(request);
        Pageable<RecordResp> recordRespPageable = operationLogDecorator.pageLog(req);
        List<RecordResp> records = recordRespPageable.getDatas();
        if(CollectionUtils.isEmpty(records)){
            result.setTotalPage(0).setTotal(0).setCurrPage(0).setDatas(Collections.emptyList());
            return Response.success(result);
        }
        List<LogResponse> collect = records.stream().map(this::assembleLogResponse).collect(Collectors.toList());
        result.setDatas(collect);
        result.setCurrPage(request.getPageIndex())
                .setTotal(recordRespPageable.getTotal()).setTotalPage(recordRespPageable.getTotalPage());
        return Response.success(result);
    }

    private LogResponse assembleLogResponse(RecordResp record) {
        LogResponse response = new LogResponse();
        String opContent = record.getOpContent();
        response.setModuleName(record.getBizName()).setActionName(record.getCategoryName()).setOperator(record.getOperator()).setGmtCreate(record.getDateTime())
                .setIp(record.getClientIp()).setTrackingId(record.getTrackingId());
        if (StringUtils.isNotEmpty(opContent)) {
            try {
                String name="";
                Map<String, Object> tags = record.getTags();
                if(null!=tags){
                    name = (String)tags.get("name");
                    //设置响应名称
                    response.setName(name);
                }
                JSONObject json = JSONObject.parseObject(opContent);
                if(StringUtils.isEmpty(name)){
                    name = json.getString("name");
                    response.setName(name);
                }
                if (json.containsKey("records")) {
                    String records = json.getString("records");
                    if (StringUtils.isNotEmpty(records)) {
                        response.setRecords(JSONObject.parseArray(records, String.class));
                    }
                }
            }catch (Exception e){
                response.setRecords(Arrays.asList(opContent));
            }
        }
        return response;
    }

    /**
     * 预处理入参
     *
     * @param request
     * @return
     */
    private PageRecordsReq prepareRequest(PageLogRequest request) {
        PageRecordsReq req = new PageRecordsReq();
        Integer moduleId = request.getModuleId();
        Integer actionId = request.getActionId();
        String startDate = request.getStartDate();
        String endDate = request.getEndDate();
        String name = request.getName();
        Map<String, Object> tags = new HashMap<>();
        req.setState(CommonConstant.LOG_SUCCESS_STATE);
        req.setTenant(CommonConstant.PROJECT_CODE).setOperator(request.getOperator()).setPageIndex(request.getPageIndex()).setPageSize(request.getPageSize())
                .setTrackingId(request.getTrackingId());
        if (null != moduleId) {
            req.setBizCode(String.valueOf(moduleId)).setBizName(null != ModuleEnum.getByCode(moduleId) ? ModuleEnum.getByCode(moduleId).getDesc() : Constant.STRING_EMPTY);
        }
        if (null != actionId) {
            req.setCategory(String.valueOf(actionId)).setCategoryName(null != ActionEnum.getByCode(actionId) ? ActionEnum.getByCode(actionId).getDesc() : Constant.STRING_EMPTY);
        }
        if (StringUtils.isNotEmpty(startDate)) {
            req.setStartTime(LocalDateUtil.parseByNormalDate(startDate).atStartOfDay(ZoneOffset.ofHours(Constant.EIGHT)).toInstant().toEpochMilli());
        }
        if (StringUtils.isNotEmpty(endDate)) {
            req.setEndTime(LocalDateUtil.parseByNormalDate(endDate).plusDays(1).atStartOfDay(ZoneOffset.ofHours(Constant.EIGHT)).toInstant().toEpochMilli());
        }
        if (StringUtils.isNotEmpty(request.getIp())) {
            req.setClientIp(request.getIp());
        }
        if (StringUtils.isNotEmpty(request.getLogOperator())) {
            req.setOperator(request.getLogOperator());
        }
        if (StringUtils.isNotEmpty(name)) {
            tags.put("name", name);
        }
//        if (StringUtils.isNotEmpty(hotelCode)) {
//            tags.put("hotelCode", hotelCode);
//        }
        if (StringUtils.isNotEmpty(request.getRecord())) {
            req.setOpContent(request.getRecord());
        }
        tags.put("blocCode", request.getBlocCode());
        req.setTags(tags);
        return req;
    }

    /**
     * 操作模块下拉列表
     *
     * @return
     */
    @PostMapping("/selectModule")
    public Response<List<ModuleResponse>> selectModule() {
        List<ModuleResponse> result = Arrays.stream(ModuleEnum.values()).sorted(Comparator.comparing(ModuleEnum::getSort)).map(moduleEnum -> {
            ModuleResponse moduleResponse = new ModuleResponse();
            moduleResponse.setModuleId(moduleEnum.getCode()).setModuleName(moduleEnum.getDesc());
            return moduleResponse;
        }).collect(Collectors.toList());
        result.remove(null);
        return Response.success(result);
    }

    /**
     * 操作行为下拉列表
     *
     * @return
     */
    @PostMapping("/selectAction")
    public Response<List<ActionResponse>> selectAction() {
        List<ActionResponse> result = Arrays.stream(ActionEnum.values()).sorted(Comparator.comparing(ActionEnum::getSort)).map(actionEnum -> {
            ActionResponse actionResponse = new ActionResponse();
            actionResponse.setActionId(actionEnum.getCode()).setActionName(actionEnum.getDesc());
            return actionResponse;
        }).collect(Collectors.toList());
        return Response.success(result);
    }

    /**
     * 根据操作模块模糊查询名称
     *
     * @return
     */
    @PostMapping("/selectNameByModule")
    public Response<List<String>> selectNameByModule(@Valid @RequestBody FuzzyQueryRequest request) {
        String trackingId = request.getTrackingId();
        Integer moduleId = request.getModuleId();
        String name = request.getName();
        String blocCode = request.getBlocCode();
//        String hotelCode = request.getHotelCode();
        AbstractModuleServiceHandler instance = ModuleServiceHandlerManager.getInstance(moduleId);
        if (null == instance) {
            return Response.success(Collections.emptyList());
        }
        return Response.success(instance.execute(blocCode, null, name, trackingId));
    }
}
