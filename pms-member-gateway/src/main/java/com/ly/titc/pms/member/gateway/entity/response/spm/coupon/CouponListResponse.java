package com.ly.titc.pms.member.gateway.entity.response.spm.coupon;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024-11-4 19:53
 */
@Data
@Accessors(chain = true)
public class CouponListResponse {
    /**
     * 券编号
     */
    private String couponCode;

    /**
     * 券类型
     */
    private Integer couponType;

    /**
     * 券类型名
     */
    private String couponTypeName;

    /**
     * 券名称
     */
    private String couponName;

    /**
     * 券优惠
     */
    private String couponValue;

    /**
     * 生成门店
     * 中央生成 or  xxx酒店
     */
    private String hotelName;

    /**
     * 发放事件
     */
    private String issueEvent;

    /**
     * 发放日期
     */
    private String issueDate;

    /**
     * 开始时间
     */
    private String startDate;

    /**
     * 过期时间
     * 若永久有效，则2099-12-31
     */
    private String endDate;

    /**
     * 是否永久有效
     */
    private boolean isLongTerm;

    /**
     * 券模板code
     */
    private String templateCode;

    /**
     * 模板版本
     */
    private Integer tempVersion;

    /**
     * 有效时间描述
     */
    private String effectiveTimeDesc;

    /**
     * 券模板创建来源
     */
    private String sourceClient;

    /**
     * 状态
     */
    private Integer state;

    /**
     * 状态名称
     */
    private String stateName;

    /**
     * 所属人名称
     */
    private String owner;

    /**
     * 会员编号
     */
    private String memberNo;

    /**
     * 客户编号
     */
    private String customerNo;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 身份证id
     */
    private String idCard;

    /**
     * 使用时间
     */
    private String useTime;

    private Integer isSys;

}
