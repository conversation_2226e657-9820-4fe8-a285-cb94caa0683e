package com.ly.titc.pms.member.gateway.entity.request.spm.activity;

import com.ly.titc.pms.member.gateway.entity.request.BaseRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024-12-16 11:41
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class GetActivityRequest extends BaseRequest {

    /**
     * 活动编码
     */
    @NotEmpty(message = "活动编码不能为空")
    private String activityCode;
}
