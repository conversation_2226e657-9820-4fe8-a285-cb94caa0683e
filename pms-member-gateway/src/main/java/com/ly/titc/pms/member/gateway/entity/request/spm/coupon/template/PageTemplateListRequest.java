package com.ly.titc.pms.member.gateway.entity.request.spm.coupon.template;

import com.ly.titc.pms.member.gateway.entity.request.PageBaseRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Description:
 * @Author: huaizhu.zhong
 * @Date: 2024-11-6
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PageTemplateListRequest extends PageBaseRequest {
    /**
     * 券类型 1.折扣券,2.代金券,3.免房券,4.餐饮券,5.抵扣券,6.小时券,7.延时券,8.升房券,9.体验券,10.通用券
     * 查询全部时，参数 [不传 或 null 或 0]
     */
    private Integer couponType;

    /**
     * 券名称
     */
    private String couponName;

    /**
     * 券状态 1-已生效 2-已作废 3-已过期
     */
    private Integer state;

    /**
     * 过期开始时间
     * 礼包查询可用的优惠券，当前字段表示礼包的结束时间
     */
    private String expiredStartTime;
}
