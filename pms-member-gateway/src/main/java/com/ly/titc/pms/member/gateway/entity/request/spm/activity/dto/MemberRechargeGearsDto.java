package com.ly.titc.pms.member.gateway.entity.request.spm.activity.dto;

import com.ly.titc.pms.spm.dubbo.entity.dto.activity.gear.BaseGearDto;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 储值充值
 *
 * <AUTHOR>
 * @date 2024/12/31 10:38
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MemberRechargeGearsDto extends BaseGearDto {

    /**
     * 充值面额
     */
    private BigDecimal rechargeAmount;

    /**
     * 赠送礼金
     */
    private BigDecimal giftAmount;

    /**
     * 优惠后需付金额
     */
    private BigDecimal discountAmount;

}
