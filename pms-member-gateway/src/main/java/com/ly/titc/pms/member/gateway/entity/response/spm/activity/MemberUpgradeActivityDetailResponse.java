package com.ly.titc.pms.member.gateway.entity.response.spm.activity;

import com.ly.titc.pms.member.gateway.entity.request.spm.activity.dto.MemberUpgradeGearsDto;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 升级活动返回
 *
 * <AUTHOR>
 * @date 2024/12/31 14:54
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MemberUpgradeActivityDetailResponse extends MemberActivityCommonResponse {

    /**
     * 档位
     */
    private List<MemberUpgradeGearsDto> carriers;

    /**
     * 会员卡code
     */
    private String cardId;

    /**
     * 会员卡名称
     */
    private String cardName;

}
