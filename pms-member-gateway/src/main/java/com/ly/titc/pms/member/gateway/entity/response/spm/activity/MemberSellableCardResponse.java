package com.ly.titc.pms.member.gateway.entity.response.spm.activity;

import lombok.Data;

/**
 * 可售卡
 *
 * <AUTHOR>
 * @date 2025/1/8 20:54
 */
@Data
public class MemberSellableCardResponse {

    /**
     * 卡ID
     */
    private Integer cardId;

    /**
     * 卡类型 1: 基础卡 2 企业卡
     */
    private Integer cardType;

    /**
     * 卡名称
     */
    private String cardName;

    /**
     * 是否默认卡
     */
    private Integer isDefault;

}
