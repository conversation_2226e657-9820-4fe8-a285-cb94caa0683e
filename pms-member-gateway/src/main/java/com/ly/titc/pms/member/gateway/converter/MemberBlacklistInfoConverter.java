package com.ly.titc.pms.member.gateway.converter;

import com.ly.titc.pms.member.gateway.entity.request.blacklist.BlacklistedRequest;
import com.ly.titc.pms.member.gateway.entity.request.blacklist.QueryBlacklistedRequest;
import com.ly.titc.pms.member.gateway.entity.request.blacklist.CancelBlacklistedRequest;
import com.ly.titc.pms.member.gateway.entity.response.blacklist.BlacklistedResponse;
import com.ly.titc.pms.member.mediator.entity.dto.blacklist.BlacklistInfoDto;
import com.ly.titc.pms.member.mediator.entity.dto.blacklist.BlacklistMemberDto;
import com.ly.titc.pms.member.mediator.entity.dto.blacklist.CancelBlacklistMemberDto;
import com.ly.titc.pms.member.mediator.entity.dto.blacklist.ListBlacklistParamDto;
import org.mapstruct.Mapper;

import java.util.Arrays;

/**
 * @Author：rui
 * @name：MemberBlacklistInfoConverter
 * @Date：2024-12-10 19:51
 * @Filename：MemberBlacklistInfoConverter
 */
@Mapper(componentModel = "spring", imports = {Arrays.class})
public interface MemberBlacklistInfoConverter {

//    MemberBlacklistInfoDto convert(BlacklistedRequest request);
//
//    List<QueryBlacklistedResponse> convertResponses(List<MemberBlacklistInfoDto> dtoList);

    BlacklistMemberDto convertRequestToDto(BlacklistedRequest request);

    CancelBlacklistMemberDto convertRequestToDto(CancelBlacklistedRequest request);

    ListBlacklistParamDto convertRequestToDto(QueryBlacklistedRequest request);

    BlacklistedResponse convertDtoToResponse(BlacklistInfoDto blacklistInfoDto);

}
