package com.ly.titc.pms.member.gateway.controller.member;

import com.ly.titc.cc.sdk.log.annotation.LogRecord;
import com.ly.titc.common.constants.Constant;
import com.ly.titc.common.entity.Response;
import com.ly.titc.common.enums.PlatformChannelEnum;
import com.ly.titc.pms.member.com.annotation.OperationLog;
import com.ly.titc.pms.member.com.constant.ActionConstants;
import com.ly.titc.pms.member.com.constant.CommonConstant;
import com.ly.titc.pms.member.com.constant.ModuleConstants;
import com.ly.titc.pms.member.com.enums.BlacklistSceneEnum;
import com.ly.titc.pms.member.com.lang.TwoTuple;
import com.ly.titc.pms.member.com.utils.ConfigCenterUtil;
import com.ly.titc.pms.member.gateway.converter.MemberBlacklistInfoConverter;
import com.ly.titc.pms.member.gateway.entity.request.blacklist.BlacklistedRequest;
import com.ly.titc.pms.member.gateway.entity.request.blacklist.CancelBlacklistedRequest;
import com.ly.titc.pms.member.gateway.entity.request.blacklist.QueryBlacklistedRequest;
import com.ly.titc.pms.member.gateway.entity.response.SelectResponse;
import com.ly.titc.pms.member.gateway.entity.response.blacklist.BlacklistedResponse;
import com.ly.titc.pms.member.mediator.entity.dto.blacklist.BlacklistInfoDto;
import com.ly.titc.pms.member.mediator.entity.dto.blacklist.BlacklistMemberDto;
import com.ly.titc.pms.member.mediator.entity.dto.blacklist.CancelBlacklistMemberDto;
import com.ly.titc.pms.member.mediator.entity.dto.blacklist.ListBlacklistParamDto;
import com.ly.titc.pms.member.mediator.service.MemberBlacklistMedService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 会员黑名单服务
 *
 * @Author：rui
 * @name：MemberBlacklistInfoController
 * @Date：2024-12-10 19:21
 * @Filename：MemberBlacklistInfoController
 */
@Slf4j
@RestController
@RequestMapping("/member/blacklist")
public class MemberBlacklistInfoController {

    @Resource
    private MemberBlacklistMedService memberBlacklistMedService;

    @Resource
    private MemberBlacklistInfoConverter converter;

    /**
     * 黑名单场景
     *
     * @return
     */
    @PostMapping("/listScene")
    public Response<SelectResponse> listBlacklistScene(){
        return Response.success(Arrays.stream(BlacklistSceneEnum.values()).map(e -> new SelectResponse().setText(e.getDesc()).setValue(e.getType())).collect(Collectors.toList()));
    }

    /**
     * 黑名单渠道
     *
     * @return
     */
    @PostMapping("/listPlatformChannel")
    public Response<SelectResponse> listPlatformChannel(){
        return Response.success(Arrays.stream(PlatformChannelEnum.values()).map(e -> new SelectResponse().setText(e.getPlatformChannelDesc()).setValue(e.getPlatformChannel())).collect(Collectors.toList()));
    }

    /**
     * 会员拉黑
     *
     * @param request
     * @return
     */
    @PostMapping
    @LogRecord(tenant = CommonConstant.PROJECT_CODE, bizCode = ModuleConstants.MEMBER_MANAGE_CODE, bizName = ModuleConstants.MEMBER_MANAGE_NAME,
            category = ActionConstants.MEMBER_BLACKLIST_CODE, categoryName = ActionConstants.MEMBER_BLACKLIST_NAME)
    @OperationLog(operateRecord = "#member_blacklist(#request)")
    public Response<String> blacklist(@RequestBody @Valid BlacklistedRequest request) {
        TwoTuple<Integer, String> masterTuple = ConfigCenterUtil.getMasterByBloc(request.getBlocCode());
        BlacklistMemberDto dto = converter.convertRequestToDto(request);
        dto.setMasterType(masterTuple.getFirst());
        dto.setMasterCode(masterTuple.getSecond());
        dto.setSourceType(Constant.ONE);
        dto.setSource(request.getBlocCode());
        String blacklistNo = memberBlacklistMedService.blacklist(dto);
        return Response.success(blacklistNo);
    }

    /**
     * 取消拉黑
     *
     * @param request
     * @return
     */
    @PostMapping("/cancel")
    @LogRecord(tenant = CommonConstant.PROJECT_CODE, bizCode = ModuleConstants.MEMBER_MANAGE_CODE, bizName = ModuleConstants.MEMBER_MANAGE_NAME,
            category = ActionConstants.MEMBER_CANCEL_BLACKLIST_CODE, categoryName = ActionConstants.MEMBER_CANCEL_BLACKLIST_NAME)
    @OperationLog(operateRecord = "#member_cancelBlacklist(#request)")
    public Response<String> unBlacklisted(@RequestBody @Valid CancelBlacklistedRequest request) {
        TwoTuple<Integer, String> masterTuple = ConfigCenterUtil.getMasterByBloc(request.getBlocCode());
        CancelBlacklistMemberDto dto = converter.convertRequestToDto(request);
        dto.setMasterType(masterTuple.getFirst());
        dto.setMasterCode(masterTuple.getSecond());
        String blacklistNo = memberBlacklistMedService.cancelBlacklist(dto);
        return Response.success(blacklistNo);
    }

    /**
     * 会员黑名单列表
     */
    @PostMapping("/list")
    public Response<List<BlacklistedResponse>> list(@RequestBody @Valid QueryBlacklistedRequest request) {
        TwoTuple<Integer, String> masterTuple = ConfigCenterUtil.getMasterByBloc(request.getBlocCode());
        ListBlacklistParamDto dto = converter.convertRequestToDto(request);
        dto.setMasterType(masterTuple.getFirst());
        dto.setMasterCode(masterTuple.getSecond());
        List<BlacklistInfoDto> blacklistInfos = memberBlacklistMedService.listBlacklist(dto);

        return Response.success(blacklistInfos.stream().map(converter::convertDtoToResponse).collect(Collectors.toList()));
    }
}
