package com.ly.titc.pms.member.gateway.controller.brand;

import com.ly.titc.cc.sdk.log.annotation.LogRecord;
import com.ly.titc.common.entity.Pageable;
import com.ly.titc.common.entity.Response;
import com.ly.titc.common.enums.StatusEnum;
import com.ly.titc.common.exceptions.ServiceException;
import com.ly.titc.mdm.entity.request.brand.GetByCodeReq;
import com.ly.titc.mdm.entity.request.brand.PageBrandsReq;
import com.ly.titc.mdm.entity.request.brand.SaveBrandReq;
import com.ly.titc.mdm.entity.request.brand.SelectBrandReq;
import com.ly.titc.mdm.entity.request.hotel.SelectHotelReq;
import com.ly.titc.mdm.entity.response.brand.BrandDetailInfoResp;
import com.ly.titc.mdm.entity.response.brand.PageBrandsResp;
import com.ly.titc.mdm.entity.response.brand.SelectBrandResp;
import com.ly.titc.mdm.entity.response.hotel.SelectHotelResp;
import com.ly.titc.mdm.enums.HotelOpenStateEnum;
import com.ly.titc.pms.member.com.annotation.OperationLog;
import com.ly.titc.pms.member.com.constant.ActionConstants;
import com.ly.titc.pms.member.gateway.entity.request.brand.SelectBrandRequest;
import com.ly.titc.pms.member.gateway.thread.local.UserInfoThreadHolder;
import com.ly.titc.pms.member.mediator.entity.dto.user.UserInfoDto;
import com.ly.titc.pms.member.mediator.rpc.dsf.mdm.BrandDecorator;
import com.ly.titc.pms.member.mediator.rpc.dsf.mdm.HotelDecorator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Arrays;
import java.util.List;


/**
 *  品牌相关
 * @Description: 
 * @Author: lixu
 * @Date: 2022/11/28
 */
@Slf4j
@RestController
@RequestMapping("/brand")
public class BrandController {

    @Resource(type = BrandDecorator.class)
    private BrandDecorator brandDecorator;
    @Resource(type = HotelDecorator.class)
    private HotelDecorator hotelDecorator;

    /**
     * 品牌下拉列表
     * @param request
     * @return
     */
    @PostMapping("/selectBrands")
    public Response<List<SelectBrandResp>> selectBrands(@Valid @RequestBody SelectBrandRequest request){
        UserInfoDto user = UserInfoThreadHolder.getUser();
        SelectBrandReq req = new SelectBrandReq();
        req.setBrandName(request.getBrandName()).setState(request.getState()).setBlocCode(user.getBlocCode());
        return Response.success(brandDecorator.selectBrands(req));
    }
}
