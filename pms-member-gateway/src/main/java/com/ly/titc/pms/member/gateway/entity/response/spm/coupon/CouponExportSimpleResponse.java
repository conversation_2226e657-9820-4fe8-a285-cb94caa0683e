package com.ly.titc.pms.member.gateway.entity.response.spm.coupon;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024-11-7 10:21
 */
@Getter
@Setter
@EqualsAndHashCode
public class CouponExportSimpleResponse {


    @ExcelProperty("券编号")
    private String couponCode;


    @ExcelProperty("门店名称")
    private String hotelName;


    @ExcelProperty("客户名称")
    private String customerName;


    @ExcelProperty("客户编号")
    private String customerNo;


    @ExcelProperty("会员卡号")
    private String memberNo;


    @ExcelProperty("手机号")
    private String mobile;
}
