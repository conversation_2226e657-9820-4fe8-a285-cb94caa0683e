package com.ly.titc.pms.member.gateway.entity.request.member;

import com.ly.titc.pms.member.gateway.entity.request.BaseRequest;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @Author：rui
 * @name：GetDetailBaseReq
 * @Date：2024-11-12 17:40
 * @Filename：GetDetailBaseReq
 */
@Data
public class GetDetailBaseRequest extends BaseRequest {

    /**
     * id
     */
    @NotNull(message = "id不能为空")
    private Long id;
}
