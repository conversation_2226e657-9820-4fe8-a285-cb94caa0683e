package com.ly.titc.pms.member.gateway.entity.request.spm.activity.dto;

import com.ly.titc.pms.spm.dubbo.entity.dto.activity.gear.BaseGearDto;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Description:
 * @Author: huaizhu.zhong
 * @Date: 2025-3-13
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class EventActivityGearDto extends BaseGearDto {
    /**
     * 会员卡Id
     */
    private String memberCardId;

    /**
     * 会员卡等级
     */
    private Integer memberCardLevel;

    /**
     * 会员卡等级描述
     */
    private String memberCardLevelDesc;

    /**
     * 档位值
     */
    private String value;
}
