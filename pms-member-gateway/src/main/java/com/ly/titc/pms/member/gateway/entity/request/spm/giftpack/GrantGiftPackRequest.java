package com.ly.titc.pms.member.gateway.entity.request.spm.giftpack;

import com.ly.titc.pms.member.gateway.entity.request.BaseRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024-11-28 16:19
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class GrantGiftPackRequest extends BaseRequest {

    /**
     * 礼包编号
     */
    @NotBlank(message = "礼包编号不能为空")
    private String giftPackNo;

    /**
     * 版本号
     */
    @NotNull(message = "礼包版本")
    private Integer version;

    /**
     * 发放类型 1-单个发放 2-批量发放
     */
    @NotNull(message = "发放类型不能为空")
    private Integer grantType;

    /**
     * 发放数量
     */
    @NotNull(message = "发放数量不能为空")
    private Integer grantNum;

    /**
     * 接收人类型
     * customer-客户 member-会员 room_order-房单
     */
    private String receiverType;

    /**
     * 接收人code
     */
    private String receiverCode;

    /**
     * 接收人类型时客户时所在的酒店code
     */
    private String customerHotelCode;


}
