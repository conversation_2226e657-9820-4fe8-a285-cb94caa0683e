package com.ly.titc.pms.member.gateway.entity.request.spm.coupon;

import com.ly.titc.pms.member.gateway.entity.request.BaseRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 发放优惠券参数
 * @Description
 * <AUTHOR>
 * @Date 2024-11-6 13:52
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class GrantCouponRequest extends BaseRequest {
    /**
     * 优惠券模板编号
     */
    @NotBlank(message = "优惠券模板code不能为空")
    private String templateCode;

    /**
     * 优惠券模版版本号
     */
    @NotBlank(message = "优惠券版本不能为空")
    private String version;

    /**
     * 发放数量
     */
    @NotNull(message = "发放数量不能为空")
    private Integer grantNum;

    /**
     * 门店生成时 选择是否跨店
     */
    private Integer crossShop;

    /**
     * 门店生成时 的门店code
     */
    private String hotelCode;

    /**
     * 接收人类型
     * customer-客户 member-会员 room_order-房单
     */
    private String receiverType;

    /**
     * 接收人code
     */
    private String receiverCode;

    /**
     * 接收人类型时客户时所在的酒店code
     */
    private String customerHotelCode;

    /**
     * 是否短信通知
     */
    private Boolean toSmsNotify = false;

}
