package com.ly.titc.pms.member.gateway.entity.request.spm.activity.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Description:
 * @Author: huaizhu.zhong
 * @Date: 2025-3-12
 */
@Data
public class StoredValueRuleDto {

    /**
     * 储值规则 0-固定储值 1-阶梯储值
     */
    private Integer storedValueType;

    /**
     * 固定储值金额
     */
    private String fixedStoredValue;

    /**
     * 阶梯储值是否相同 0-不相同 1-相同
     */
    private Integer isStairsSame;

    /**
     * 阶梯储值阶梯数
     */
    private List<String> stairsStoredValues;
}
