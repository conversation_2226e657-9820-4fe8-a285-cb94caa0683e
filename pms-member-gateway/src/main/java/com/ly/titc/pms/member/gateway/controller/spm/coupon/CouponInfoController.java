package com.ly.titc.pms.member.gateway.controller.spm.coupon;


import cn.hutool.core.collection.CollUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.fastjson2.JSON;
import com.ly.titc.common.entity.Pageable;
import com.ly.titc.common.entity.Response;
import com.ly.titc.common.util.PageableUtil;
import com.ly.titc.mdm.entity.response.hotel.HotelBaseInfoResp;
import com.ly.titc.pms.customer.dubbo.entity.request.customer.GetByCustomerNoReq;
import com.ly.titc.pms.customer.dubbo.entity.response.customer.CustomerInfoResp;
import com.ly.titc.pms.member.dubbo.enums.IdTypeEnum;
import com.ly.titc.pms.member.dubbo.enums.MasterTypeEnum;
import com.ly.titc.pms.member.dubbo.enums.MemberStateEnum;
import com.ly.titc.pms.member.gateway.converter.CouponConverter;
import com.ly.titc.pms.member.gateway.entity.request.spm.coupon.*;
import com.ly.titc.pms.member.gateway.entity.response.spm.coupon.*;
import com.ly.titc.pms.member.gateway.thread.local.UserInfoThreadHolder;
import com.ly.titc.pms.member.mediator.entity.dto.member.MemberDetailDto;
import com.ly.titc.pms.member.mediator.entity.dto.member.MemberInfoDto;
import com.ly.titc.pms.member.mediator.entity.dto.user.UserInfoDto;
import com.ly.titc.pms.member.mediator.rpc.dsf.mdm.HotelDecorator;
import com.ly.titc.pms.member.mediator.rpc.dubbo.coupon.CouponDecorator;
import com.ly.titc.pms.member.mediator.rpc.dubbo.customer.CustomerDecorator;
import com.ly.titc.pms.member.mediator.service.MemberMedService;
import com.ly.titc.pms.spm.dubbo.entity.request.coupon.*;
import com.ly.titc.pms.spm.dubbo.entity.response.coupon.*;
import com.ly.titc.pms.spm.dubbo.enums.ReceiverTypeEnum;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.net.URLEncoder;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 优惠券发放及发放记录管理
 *
 * @Description 优惠券发放及发放记录管理
 * <AUTHOR>
 * @Date 2024-11-4 14:25
 */
@Slf4j
@RestController
@RequestMapping("/coupon/info")
public class CouponInfoController {
    @Resource(type = HotelDecorator.class)
    private HotelDecorator hotelDecorator;
    @Resource(type = CouponDecorator.class)
    private CouponDecorator couponDecorator;
    @Resource(type = CouponConverter.class)
    private CouponConverter couponConverter;
    @Resource(type = CustomerDecorator.class)
    private CustomerDecorator customerDecorator;
    @Resource(type = MemberMedService.class)
    private MemberMedService memberMedService;



    /**
     * 根据 会员卡号、客户编号、手机号、身份证号 查询身份信息的接口
     */
    @PostMapping("/getGrantUserInfo")
    public Response<CouponReceiverInfoResponse> getGrantUserInfo(@Valid @RequestBody GetGrantReceiverRequest request) {
        if (StringUtils.isNotBlank(request.getMemberNo())) {
            return Response.success(getInfoByMemberNo(request));
        }
        if (StringUtils.isNotBlank(request.getIdCard())) {
            return Response.success(getInfoByIdCard(request));
        }
        if (StringUtils.isNotBlank(request.getMobile())) {
            return Response.success(getInfoByMobile(request));
        }
        if (StringUtils.isNotBlank(request.getCustomerNo())) {
            return Response.success(getInfoByCustomerNo(request));
        }
        return Response.success();
    }

    /**
     * 优惠券发放明细分页列表查询
     */
    @PostMapping("/pageInfos")
    public Response<Pageable<CouponListResponse>> pageInfos(@Valid @RequestBody PageCouponRequest req) {
        return Response.success(this.pageCoupon(req));
    }

    /**
     * 优惠券分页列表导出
     */
    @SneakyThrows
    @PostMapping("/export")
    public Response<Void> export(@Valid @RequestBody PageCouponRequest req, HttpServletResponse response) {
        req.setPageSize(Integer.MAX_VALUE);
        Pageable<CouponListResponse> pageInfos = this.pageCoupon(req);
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode("优惠券查询", "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
        response.setHeader("Access-Control-Expose-Headers", "Content-Disposition,x-access-titc-c-token");
        List<CouponExportResponse> data = couponConverter.convertCouponExportResponses(pageInfos.getDatas());
        EasyExcel.write(response.getOutputStream(), CouponExportResponse.class).sheet("优惠券").doWrite(data);
        return Response.success();
    }


    /**
     * 获取某人各状态优惠券统计信息
     */
    @PostMapping("/getCouponStatistics")
    public Response<CouponStatisticsResponse> getCouponStatistics(@Valid @RequestBody CouponStatisticsRequest req) {
        StatisticsCouponReq apiReq = couponConverter.convertStatisticsCouponReq(req);
        CouponStatisticsResp apiResp = couponDecorator.statisticsCoupon(apiReq);
        return Response.success(couponConverter.convertCouponStatisticsResponse(apiResp));
    }


    /**
     * 获取券的核销信息
     */
    @PostMapping("/getRedeemInfo")
    public Response<CouponRedeemInfoResponse> getCouponRedeemInfo(@Valid @RequestBody GetCouponRequest request) {
        GetCouponInfoReq apiReq = couponConverter.convertGetCouponRequest2GetRedeemReq(request);
        CouponRedeemInfoResp apiResp = couponDecorator.getCouponRedeemInfo(apiReq);
        return Response.success(couponConverter.convertCouponRedeemInfoResp2Response(apiResp));
    }


    /**
     * 发放优惠券(批量发放)
     */
    @SneakyThrows
    @PostMapping("/grantCouponByImport")
    public Response<Void> grantCouponByImport(@RequestParam(value = "templateCode") String templateCode,
                                              @RequestParam(value = "version") Integer version,
                                              @RequestParam(value = "grantNum") Integer grantNum,
                                              @RequestParam(value = "crossShop") Integer crossShop,
                                              @RequestParam(value = "hotelCode", required = false) String hotelCode,
                                              @RequestParam(value = "toSmsNotify", required = false) Boolean toSmsNotify,
                                              @RequestParam("file") MultipartFile file) {
        int batchSize = 200;
        EasyExcel.read(file.getInputStream(), GrantCoupon.class,new AnalysisEventListener<GrantCoupon>() {
                    final List<GrantCoupon> grantCoupons = Lists.newArrayList();
                    @Override
                    public void invoke(GrantCoupon data, AnalysisContext analysisContext) {
                        log.info("解析到【批量发放优惠券模版】一条数据:{}", JSON.toJSONString(data));
                        grantCoupons.add(data);
                        if (grantCoupons.size() >= batchSize) {
                            batchGrantCoupon(templateCode, version, grantNum, crossShop, hotelCode, toSmsNotify, grantCoupons);
                            grantCoupons.clear();
                        }
                    }

                    @Override
                    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
                        log.info("【批量发放优惠券模版】解析完成");
                        if (!CollectionUtils.isEmpty(grantCoupons)) {
                            batchGrantCoupon(templateCode, version, grantNum, crossShop, hotelCode, toSmsNotify, grantCoupons);
                        }
                    }
                }).headRowNumber(1).sheet(0).doRead();
        return Response.success();
    }

    /**
     * 发放优惠券(单个发放)
     */
    @PostMapping("/grantCouponByBind")
    public Response<Void> grantCouponByBind(@Valid @RequestBody GrantCouponRequest req) {
        couponDecorator.grantCouponsByBind(couponConverter.convertGrantCouponBatchReq(req));
        return Response.success();
    }


    /**
     * 发放优惠券(导出券码)
     */
    @SneakyThrows
    @PostMapping("/exportCouponForGrant")
    public Response<Void> exportCouponForGrant(@Valid @RequestBody GrantCouponRequest req, HttpServletResponse response) {
        List<String> couponCodes = couponDecorator.grantCouponsByExport(couponConverter.convertGrantCouponBatchReq(req));
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode("优惠券码", "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
        response.setHeader("Access-Control-Expose-Headers", "Content-Disposition,x-access-titc-c-token");
        EasyExcel.write(response.getOutputStream(), String.class).sheet("优惠券码").doWrite(couponCodes);
        return Response.success();
    }

    /**
     * 券发放详情
     */
    @PostMapping("/couponInfoDetail")
    public Response<CouponDetailResponse> couponInfoDetail(@Valid @RequestBody CouponInfoDetailRequest req) {
        String blocCode = req.getBlocCode();
        MemberInfoDto memberInfoDto = null;CustomerInfoResp customerInfoResp = null;
        CouponInfoResp couponInfoResp = couponDecorator.getCouponInfo(couponConverter.convertGetCouponInfoReq(req));
        if (ReceiverTypeEnum.MEMBER.getCode().equals(couponInfoResp.getReceiverType())) {
            memberInfoDto = memberMedService.getByMemberNo(MasterTypeEnum.BLOC.getType(),blocCode, couponInfoResp.getReceiverCode());
        }
        if (ReceiverTypeEnum.CUSTOMER.getCode().equals(couponInfoResp.getReceiverType())) {
            GetByCustomerNoReq getByCustomerNoReq = couponConverter.convertGetByCustomerNoReq(
                    blocCode, req.getTrackingId(), couponInfoResp.getCustomerHotelCode(),couponInfoResp.getReceiverCode());
            customerInfoResp = customerDecorator.getByCustomerNo(getByCustomerNoReq);
        }
        return Response.success(couponConverter.buildCouponDetailResponse(couponInfoResp, memberInfoDto, customerInfoResp));
    }

    /**
     * 具体优惠券作废
     */
    @PostMapping("/invalidCoupon")
    public Response<Void> invalidCoupon(@Valid @RequestBody CouponInfoDetailRequest req) {
        couponDecorator.invalidCoupon(couponConverter.convertGetByCouponCodeReq(req));
        return Response.success();
    }

    /**
     * 具体优惠券删除
     */
    @PostMapping("/deleteCoupon")
    public Response<Void> deleteCoupon(@Valid @RequestBody CouponInfoDetailRequest req) {
        couponDecorator.deleteCoupon(couponConverter.convertGetByCouponCodeReq(req));
        return Response.success();
    }


    /**
     * 优惠券发放批次分页列表查询
     */
    @PostMapping("/grantBatchPageInfos")
    public Response<Pageable<CouponBatchListResponse>> grantBatchPageInfos(@Valid @RequestBody PageCouponBatchRequest req) {
        QueryCouponBatchListReq queryCouponBatchListReq = couponConverter.convertPageBatchRequest2ApiReq(req);
        Pageable<CouponBatchListResp> pageable = couponDecorator.batchPageInfos(queryCouponBatchListReq);
        if (CollectionUtils.isEmpty(pageable.getDatas())) {
            return Response.success(Pageable.empty());
        }
        List<String> hotelCodes = pageable.getDatas().stream().map(CouponBatchListResp::getHotelCode)
                .filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        List<HotelBaseInfoResp> hotelBaseInfos = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(hotelCodes)) {
            hotelBaseInfos = hotelDecorator.listHotelBaseInfos(req.getBlocCode(), hotelCodes);
        }
        Map<String, HotelBaseInfoResp> hotelBaseInfoMap = hotelBaseInfos.stream().collect(
                Collectors.toMap(HotelBaseInfoResp::getHotelCode, Function.identity(), (v1, v2) -> v1));
        return Response.success(PageableUtil.convert(pageable, d -> couponConverter.buildCouponBatchListResp2Response(d,hotelBaseInfoMap)));
    }

    /**
     * 优惠券某发放批次详细列表分页查询
     */
    @PostMapping("/grantBatchDetailPageInfos")
    public Response<Pageable<CouponBatchDetailResponse>> pageGrantBatchDetailInfos(@Valid @RequestBody PageCouponBatchDetailsRequest req) {
        return Response.success(this.pageGrantBatchDetail(req));
    }

    /**
     * 优惠券某发放批次详细列表导出
     */
    @SneakyThrows
    @PostMapping("/exportBatchDetail")
    public Response<Void> exportBatchDetail(@Valid @RequestBody PageCouponBatchDetailsRequest req, HttpServletResponse response) {
        req.setPageSize(Integer.MAX_VALUE);
        req.setBlocCode(UserInfoThreadHolder.getUser().getBlocCode());
        Pageable<CouponBatchDetailResponse> pageable = this.pageGrantBatchDetail(req);
        log.info("优惠券发放批次详细列表导出:{}", JSON.toJSONString(pageable.getDatas()));
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode("发放详情", "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
        response.setHeader("Access-Control-Expose-Headers", "Content-Disposition,x-access-titc-c-token");
        List<CouponExportSimpleResponse> data = couponConverter.convertCouponExportSimpleResponse(pageable.getDatas());
        EasyExcel.write(response.getOutputStream(),CouponExportSimpleResponse.class).sheet("发放详情").doWrite(data);
        return Response.success();
    }

    /**
     * 券批次作废
     */
    @PostMapping("/invalidBatch")
    public Response<Void> invalidBatch(@Valid @RequestBody CouponBatchInfoRequest req) {
        couponDecorator.invalidBatch(couponConverter.convertGetByCouponBatchCodeReq(req));
        return Response.success();
    }


    /**
     * 券批次删除
     */
    @PostMapping("/deleteBatch")
    public Response<Void> deleteBatch(@Valid @RequestBody CouponBatchInfoRequest req) {
        couponDecorator.deleteBatch(couponConverter.convertGetByCouponBatchCodeReq(req));
        return Response.success();
    }


    private CouponReceiverInfoResponse getInfoByMemberNo(GetGrantReceiverRequest request) {
        String masterCode = request.getBlocCode();
        Integer masterType = MasterTypeEnum.BLOC.getType();
        MemberInfoDto memberInfoDto = memberMedService.getByMemberNo(masterType, masterCode, request.getMemberNo());
        return couponConverter.convertCouponReceiverInfoResponseByMemberInfoDto(memberInfoDto);
    }

    private CouponReceiverInfoResponse getInfoByCustomerNo(GetGrantReceiverRequest request) {
        GetByCustomerNoReq req = couponConverter.convertGetCustomerNoReqByGetGrantReceiverRequest(request);
        CustomerInfoResp customerInfoResp = customerDecorator.getByCustomerNo(req);
        return couponConverter.convertCouponReceiverInfoResponseByCustomerInfoResp(customerInfoResp);
    }


    private CouponReceiverInfoResponse getInfoByMobile(GetGrantReceiverRequest request) {
        String masterCode = request.getBlocCode();
        Integer masterType = MasterTypeEnum.BLOC.getType();
        List<String> mobiles = Collections.singletonList(request.getMobile());
        List<MemberDetailDto> memberDetails = memberMedService.listByMobiles(masterType, masterCode, mobiles, MemberStateEnum.VALID.getState());
        if (CollectionUtils.isEmpty(memberDetails)) {
            return null;
        }
        MemberDetailDto memberDetailDto = CollUtil.getFirst(memberDetails.listIterator());
        return couponConverter.convertCouponReceiverInfoResponseByMemberDetailDto(memberDetailDto);
    }

    private CouponReceiverInfoResponse getInfoByIdCard(GetGrantReceiverRequest request) {
        String masterCode = request.getBlocCode();
        Integer masterType = MasterTypeEnum.BLOC.getType();
        List<String> idCards = Collections.singletonList(request.getIdCard());
        Integer idType = IdTypeEnum.IDENTITY_CARD.getType();
        List<MemberDetailDto> memberDetails = memberMedService.listByIdNos(masterType, masterCode, idType, idCards, null);
        if (CollectionUtils.isEmpty(memberDetails)) {
            return null;
        }
        MemberDetailDto memberDetailDto = CollUtil.getFirst(memberDetails.listIterator());
        return couponConverter.convertCouponReceiverInfoResponseByMemberDetailDto(memberDetailDto);
    }

    private Pageable<CouponListResponse> pageCoupon(PageCouponRequest req) {
        Pageable<CouponListResp> pageable = couponDecorator.pageInfos(couponConverter.convertGetCouponListReq(req));
        if (CollectionUtils.isEmpty(pageable.getDatas())) {
            return Pageable.empty();
        }
        List<String> memberNos = new ArrayList<>();
        List<String> customerNos = new ArrayList<>();
        pageable.getDatas().forEach(item -> {
            if (ReceiverTypeEnum.MEMBER.getCode().equals(item.getReceiverType())) {
                memberNos.add(item.getReceiverCode());
            }
            if (ReceiverTypeEnum.CUSTOMER.getCode().equals(item.getReceiverType())) {
                customerNos.add(item.getReceiverCode());
            }
        });
        List<MemberDetailDto> memberDetails = new ArrayList<>();
        List<CustomerInfoResp> customerInfos = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(memberNos)) {
            memberDetails = memberMedService.listByMemberNos(MasterTypeEnum.BLOC.getType(), req.getBlocCode(), memberNos);
        }
        if (CollectionUtils.isNotEmpty(customerNos)) {
            customerInfos = customerDecorator.listByCustomerNos(req.getBlocCode(), customerNos);
        }

        Map<String, MemberDetailDto> memberDetailMap = memberDetails.stream().collect(Collectors.toMap(MemberDetailDto::getMemberNo, Function.identity(), (v1, v2) -> v1));
        Map<String, CustomerInfoResp> customerInfoMap = customerInfos.stream().collect(Collectors.toMap(CustomerInfoResp::getCustomerNo, Function.identity(), (v1, v2) -> v1));
        return PageableUtil.convert(pageable, d -> couponConverter.buildCouponListResponse(d, memberDetailMap, customerInfoMap));
    }

    private Pageable<CouponBatchDetailResponse> pageGrantBatchDetail(PageCouponBatchDetailsRequest req) {
        QueryCouponBatchDetailsReq queryCouponBatchDetailsReq = couponConverter.convertPageBatchDetailsRequest2ApiReq(req);
        Pageable<CouponBatchDetailResp> pageable = couponDecorator.pageCouponBatchDetails(queryCouponBatchDetailsReq);
        if (CollectionUtils.isEmpty(pageable.getDatas())) {
            return Pageable.empty();
        }
        List<String> memberNos = new ArrayList<>();
        List<String> customerNos = new ArrayList<>();
        List<String> hotelCodes = new ArrayList<>();
        pageable.getDatas().forEach(item -> {
            if (Objects.equals(item.getMasterType(), MasterTypeEnum.HOTEL.getType())) {
                hotelCodes.add(item.getMasterCode());
            }
            if (ReceiverTypeEnum.MEMBER.getCode().equals(item.getReceiverType())) {
                memberNos.add(item.getReceiverCode());
            }
            if (ReceiverTypeEnum.CUSTOMER.getCode().equals(item.getReceiverType())) {
                customerNos.add(item.getReceiverCode());
            }
        });
        List<MemberDetailDto> memberDetails = new ArrayList<>();
        List<CustomerInfoResp> customerInfos = new ArrayList<>();
        List<HotelBaseInfoResp> hotelBaseInfos = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(memberNos)) {
            memberDetails = memberMedService.listByMemberNos(MasterTypeEnum.BLOC.getType(), req.getBlocCode(), memberNos);
        }
        if (CollectionUtils.isNotEmpty(customerNos)) {
            customerInfos = customerDecorator.listByCustomerNos(req.getBlocCode(), customerNos);
        }
        if (CollectionUtils.isNotEmpty(hotelCodes)) {
            hotelBaseInfos = hotelDecorator.listHotelBaseInfos(req.getBlocCode(), hotelCodes);
        }
        Map<String, MemberDetailDto> memberDetailMap = memberDetails.stream().collect(Collectors.toMap(MemberDetailDto::getMemberNo, Function.identity(), (v1, v2) -> v1));
        Map<String, CustomerInfoResp> customerInfoMap = customerInfos.stream().collect(Collectors.toMap(CustomerInfoResp::getCustomerNo, Function.identity(), (v1, v2) -> v1));
        Map<String, HotelBaseInfoResp> hotelBaseInfoMap = hotelBaseInfos.stream().collect(Collectors.toMap(HotelBaseInfoResp::getHotelCode, Function.identity(), (v1, v2) -> v1));
        return PageableUtil.convert(pageable, couponBatchDetailResp ->
                couponConverter.buildCouponBatchDetailResponse(couponBatchDetailResp, memberDetailMap, customerInfoMap, hotelBaseInfoMap));
    }

    private void batchGrantCoupon(String templateCode, Integer version,
                                  Integer grantNum, Integer crossShop,
                                  String hotelCode, Boolean toSmsNotify,
                                  List<GrantCoupon> members) {
        UserInfoDto user = UserInfoThreadHolder.getUser();
        List<String> memberNos = new ArrayList<>();
        List<String> mobileNos = new ArrayList<>();
        members.forEach(memberDto -> {
            if (StringUtils.isNotBlank(memberDto.getMemberNo())) {
                memberNos.add(memberDto.getMemberNo());
            } else if (StringUtils.isNotBlank(memberDto.getMobile())) {
                mobileNos.add(memberDto.getMobile());
            }
        });
        if (CollectionUtils.isNotEmpty(mobileNos)) {
            memberNos.addAll(memberMedService.listByMobiles(MasterTypeEnum.BLOC.getType(), user.getBlocCode(), mobileNos,
                    MemberStateEnum.VALID.getState()).stream().map(MemberDetailDto::getMemberNo).collect(Collectors.toList()));
        }
        GrantCouponBatchReq grantCouponBatchReq = couponConverter.convertParamsToGrantCouponBatchReq(
                templateCode, version, grantNum, user.getBlocCode(), hotelCode, crossShop, toSmsNotify, memberNos, user.getUserName());
        couponDecorator.grantCouponsByBind(grantCouponBatchReq);
    }
}
