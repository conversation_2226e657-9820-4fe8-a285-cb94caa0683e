package com.ly.titc.pms.member.gateway.entity.request.spm.activity.dto;

import com.ly.titc.pms.spm.dubbo.entity.dto.rule.use.applicable.ApplicableHotelsDto;
import com.ly.titc.pms.spm.dubbo.entity.dto.rule.use.applicable.CommonInfoDto;
import lombok.Data;

import java.util.List;

/**
 * @Description:
 * @Author: huaizhu.zhong
 * @Date: 2025-3-12
 */
@Data
public class EventActivityExtendDto {

    /**
     * 累计统计范围(永久forever，一年内one_year)
     */
    private String cumulativeTimeRange;

    /**
     * 是否累计计算赠送金额 0-不计算 1-计算
     */
    private Integer calcGiftAmount;

    /**
     * 储值规则
     */
    private StoredValueRuleDto storedValueRule;

    /**
     * 适用酒店
     */
    private ApplicableHotelsDto applicableHotelsDto;

    /**
     * 适用渠道信息
     */
    private List<CommonInfoDto> applicablePlatformChannels;

    /**
     * 适用会员等级
     */
    private ApplicableMemberLevelDto applicableMemberLevel;

    /**
     * 适用用户类型 customer-客户 MEMBER-会员
     */
    private List<String> applicableUserTypes;

    /**
     * 适用租住模式 0日历房 1钟点房 2-公寓房
     */
    private List<Integer> applicableRentModes;

    /**
     * 可叠加活动类型(展示类型)
     */
    private List<String> stackableActivityTypeList;
}
