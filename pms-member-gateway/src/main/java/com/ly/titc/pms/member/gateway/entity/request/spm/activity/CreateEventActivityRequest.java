package com.ly.titc.pms.member.gateway.entity.request.spm.activity;

import com.ly.titc.common.annotation.LegalEnum;
import com.ly.titc.pms.member.gateway.entity.request.BaseRequest;
import com.ly.titc.pms.member.gateway.entity.request.spm.activity.dto.EventActivityExtendDto;
import com.ly.titc.pms.member.gateway.entity.request.spm.activity.dto.EventActivityGearDto;
import com.ly.titc.pms.spm.dubbo.enums.SubActivityTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class CreateEventActivityRequest extends BaseRequest {
    /**
     * 活动名称
     */
    @NotBlank(message = "活动名称不能为空")
    private String name;

    /**
     * 活动描述
     */
    private String desc;

    /**
     * 活动展示类型
     */
    @NotBlank(message = "活动类型不能为空")
    @LegalEnum(target = SubActivityTypeEnum.class, message = "活动类型不合法",methodName = "getCode")
    private String displayType;

    /**
     * 活动生效时间
     */
    private String effectiveStartTime;

    /**
     * 活动失效时间
     */
    private String effectiveEndTime;

    /**
     * 是否长期有效
     */
    private Boolean isLongTerm;

    /**
     * 档位
     */
    @NotEmpty(message = "档位不能为空")
    private List<EventActivityGearDto> carrierDtoList;

    /**
     * 事件活动扩展信息
     */
    @NotNull
    private EventActivityExtendDto extendDto;
}
