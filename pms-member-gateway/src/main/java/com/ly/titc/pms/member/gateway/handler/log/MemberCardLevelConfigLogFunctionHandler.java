package com.ly.titc.pms.member.gateway.handler.log;

import com.alibaba.excel.util.StringUtils;
import com.alibaba.fastjson.JSONObject;
import com.ly.titc.pms.ecrm.dubbo.entity.response.member.MemberCardConfigResp;
import com.ly.titc.pms.ecrm.dubbo.entity.response.member.MemberCardLevelConfigInfoResp;
import com.ly.titc.pms.ecrm.dubbo.entity.response.member.MemberCardPrivilegeConfigResp;
import com.ly.titc.pms.member.com.annotation.LogRecordFunction;
import com.ly.titc.pms.member.com.lang.TwoTuple;
import com.ly.titc.pms.member.com.utils.ConfigCenterUtil;
import com.ly.titc.pms.member.dubbo.enums.MasterTypeEnum;
import com.ly.titc.pms.member.gateway.entity.request.member.card.ActionCardLevelRequest;
import com.ly.titc.pms.member.gateway.entity.request.member.card.DeleteMemberCardLevelConfigRequest;
import com.ly.titc.pms.member.gateway.entity.request.member.card.MemberCardPrivilegeConfigInfoRequest;
import com.ly.titc.pms.member.gateway.entity.request.member.card.SaveCardLevelConfigInfoRequest;
import com.ly.titc.pms.member.mediator.rpc.dubbo.ecrm.MemberCardInfoDecorator;
import com.ly.titc.pms.member.mediator.rpc.dubbo.ecrm.MemberPrivilegeDecorator;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author：rui
 * @name：MemberCardConfigLogFunctionHandler
 * @Date：2024-11-15 11:47
 * @Filename：MemberCardConfigLogFunctionHandler
 */
@LogRecordFunction("memberLevelConfig")
public class MemberCardLevelConfigLogFunctionHandler extends AbstractModuleServiceHandler implements ApplicationContextAware {

    private static MemberCardInfoDecorator decorator;

    private static MemberPrivilegeDecorator privilegeDecorator;

    @Override
    public List<String> execute(String blocCode, String hotelCode, String name, String trackingId) {
        return null;
    }

    @Override
    public Integer getModuleId() {
        return null;
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        decorator = applicationContext.getBean(MemberCardInfoDecorator.class);
        privilegeDecorator = applicationContext.getBean(MemberPrivilegeDecorator.class);
    }

    @LogRecordFunction("addMemberCardLevel")
    public static String addMemberCardLevel(SaveCardLevelConfigInfoRequest request) {
        MemberCardConfigResp cardConfigResp = decorator.getById(request.getCardId());
        String name = request.getCardLevelName();
        JSONObject json = new JSONObject();
        json.put("name", String.format("%s-%s", cardConfigResp.getCardName(), request.getCardLevelName()));
        json.put("blocCode", request.getBlocCode());
        List<String> records = new ArrayList<>();
        records.add(String.format("新增【%s】", name));
        json.put("records", records);
        return json.toJSONString();
    }

    @LogRecordFunction("updateMemberCardLevel")
    public static String updateMemberCardLevel(SaveCardLevelConfigInfoRequest request) {
        MemberCardConfigResp cardConfigResp = decorator.getById(request.getCardId());
        String name = request.getCardLevelName();
        JSONObject json = new JSONObject();
        json.put("name", String.format("%s-%s", cardConfigResp.getCardName(), request.getCardLevelName()));
        json.put("blocCode", request.getBlocCode());
        List<String> records = new ArrayList<>();
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(String.format("修改【%s】", name));
        MemberCardLevelConfigInfoResp resp = decorator.getCardLevelConfig(Long.valueOf(request.getId()));
        if (!resp.getCardId().equals(request.getCardId())) {
            TwoTuple<Integer, String> masterTuple = ConfigCenterUtil.getMasterByBloc(request.getBlocCode());
            List<MemberCardConfigResp> cardConfigRespList = decorator.listCardConfig(masterTuple.getFirst(), request.getBlocCode());
            Map<Long, String> map = cardConfigRespList.stream().collect(Collectors.toMap(MemberCardConfigResp::getId, MemberCardConfigResp::getCardName));
            stringBuilder.append(String.format("【会员卡】%s->%s", map.get(resp.getCardId()), map.get(request.getCardId())));
        }
        if (request.getCardLevel() != null && !request.getCardLevel().equals(resp.getCardLevel())) {
            stringBuilder.append(String.format("【会员等级】%s->%s", resp.getCardLevel(), request.getCardLevel()));
        }
        if (!StringUtils.equals(request.getCardLevelName(), resp.getCardLevelName())) {
            stringBuilder.append(String.format("【会员等级名称】%s->%s", resp.getCardLevelName(), request.getCardLevelName()));
        }
        if (request.getValidPeriod() != null && !request.getValidPeriod().equals(resp.getValidPeriod())) {
            stringBuilder.append(String.format("【有限期】%s->%s", resp.getValidPeriod(), request.getValidPeriod()));
        }
        if (request.getIsLongTerm() != null && !request.getIsLongTerm().equals(resp.getIsLongTerm())) {
            stringBuilder.append(String.format("【长期有效】%s->%s", resp.getIsLongTerm() == 0 ? "否" : "是", request.getIsLongTerm() == 0 ? "否" : "是"));
        }
        // 查询等级关联的权益
        List<MemberCardPrivilegeConfigResp> cardConfigRespList = privilegeDecorator.listMemberCardPrivilegeLevel(request.getCardId(), resp.getCardLevel());
        Map<Long, MemberCardPrivilegeConfigResp> originalMap = cardConfigRespList.stream().collect(Collectors.toMap(MemberCardPrivilegeConfigResp::getPrivilegeId, Function.identity()));
        Map<Long, MemberCardPrivilegeConfigInfoRequest> map = request.getRelationList().stream().collect(Collectors.toMap(MemberCardPrivilegeConfigInfoRequest::getPrivilegeId, Function.identity()));
        if (!originalMap.keySet().equals(map.keySet())) {
            // 新增的
            Set<Long> add = map.keySet().stream().filter(id -> !originalMap.containsKey(id)).collect(Collectors.toSet());
            if (CollectionUtils.isNotEmpty(add)) {
                stringBuilder.append(String.format("【新增权益】%s", add.stream().map(item -> map.get(item).getName()).collect(Collectors.joining("、"))));
            }
            // 删除的
            Set<Long> delete = originalMap.keySet().stream().filter(id -> !map.containsKey(id)).collect(Collectors.toSet());
            if (CollectionUtils.isNotEmpty(delete)) {
                stringBuilder.append(String.format("【删除权益】%s", delete.stream().map(item -> originalMap.get(item).getName()).collect(Collectors.joining("、"))));
            }
            // 编辑的
            Set<Long> edit = map.keySet().stream().filter(originalMap::containsKey).collect(Collectors.toSet());
            if (CollectionUtils.isNotEmpty(edit)) {
                List<String> s = new ArrayList<>();
                edit.forEach(id -> {
                    Integer originalSort = originalMap.get(id).getSort();
                    Integer sort = map.get(id).getSort();
                    if (!originalSort.equals(sort)) {
                        s.add(String.format("%s排序%s->%s", originalMap.get(id).getName(), originalSort, sort));
                    }
                });
                if (CollectionUtils.isNotEmpty(s)) {
                    stringBuilder.append(String.format("【修改权益】%s", String.join("；", s)));

                }
            }
        }
        records.add(stringBuilder.toString());
        json.put("records", records);
        return json.toJSONString();
    }

    @LogRecordFunction("deleteMemberCardLevel")
    public static String deleteMemberCardLevel(DeleteMemberCardLevelConfigRequest request) {
        MemberCardConfigResp cardConfigResp = decorator.getById(Long.parseLong(request.getCardId()));
        String name = request.getCardLevelName();
        JSONObject json = new JSONObject();
        json.put("name", String.format("%s-%s", cardConfigResp.getCardName(), request.getCardLevelName()));
        json.put("blocCode", request.getBlocCode());
        List<String> records = new ArrayList<>();
        records.add(String.format("删除【%s】", name));
        json.put("records", records);
        return json.toJSONString();
    }

    @LogRecordFunction("enableMemberCardLevel")
    public static String enableMemberCardLevel(ActionCardLevelRequest request) {
        MemberCardConfigResp cardConfigResp = decorator.getById(Long.parseLong(request.getCardId()));
        String name = request.getCardLevelName();
        JSONObject json = new JSONObject();
        json.put("name", String.format("%s-%s", cardConfigResp.getCardName(), request.getCardLevelName()));
        json.put("blocCode", request.getBlocCode());
        List<String> records = new ArrayList<>();
        records.add(String.format("启用【%s】", name));
        json.put("records", records);
        return json.toJSONString();
    }

    @LogRecordFunction("stopMemberCardLevel")
    public static String stopMemberCardLevel(ActionCardLevelRequest request) {
        MemberCardConfigResp cardConfigResp = decorator.getById(Long.parseLong(request.getCardId()));
        String name = request.getCardLevelName();
        JSONObject json = new JSONObject();
        json.put("name", String.format("%s-%s", cardConfigResp.getCardName(), request.getCardLevelName()));
        json.put("blocCode", request.getBlocCode());
        List<String> records = new ArrayList<>();
        records.add(String.format("停用【%s】", name));
        json.put("records", records);
        return json.toJSONString();
    }

}
