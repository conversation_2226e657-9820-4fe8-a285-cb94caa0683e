package com.ly.titc.pms.member.gateway.converter;

import com.ly.titc.common.enums.PlatformChannelEnum;
import com.ly.titc.pms.ecrm.dubbo.entity.BaseMasterReq;
import com.ly.titc.pms.member.asset.dubbo.entity.request.points.PageMemberPointsFlowMemberReq;
import com.ly.titc.pms.member.asset.dubbo.entity.request.stored.*;
import com.ly.titc.pms.member.asset.dubbo.entity.response.MemberPointsFlowInfoResp;
import com.ly.titc.pms.member.asset.dubbo.entity.response.point.MemberTotalPointResp;
import com.ly.titc.pms.member.asset.dubbo.entity.response.store.MemberStoreConsumeCalResp;
import com.ly.titc.pms.member.asset.dubbo.entity.response.store.MemberStoreConsumeRecordResp;
import com.ly.titc.pms.member.asset.dubbo.entity.response.store.MemberTotalAmountResp;
import com.ly.titc.pms.member.asset.dubbo.enums.AssetBusinessTypeEnum;
import com.ly.titc.pms.member.asset.dubbo.enums.AssetConsumeTypeEnum;
import com.ly.titc.pms.member.asset.dubbo.enums.PointActionTypeEnum;
import com.ly.titc.pms.member.com.constant.SystemConstant;
import com.ly.titc.pms.member.com.utils.GenerateUtils;
import com.ly.titc.pms.member.dubbo.enums.MasterTypeEnum;
import com.ly.titc.pms.member.gateway.entity.request.asset.PageMemberPointConsumeRecordRequest;
import com.ly.titc.pms.member.gateway.entity.request.asset.PageMemberStoreConsumeRequest;
import com.ly.titc.pms.member.gateway.entity.request.asset.point.MemberPointAdjustRequest;
import com.ly.titc.pms.member.gateway.entity.request.asset.store.MemberStoreConsumeCalRequest;
import com.ly.titc.pms.member.gateway.entity.request.asset.store.MemberStoreConsumeRequest;
import com.ly.titc.pms.member.gateway.entity.request.asset.store.MemberStoreFreezeRequest;
import com.ly.titc.pms.member.gateway.entity.request.asset.store.MemberStoreUnFreezeRequest;
import com.ly.titc.pms.member.gateway.entity.response.asset.*;
import com.ly.titc.pms.member.mediator.entity.dto.asset.ConsumeMemberPointDto;
import com.ly.titc.pms.member.mediator.entity.dto.asset.ReceiveMemberPointDto;
import com.ly.titc.pms.member.mediator.entity.dto.order.MemberRechargeOrderDetailDto;
import com.ly.titc.pms.member.mediator.entity.dto.order.MemberRechargeOrderDto;
import com.ly.titc.pms.member.mediator.entity.dto.order.PageOrderQueryDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

/**
 * @Author：rui
 * @name：AssetConverter
 * @Date：2024-12-9 16:17
 * @Filename：AssetConverter
 */
@Mapper(componentModel = "spring")
public interface AssetConverter {


    MemberStoreConsumeRecordResponse convertMemberStoreConsumeRecord(MemberStoreConsumeRecordResp resp);

    MemberOrderRechargeDetailResponse convert(MemberRechargeOrderDetailDto dto);

    MemberOrderRechargeResponse convertMemberOrderRecharge(MemberRechargeOrderDto info);

    MemberPointConsumeRecordResponse convertMemberPointConsumeRecord(MemberPointsFlowInfoResp resp);

    List<MemberPointConsumeRecordResponse> convertMemberPointConsumeRecord(List<MemberPointsFlowInfoResp> resps);

    MemberTotalPointResponse convertMemberTotalPoint(MemberTotalPointResp resp);

    MemberTotalAmountResponse convertMemberTotalAmount(MemberTotalAmountResp resp);

    default ReceiveMemberPointDto convert(MemberPointAdjustRequest request) {
        ReceiveMemberPointDto dto = new ReceiveMemberPointDto();
        dto.setTrackingId(request.getTrackingId());
        dto.setMasterType(MasterTypeEnum.BLOC.getType());
        dto.setMasterCode(request.getBlocCode());
        dto.setBlocCode(request.getBlocCode());
        dto.setMemberNo(request.getMemberNo());
        dto.setPlatformChannel(PlatformChannelEnum.CRM.getPlatformChannel());
        dto.setBusinessType(AssetBusinessTypeEnum.MEMBER.getType());
        dto.setBusinessNo(GenerateUtils.generate(SystemConstant.MEMBER_NO_PREFIX));
        dto.setActionType(PointActionTypeEnum.ADJUST.getType());
        dto.setActionItem(request.getActionItem());
        dto.setActionItemDesc(request.getActionItemDesc());
        dto.setScore(request.getScore());
        dto.setRemark(request.getRemark());
        dto.setOperator(request.getOperator());
        return dto;
    }

    default BaseMasterReq convertReq(MemberPointAdjustRequest request) {
        BaseMasterReq req = new BaseMasterReq();
        req.setMasterType(MasterTypeEnum.BLOC.getType());
        req.setMasterCode(request.getBlocCode());
        req.setTrackingId(request.getTrackingId());
        return req;
    }

    default ConsumeMemberPointDto convertConsumeReq(MemberPointAdjustRequest request) {
        ConsumeMemberPointDto pointDto = new ConsumeMemberPointDto();
        pointDto.setMemberNo(request.getMemberNo());
        pointDto.setTrackingId(request.getTrackingId());
        pointDto.setMasterType(MasterTypeEnum.BLOC.getType());
        pointDto.setMasterCode(request.getBlocCode());
        pointDto.setBlocCode(request.getBlocCode());
        pointDto.setPlatformChannel(PlatformChannelEnum.CRM.getPlatformChannel());
        pointDto.setBusinessType(AssetBusinessTypeEnum.MEMBER.getType());
        pointDto.setBusinessNo(GenerateUtils.generate(SystemConstant.MEMBER_NO_PREFIX));
        pointDto.setConsumeType(AssetConsumeTypeEnum.ADJUST.getType());
        pointDto.setConsumeDesc(request.getActionItemDesc() + request.getRemark());
        pointDto.setActionItem(request.getActionItem());
        pointDto.setActionItemDesc(request.getActionItemDesc());
        pointDto.setScore(Math.abs(request.getScore()));
        pointDto.setScene("");
        pointDto.setRemark(request.getRemark());
        pointDto.setOperator(request.getOperator());
        return pointDto;
    }

    default MemberStoreConsumeReq convert(MemberStoreConsumeRequest request) {
        MemberStoreConsumeReq req = convertBase(request);
        convert(req, request.getGoodsDes());
        return req;
    }

    MemberStoreConsumeReq convertBase(MemberStoreConsumeRequest request);

    default MemberStoreConsumePreCalReq convert(MemberStoreConsumeCalRequest request) {
        MemberStoreConsumePreCalReq req = new MemberStoreConsumePreCalReq();
        req.setMemberNo(request.getMemberNo());
        req.setMasterType(MasterTypeEnum.BLOC.getType());
        req.setMasterCode(request.getBlocCode());
        req.setBlocCode(request.getBlocCode());
        req.setMemberNo(request.getMemberNo());
        req.setPlatformChannel(PlatformChannelEnum.CRM.getPlatformChannel());
        req.setCashierScene(request.getCashierScene());
        return req;
    }

    default MemberStoreFreezeReq convert(MemberStoreFreezeRequest request) {
        MemberStoreFreezeReq req = convertBase(request);
        req.setMasterType(MasterTypeEnum.BLOC.getType());
        req.setMasterCode(request.getBlocCode());
        req.setBlocCode(request.getBlocCode());
        req.setMemberNo(request.getMemberNo());
        req.setPlatformChannel(PlatformChannelEnum.CRM.getPlatformChannel());
        req.setConsumeType(AssetConsumeTypeEnum.FREEZE.getType());
        req.setSourceSystem(AssetBusinessTypeEnum.MEMBER.getType());
        req.setBizConsumeNo(GenerateUtils.generate(SystemConstant.MEMBER_NO_PREFIX));
        return req;
    }
    MemberStoreFreezeReq convertBase(MemberStoreFreezeRequest request);

    MemberStoreConsumeReq convertBase(MemberStoreConsumeCalRequest request);

    UnfreezeConsumeRecordNoReq convert(MemberStoreUnFreezeRequest request);

    default MemberStoreConsumeReq convert(MemberStoreConsumeReq req, String goodDes) {
        req.setConsumeDesc(goodDes);
        req.setMasterType(MasterTypeEnum.BLOC.getType());
        req.setMasterCode(req.getBlocCode());
        req.setConsumeType(AssetConsumeTypeEnum.PAY.getType());
        req.setSourceSystem(AssetBusinessTypeEnum.MEMBER.getType());
        req.setBizConsumeNo(GenerateUtils.generate(SystemConstant.MEMBER_NO_PREFIX));
        req.setPlatformChannel(PlatformChannelEnum.CRM.getPlatformChannel());
        return req;
    }

    MemberStoreConsumeCalResponse convert(MemberStoreConsumeCalResp calResp);


    PageMemberStoreConsumeMemberReq convert(PageMemberStoreConsumeRequest request);

    PageOrderQueryDto convertDto(PageMemberStoreConsumeRequest request);

    PageMemberPointsFlowMemberReq convert(PageMemberPointConsumeRecordRequest request);
}
