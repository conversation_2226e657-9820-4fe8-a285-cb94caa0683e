package com.ly.titc.pms.member.gateway.entity.request.spm.coupon.template.dto;

import com.ly.titc.pms.member.mediator.entity.dto.spm.acitvity.ApplicableHotelsDto;
import com.ly.titc.pms.spm.dubbo.entity.dto.rule.use.applicable.ApplicableBlocRatePlanInfoDto;
import com.ly.titc.pms.spm.dubbo.entity.dto.rule.use.applicable.ApplicableUsersRuleDto;
import com.ly.titc.pms.spm.dubbo.entity.dto.rule.use.applicable.CommonInfoDto;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Description:
 * @Author: huaizhu.zhong
 * @Date: 2024-11-5
 */
@Data
public class TemplateUseRuleDto {

    /**
     * 当晚房费最低价格 0-不限
     */
    private BigDecimal roomMinPrice = BigDecimal.ZERO;

    /**
     * 订单房费最低价格 0-不限
     */
    private BigDecimal orderMinPrice = BigDecimal.ZERO;

    /**
     * 提前预订天数 0-不限
     */
    private Integer aheadReserveDay = 0;

    /**
     * 是否叠加 false-不叠加 true-叠加
     */
    private Boolean isStacking = false;

    /**
     * 不适用星期
     */
    private List<String> unApplicableWeekInfos;

    /**
     * 适用时间段
     */
    private List<TimeBucketDto> unApplicableTimeBucketInfos;

    /**
     * 适用渠道信息
     */
    private List<CommonInfoDto> applicablePlatformChannels;

    /**
     * 适用酒店范围
     */
    private ApplicableHotelsDto applicableHotelsDto;

    /**
     * 适用会员范围
     */
    private ApplicableUsersRuleDto applicableUsersDto;

    /*
     * 适用集团价格方案
     */
    private List<ApplicableBlocRatePlanInfoDto> blocApplicableRatePlanInfos;

    /*
     * 适用酒店价格方案
     */
    private ApplicableHotelRatePlanDto hotelApplicableRatePlanInfos;

    /*
     * 适用集团房型
     */
    private List<CommonInfoDto> blocApplicableRoomTypeInfos;

    /*
     * 适用酒店房型
     */
    private ApplicableHotelRoomTypeDto hotelApplicableRoomTypeInfos;

}
