package com.ly.titc.pms.member.gateway.entity.request.spm.giftpack;


import com.ly.titc.pms.member.gateway.entity.request.PageBaseRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * @Description:
 * @Author: huaizhu.zhong
 * @Date: 2024-11-22
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SaveGiftPackInfoRequest extends PageBaseRequest {

    /**
     * 礼包编码
     */
    private String giftPackNo;

    /**
     * 礼包名称
     */
    @NotBlank(message = "礼包名称不能为空")
    private String giftPackName;

    /**
     * 礼包金额
     */
    @NotNull(message = "礼包金额不能为空")
    private BigDecimal amount;

    /**
     * 是否允许售卖  0-不允许 1-允许
     */
    @NotNull(message = "是否允许售卖不能为空")
    private Integer isAllowSale;

    /**
     * 礼包开始时间
     */
    @NotBlank(message = "礼包有效开始时间不能为空")
    private String effectStartDate;

    /**
     * 礼包结束时间
     */
    @NotBlank(message = "礼包有效结束时间不能为空")
    private String effectEndDate;

    /**
     * 礼包描述
     */
    private String content;

    /**
     * 促销信息
     */
    private String promotionContent;

    /**
     * 备注
     */
    private String remark;

    /**
     * 礼包可售数量 -1表示不限
     */
    private Integer availableQuantity;

    /**
     * 礼包售卖信息
     */
    private GiftPackSaleInfoDto saleInfo;

    /**
     * 礼包明细集合
     */
    @Valid
    @NotEmpty(message = "礼包明细不能为空")
    private List<GiftPackItemInfoDto> itemInfos;
}
