package com.ly.titc.pms.member.gateway.entity.request.spm.activity.dto;

import com.ly.titc.common.annotation.LegalEnum;
import com.ly.titc.pms.spm.dubbo.entity.dto.activity.BearRatioDto;
import com.ly.titc.pms.spm.dubbo.enums.CostBearTypeEnum;
import lombok.Data;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024-12-13 14:20
 */
@Data
public class CostBearInfoDto {

    /**
     * 成本承担类型
     * {@link CostBearTypeEnum}
     */
    @LegalEnum(target = CostBearTypeEnum.class, message = "成本承担类型不合法",methodName = "getCode")
    private Integer costBearType;

    /**
     * 承担比例
     */
    private BearRatioDto bearRatioDto;
}
