package com.ly.titc.pms.member.gateway.entity.request.spm.activity;

import com.ly.titc.common.annotation.LegalEnum;
import com.ly.titc.pms.member.gateway.entity.request.BaseRequest;
import com.ly.titc.pms.member.gateway.entity.request.spm.activity.dto.CostBearInfoDto;
import com.ly.titc.pms.member.mediator.entity.dto.spm.acitvity.ApplicableUsersDto;
import com.ly.titc.pms.spm.dubbo.entity.dto.activity.ApplicableExpensesDto;
import com.ly.titc.pms.spm.dubbo.entity.dto.activity.ApplicableTimeRangeDto;
import com.ly.titc.pms.spm.dubbo.entity.dto.rule.use.applicable.ApplicableHotelsDto;
import com.ly.titc.pms.spm.dubbo.entity.dto.rule.use.applicable.ApplicableUsersRuleDto;
import com.ly.titc.pms.spm.dubbo.entity.dto.rule.use.applicable.CommonInfoDto;
import com.ly.titc.pms.spm.dubbo.enums.SubActivityTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 会员活动公共入参活动
 *
 * <AUTHOR>
 * @date 2024/12/31 10:25
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MemberActivityCommonRequest extends BaseRequest {

    /**
     * 活动类型
     */
    @NotEmpty(message = "活动展示类型不能为空")
    @LegalEnum(target = SubActivityTypeEnum.class, methodName = "getCode",message = "活动展示类型不合法")
    private String displayType;

    /**
     * 活动code
     */
    private String activityCode;

    /**
     * 活动名称
     */
    @NotBlank(message = "活动名称不能为空")
    private String name;

    /**
     * 活动描述
     */
    private String desc;

    /**
     * 是否长期有效
     */
    @NotNull(message = "是否是长期有效")
    private Boolean isLongTerm;

    /**
     * 活动生效时间
     */
    private String effectiveStartTime;

    /**
     * 活动失效时间
     */
    private String effectiveEndTime;

    /**
     * 活动优先级
     */
    @NotNull(message = "活动优先级不能为空")
    private Integer priority;

    /**
     * 成本承担
     */
    private CostBearInfoDto costBearDto;

    /**
     * 适用订单渠道
     */
    private List<CommonInfoDto> applicableOrderChannels;

    /**
     * 配置的适用订单渠道 1 全部订单渠道 2 指定订单渠道
     */
    private Integer scopeOrderChannelRange;

    /**
     * 适用渠道
     */
    @NotEmpty(message = "适用渠道不能为空")
    private List<CommonInfoDto> applicablePlatformChannels;

    /**
     * 适用来源
     * CLUB :酒馆组  BLOC 集团 HOTEL 门店
     */
    @NotEmpty(message = "适用来源不能为空")
    private List<CommonInfoDto>  applicableScopeSources;

    /**
     * 适用门店
     */
    @Valid
    private ApplicableHotelsDto applicableHotelsDto;

    /**
     * 适用用户相关
     */
    @Valid
    private ApplicableUsersRuleDto applicableUsersDto;

    /**
     * 适用时段
     */
    @Valid
    private ApplicableTimeRangeDto applicableTimeRangeDto;

    /**
     * 消费项目
     */
    @Valid
    private ApplicableExpensesDto applicableExpensesDto;

}
