package com.ly.titc.pms.member.gateway.entity.response.spm.coupon.template.dto;

import com.ly.titc.pms.member.gateway.entity.response.spm.ApplicableHotelInfoResponse;
import lombok.Data;

/**
 * @Description:
 * @Author: huaizhu.zhong
 * @Date: 2024-12-10
 */
@Data
public class TemplateGrantRuleInfoDto {

    /**
     * 早餐券截止时间
     */
    private String deadline;

    /**
     * 是否仅集团发放
     */
    private Boolean isOnlyBloc;

    /**
     * 是否是适用门店发放
     */
    private Boolean isApplicableHotel;

    /**
     * 自定义酒店范围
     */
    private ApplicableHotelInfoResponse customHotelInfos;

}
