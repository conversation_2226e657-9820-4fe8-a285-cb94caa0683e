package com.ly.titc.pms.member.gateway.converter;

import com.ly.titc.pms.member.gateway.entity.request.spm.coupon.template.GetTemplateApplicableInfoRequest;
import com.ly.titc.pms.member.gateway.entity.response.spm.coupon.*;
import com.ly.titc.pms.member.gateway.entity.response.spm.coupon.template.CouponTemplateStateResponse;
import com.ly.titc.pms.spm.dubbo.entity.request.coupon.template.GetApplicableHotelReq;
import com.ly.titc.pms.spm.dubbo.entity.response.coupon.*;
import com.ly.titc.pms.spm.dubbo.entity.response.coupon.template.HotelInfoResp;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

/**
 * @Description:
 * @Author: huaizhu.zhong
 * @Date: 2024-11-12
 */
@Mapper(componentModel = "spring")
public interface CouponCommonConverter {

    CouponTypeInfoResponse convertCouponTypeInfoResponse(CouponTypeResp couponTypeInfoDto);

    List<CouponTypeInfoResponse> convertCouponTypeInfoResponses(List<CouponTypeResp> couponTypeInfos);

    CouponTemplateStateResponse convertCouponTemplateStateResponse(CouponTemplateStateResp dto);

    List<CouponTemplateStateResponse> convertCouponTemplateStateResponses(List<CouponTemplateStateResp> infos);

    CouponSourceClientResponse convertCouponSourceClientResponse(CouponSourceClientResp resp);

    List<CouponSourceClientResponse> convertCouponSourceClientResponses(List<CouponSourceClientResp> infos);

    CouponValueTypeResponse convertCouponValueTypeResponse(CouponValueTypeResp resp);

    List<CouponValueTypeResponse> convertCouponValueTypeResponses(List<CouponValueTypeResp> infos);

    CouponPostingTypeResponse convertCouponPostingTypeResponse(CouponPostingTypeResp resp);

    List<CouponPostingTypeResponse> convertCouponPostingTypeResponses(List<CouponPostingTypeResp> infos);

    CouponRedeemTypeResponse convertCouponRedeemTypeResponse(CouponRedeemTypeResp resp);

    List<CouponRedeemTypeResponse> convertCouponRedeemTypeResponses(List<CouponRedeemTypeResp> infos);

    List<CouponGrantEventResponse> convertCouponGrantEventResponses(List<CouponGrantEventResp> couponGrantEventInfos);

    @Mapping(target = "hotelRuleInfo", ignore = true)
    GetApplicableHotelReq convertGetApplicableHotelReq(GetTemplateApplicableInfoRequest request);

    List<CouponApplicableHotelResponse> convertCouponApplicableHotelResponses(List<HotelInfoResp> HotelInfos);
}
