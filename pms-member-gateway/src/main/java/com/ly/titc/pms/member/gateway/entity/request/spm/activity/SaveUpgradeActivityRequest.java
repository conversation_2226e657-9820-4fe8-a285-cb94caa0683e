package com.ly.titc.pms.member.gateway.entity.request.spm.activity;

import com.ly.titc.pms.member.gateway.entity.request.spm.activity.dto.MemberUpgradeGearsDto;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 保存升级活动
 *
 * <AUTHOR>
 * @date 2024/12/31 14:44
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SaveUpgradeActivityRequest extends MemberActivityCommonRequest {

    /**
     * 升级活动
     */
    @Valid
    @NotEmpty(message = "升级活动必填")
    private List<MemberUpgradeGearsDto> carriers;

    /**
     * 会员卡Id(售卡和升级必填)
     */
    private String cardId;

    /**
     * 会员卡名称(售卡和升级必填)
     */
    private String cardName;
}
