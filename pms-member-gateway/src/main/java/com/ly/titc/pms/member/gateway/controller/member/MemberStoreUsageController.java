package com.ly.titc.pms.member.gateway.controller.member;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.fasterxml.jackson.databind.ser.Serializers;
import com.ly.titc.cashier.dubbo.entity.request.SelectReq;
import com.ly.titc.cashier.dubbo.entity.response.SelectResp;
import com.ly.titc.cashier.dubbo.enums.CashierSceneEnum;
import com.ly.titc.common.entity.Pageable;
import com.ly.titc.common.entity.Response;
import com.ly.titc.common.enums.PlatformChannelEnum;
import com.ly.titc.common.util.PageableUtil;
import com.ly.titc.pms.ecrm.dubbo.entity.response.member.usageRule.MemberStoreUsageRuleAssetResp;
import com.ly.titc.pms.member.gateway.converter.MemberUsageConverter;
import com.ly.titc.pms.member.gateway.entity.request.BaseRequest;
import com.ly.titc.pms.member.gateway.entity.request.usage.*;
import com.ly.titc.pms.member.gateway.entity.response.usage.MemberStoreUsageRuleAssetResponse;
import com.ly.titc.pms.member.gateway.entity.response.usage.MemberStoreUsageRuleResponse;
import com.ly.titc.pms.member.gateway.entity.response.usage.MemberUsageRuleSaveResultResponse;
import com.ly.titc.pms.member.mediator.entity.dto.usage.*;
import com.ly.titc.pms.member.mediator.rpc.dubbo.cashier.PayCashierConfigDecorator;
import com.ly.titc.pms.member.mediator.rpc.dubbo.ecrm.MemberStoreUsageDecorator;
import com.ly.titc.pms.member.mediator.service.MemberStoreUsageMedService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;

/**
 * 储值规则设置
 *
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-11-15 11:54
 */
@Slf4j
@RestController
@RequestMapping("/member/storeUsage")
public class MemberStoreUsageController {
    @Resource
    private MemberStoreUsageMedService storeUsageMedService;

    @Resource
    private MemberUsageConverter usageConverter;

    @Resource
    private PayCashierConfigDecorator cashierConfigDecorator;

    @Resource
    private MemberStoreUsageDecorator storeUsageDecorator;


    /**
     * 储值场景下拉框
     */
    @RequestMapping("/selectScene")
    public Response<List<SelectResp>> selectScene(@RequestBody SelectReq request) {
        List<SelectResp> resps = cashierConfigDecorator.selectCashierScene(request);
        List<SelectResp> result = new ArrayList<>();
        resps.forEach(resp -> {
            if (resp.getValue().equals(CashierSceneEnum.MEMBER_RECHARGE.getCode())) {
                return;
            }
            result.add(resp);
        });
        return Response.success(result);
    }

    /**
     * 根据搜索条件分页查询配置
     * CRM调用
     */
    @RequestMapping("/page")
    public Response<Pageable<MemberStoreUsageRuleResponse>> page(@Valid @RequestBody QueryMemberMasterUsageRequest request) {
        QueryMemberMasterUsageDto dto = usageConverter.convert(request);
        Pageable<MemberStoreUsageRuleDto> page = storeUsageMedService.page(dto);
        return Response.success(PageableUtil.convert(page, usageConverter::convert));

    }

    /**
     * 新增储值设置
     */
    @RequestMapping("/add")
    public Response<List<MemberUsageRuleSaveResultResponse>> add(@Valid @RequestBody MemberStoreUsageRuleConfigSaveRequest request) {
        MemberStoreUsageRuleConfigSaveDto dto = usageConverter.convert(request);
        List<MemberUsageRuleSaveResultDto> results = storeUsageMedService.save(dto);
        return Response.success(usageConverter.convert(results));

    }

    /**
     * 编辑储值设置
     */
    @RequestMapping("/save")
    public Response<List<MemberUsageRuleSaveResultResponse>> save(@Valid @RequestBody MemberStoreUsageRuleConfigSaveRequest request) {
        MemberStoreUsageRuleConfigSaveDto dto = usageConverter.convert(request);
        List<MemberUsageRuleSaveResultDto> results = storeUsageMedService.save(dto);
        return Response.success(usageConverter.convert(results));
    }

    /**
     * 更新状态
     */
    @RequestMapping("/updateState")
    public Response<Boolean> updateState(@Valid @RequestBody UpdateMemberUsageStateRequest request) {
        UpdateMemberUsageStateDto dto = usageConverter.convert(request);
        storeUsageMedService.updateState(dto);
        return Response.success(true);
    }

    /**
     * 删除
     */
    @RequestMapping("/delete")
    public Response<Boolean> delete(@Valid @RequestBody DeleteMemberUsageRequest request) {
        Boolean result = storeUsageMedService.delete(usageConverter.convert(request));
        return Response.success(result);
    }


    /**
     * 储值规则未设置提醒
     */
    @RequestMapping("/remind")
    public Response<List<MemberUsageRuleSaveResultResponse>> remind(@Valid @RequestBody BaseRequest request) {
        List<MemberUsageRuleSaveResultDto> result = storeUsageMedService.remind(usageConverter.convert(request));
        return Response.success(usageConverter.convert(result));
    }


    /**
     * 获取可使用的储值规则
     */
    @RequestMapping("/listRuleForAsset")
    public Response<List<MemberStoreUsageRuleAssetResponse>> listRuleForAsset(@Valid @RequestBody BaseRequest request) {
        List<MemberStoreUsageRuleAssetResp> resps = storeUsageDecorator.listRuleForAsset(usageConverter.convertAssetReq(request));
        List<MemberStoreUsageRuleAssetResp> ruleAssetResps = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(resps)) {
            resps.forEach(resp -> {
                if (resp.getScopePlatformChannels() != null && resp.getScopePlatformChannels().contains(PlatformChannelEnum.CRM.getPlatformChannel())) {
                    ruleAssetResps.add(resp);
                }
            });
        }
        return Response.success(usageConverter.convertAsset(ruleAssetResps));
    }


}
