package com.ly.titc.pms.member.gateway.entity.request.spm.giftpack;

import com.ly.titc.pms.member.gateway.entity.request.PageBaseRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024-11-28 16:57
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class PageGiftPackBatchRequest extends PageBaseRequest {

    /**
     * 礼包名称
     */
    private String giftPackName;

    /**
     * 发放开始时间
     */
    @NotBlank(message = "发放开始时间不能为空")
    private String grantStartTime;

    /**
     * 发放结束时间
     */
    @NotBlank(message = "发放结束时间不能为空")
    private String grantEndTime;

    /**
     * 操作人
     */
    private String createUser;

    /**
     * 发放事件
     */
    private Integer grantEvent;

    /**
     * 会员号
     */
    private String memberNo;

    /**
     * 会员手机号
     */
    private String mobile;
}
