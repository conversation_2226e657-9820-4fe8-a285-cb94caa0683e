package com.ly.titc.pms.member.gateway.converter;

import com.ly.titc.pms.ecrm.dubbo.entity.request.MemberAssetSysConfigReq;
import com.ly.titc.pms.ecrm.dubbo.entity.response.MemberAssetSysConfigResp;
import com.ly.titc.pms.member.gateway.entity.request.sysConfig.MemberAssetSysConfigRequest;
import org.mapstruct.Mapper;
import org.mapstruct.Mappings;

/**
 * <AUTHOR>
 * @date 2025/5/9
 */
@Mapper(componentModel = "spring")

public interface MemberConfigConverter {

    MemberAssetSysConfigReq convert(MemberAssetSysConfigRequest request);


    MemberAssetSysConfigResp convert(MemberAssetSysConfigResp resp);
}
