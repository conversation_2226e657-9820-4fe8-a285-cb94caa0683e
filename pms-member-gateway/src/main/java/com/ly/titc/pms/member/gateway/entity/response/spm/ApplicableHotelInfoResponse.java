package com.ly.titc.pms.member.gateway.entity.response.spm;

import com.ly.titc.pms.spm.dubbo.entity.dto.rule.use.applicable.CommonInfoDto;
import lombok.Data;

import java.util.List;

/**
 * 价格方案适用范围
 * @Description:
 * @Author: huaizhu.zhong
 * @Date: 2024-11-12
 */
@Data
public class ApplicableHotelInfoResponse {
    /**
     * true-全部 false-指定
     */
    private Boolean allHotels;

    /**
     * 品牌信息
     */
    private List<CommonInfoDto> brandInfos;

    /**
     * 区域信息
     */
    private List<CommonInfoDto> areaInfos;

    /**
     * 城市
     */
    private List<CommonInfoDto> regionInfos;

    /**
     * 酒店
     */
    private List<CommonInfoDto> hotelInfos;
}
