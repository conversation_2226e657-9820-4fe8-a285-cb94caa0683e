package com.ly.titc.pms.member.gateway.controller.member;

import cn.hutool.core.collection.CollectionUtil;
import com.ly.titc.cc.sdk.log.annotation.LogRecord;
import com.ly.titc.common.entity.Response;
import com.ly.titc.common.exceptions.ServiceException;
import com.ly.titc.mdm.entity.response.hotel.HotelBaseInfoResp;
import com.ly.titc.pms.ecrm.dubbo.entity.request.member.SaveCardConfigReq;
import com.ly.titc.pms.ecrm.dubbo.entity.response.member.*;
import com.ly.titc.pms.member.com.annotation.OperationLog;
import com.ly.titc.pms.member.com.constant.ActionConstants;
import com.ly.titc.pms.member.com.constant.CommonConstant;
import com.ly.titc.pms.member.com.constant.ModuleConstants;
import com.ly.titc.pms.member.com.enums.ApplicationTypeEnum;
import com.ly.titc.pms.member.com.enums.RespCodeEnum;
import com.ly.titc.pms.member.com.lang.TwoTuple;
import com.ly.titc.pms.member.com.utils.ConfigCenterUtil;
import com.ly.titc.pms.member.dubbo.enums.MasterTypeEnum;
import com.ly.titc.pms.member.gateway.converter.MemberCardConverter;
import com.ly.titc.pms.member.gateway.entity.request.BaseRequest;
import com.ly.titc.pms.member.gateway.entity.request.member.GetDetailBaseRequest;
import com.ly.titc.pms.member.gateway.entity.request.member.card.DeleteMemberCardConfigRequest;
import com.ly.titc.pms.member.gateway.entity.request.member.card.SaveMemberCardConfigRequest;
import com.ly.titc.pms.member.gateway.entity.response.member.MemberCardConfigResponse;
import com.ly.titc.pms.member.mediator.rpc.dsf.mdm.HotelDecorator;
import com.ly.titc.pms.member.mediator.rpc.dubbo.ecrm.MemberCardInfoDecorator;
import com.ly.titc.pms.member.mediator.rpc.dubbo.ecrm.MemberPrivilegeDecorator;
import com.ly.titc.pms.member.mediator.service.MemberCardMedService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 会员卡管理
 *
 * @Author：rui
 * @name：MemberCardController
 * @Date：2024-11-12 19:42
 * @Filename：MemberCardController
 */
@Slf4j
@RestController
@RequestMapping("/member/card")
public class MemberCardController {

    @Resource
    private MemberCardConverter converter;

    @Resource
    private MemberCardInfoDecorator decorator;

    @Resource
    private MemberPrivilegeDecorator privilegeDecorator;

    @Resource
    private MemberCardMedService memberCardMedService;

    @Resource
    private HotelDecorator hotelDecorator;

    /**
     * 会员卡列表
     */
    @PostMapping("/listMemberCardConfig")
    public Response<List<MemberCardConfigResponse>> listMemberCardConfig(@RequestBody @Valid BaseRequest request) {
        TwoTuple<Integer, String> masterTuple = ConfigCenterUtil.getMasterByBloc(request.getBlocCode());
        List<MemberCardConfigDetailResp> cardConfigRespList = decorator.listMemberCardConfig(masterTuple.getFirst(), request.getBlocCode(), null);
        return Response.success(cardConfigRespList.stream().map(converter::convertMemberCardConfigResponse).collect(Collectors.toList()));
    }

    /**
     * 会员卡详情
     */
    @PostMapping("/getMemberCardConfigDetail")
    public Response<MemberCardConfigResponse> getMemberCardConfigDetail(@RequestBody @Valid GetDetailBaseRequest request) {
        TwoTuple<Integer, String> masterTuple = ConfigCenterUtil.getMasterByBloc(request.getBlocCode());
        List<MemberCardConfigDetailResp> cardConfigRespList = decorator.listMemberCardConfig(masterTuple.getFirst(), request.getBlocCode(), request.getId());
        return Response.success(cardConfigRespList.stream().map(converter::convertMemberCardConfigResponse).collect(Collectors.toList()).get(0));
    }

    /**
     * 查询默认卡，用于校验默认卡时二次确认
     */
    @PostMapping("/getDefaultCard")
    public Response<MemberCardConfigResponse> getDefaultCard(@RequestBody @Valid BaseRequest request) {
        TwoTuple<Integer, String> masterTuple = ConfigCenterUtil.getMasterByBloc(request.getBlocCode());
        return Response.success(decorator.getDefaultMemberCardConfigInfo(request.getBlocCode(), masterTuple.getFirst()));
    }

    /**
     * 新增会员卡
     *
     * @param request request
     * @return id
     */
    @PostMapping("/addMemberCardConfig")
    @LogRecord(tenant = CommonConstant.PROJECT_CODE, bizCode = ModuleConstants.MEMBER_CARD_MANAGE_CODE, bizName = ModuleConstants.MEMBER_CARD_MANAGE_NAME,
            category = ActionConstants.ADD_CODE, categoryName = ActionConstants.ADD_NAME)
    @OperationLog(operateRecord = "#memberCardConfig_addMemberCardConfig(#request)")
    public Response<Long> addMemberCardConfig(@RequestBody @Valid SaveMemberCardConfigRequest request) {
        TwoTuple<Integer, String> masterTuple = ConfigCenterUtil.getMasterByBloc(request.getBlocCode());
        SaveCardConfigReq req = converter.convertReq(request, masterTuple.getFirst(), ApplicationTypeEnum.HOTEL.getType());
        return Response.success(decorator.saveMemberCardConfig(req));
    }

    /**
     * 修改会员卡
     *
     * @param request request
     * @return id
     */
    @PostMapping("/updateMemberCardConfig")
    @LogRecord(tenant = CommonConstant.PROJECT_CODE, bizCode = ModuleConstants.MEMBER_CARD_MANAGE_CODE, bizName = ModuleConstants.MEMBER_CARD_MANAGE_NAME,
            category = ActionConstants.SAVE_CODE, categoryName = ActionConstants.SAVE_NAME)
    @OperationLog(operateRecord = "#memberCardConfig_updateMemberCardConfig(#request)")
    public Response<Long> updateMemberCardConfig(@RequestBody @Valid SaveMemberCardConfigRequest request) {
        TwoTuple<Integer, String> masterTuple = ConfigCenterUtil.getMasterByBloc(request.getBlocCode());
        SaveCardConfigReq req = converter.convertReq(request, masterTuple.getFirst(), ApplicationTypeEnum.HOTEL.getType());
        return Response.success(decorator.saveMemberCardConfig(req));
    }

    /**
     * 删除会员卡
     *
     * @param request request
     * @return 结果
     */
    @PostMapping("/deleteMemberCardConfig")
    @LogRecord(tenant = CommonConstant.PROJECT_CODE, bizCode = ModuleConstants.MEMBER_CARD_MANAGE_CODE, bizName = ModuleConstants.MEMBER_CARD_MANAGE_NAME,
            category = ActionConstants.DELETE_CODE, categoryName = ActionConstants.DELETE_NAME)
    @OperationLog(operateRecord = "#memberCardConfig_deleteMemberCardConfig(#request)")
    public Response deleteMemberCardConfig(@RequestBody @Valid DeleteMemberCardConfigRequest request) {

        TwoTuple<Integer, String> masterTuple = ConfigCenterUtil.getMasterByBloc(request.getBlocCode());

        // 先校验有没有会员
        if (memberCardMedService.checkHasCard(masterTuple.getFirst(), request.getBlocCode(), Long.parseLong(request.getCardId()), null)) {
            throw new ServiceException(RespCodeEnum.MEMBER_10020);
        }
        decorator.deleteMemberCardConfig(converter.convertDeleteBaseReq(request));
        return Response.success();
    }

    /**
     * 会员卡下拉选择,会员注册时可以使用
     */
    @PostMapping("/selectMemberCardConfig")
    public Response<List<MemberCardConfigResponse>> selectMemberCardConfig(@RequestBody @Valid BaseRequest request) {
        TwoTuple<Integer, String> masterTuple = ConfigCenterUtil.getMasterByBloc(request.getBlocCode());
        List<MemberCardConfigResp> cardConfigRespList = decorator.listCardConfig(masterTuple.getFirst(), request.getBlocCode())
                .stream().filter(item -> item.getState() == 1).collect(Collectors.toList());
        // 查询卡关联等级列表
        if (CollectionUtil.isEmpty(cardConfigRespList)) {
            return Response.success(Collections.emptyList());
        }
        List<Long> cardIdList = cardConfigRespList.stream().map(MemberCardConfigResp::getId).collect(Collectors.toList());
        List<MemberCardLevelConfigInfoResp> cardLevelConfigInfoRespList = decorator.listMemberCardLevel(cardIdList);
        return Response.success(converter.convertMemberCardConfigResponse(cardConfigRespList, cardLevelConfigInfoRespList));
    }
}
