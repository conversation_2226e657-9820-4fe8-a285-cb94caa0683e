package com.ly.titc.pms.member.gateway.entity.request;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Author：rui
 * @name：PageBaseRequest
 * @Date：2023-11-3 15:17
 * @Filename：PageBaseRequest
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class PageBaseRequest extends BaseRequest {

    private Integer pageIndex = 1;

    private Integer pageSize = 20;
}
