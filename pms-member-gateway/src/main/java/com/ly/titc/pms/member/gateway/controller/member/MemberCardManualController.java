package com.ly.titc.pms.member.gateway.controller.member;

import com.ly.titc.common.entity.Response;
import com.ly.titc.pms.member.gateway.entity.request.member.IssueMemberCardManualRequest;
import com.ly.titc.pms.member.gateway.entity.response.member.IssueMemberCardManualResponse;
import com.ly.titc.pms.member.mediator.entity.dto.member.IssueMemberCardRequestDto;
import com.ly.titc.pms.member.mediator.entity.dto.member.IssueMemberCardResultDto;
import com.ly.titc.pms.member.mediator.service.MemberMedService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 会员卡手动发放控制器
 *
 * <AUTHOR>
 * @date 2024/12/25 10:00
 */
@Slf4j
@RestController
@RequestMapping("/member/card/manual")
public class MemberCardManualController {

    @Resource
    private MemberMedService memberMedService;

    /**
     * 发放会员卡
     *
     * @param request
     * @return
     */
    @PostMapping("/issue")
    public Response<IssueMemberCardManualResponse> issueMemberCard(@RequestBody @Valid IssueMemberCardManualRequest request) {
        try {
            // 转换请求参数
            IssueMemberCardRequestDto dto = convertToDto(request);
            // 调用服务
            IssueMemberCardResultDto result = memberMedService.issueMemberCard(dto);
            // 转换响应
            IssueMemberCardManualResponse response = convertToResponse(result);
            return Response.success(response);
        } catch (Exception e) {
            log.error("发放会员卡失败", e);
            IssueMemberCardManualResponse response = new IssueMemberCardManualResponse();
            response.setSuccess(false);
            response.setFailureReason("系统异常：" + e.getMessage());
            return Response.success(response);
        }
    }

    /**
     * 转换请求参数
     *
     * @param request
     * @return
     */
    private IssueMemberCardRequestDto convertToDto(IssueMemberCardManualRequest request) {
        IssueMemberCardRequestDto dto = new IssueMemberCardRequestDto();
        dto.setMasterType(request.getMasterType());
        dto.setMasterCode(request.getBlocCode());
        dto.setCustomerNo(request.getCustomerNo());
        dto.setMemberNo(request.getMemberNo());
        dto.setDefaultCard(request.getDefaultCard());
        dto.setCardId(request.getCardId());
        dto.setCardLevel(request.getCardLevel());
        dto.setHotelCode(request.getHotelCode());
        dto.setBizId(request.getBizId());
        dto.setReason(request.getReason());
        return dto;
    }

    /**
     * 转换响应
     *
     * @param result
     * @return
     */
    private IssueMemberCardManualResponse convertToResponse(IssueMemberCardResultDto result) {
        IssueMemberCardManualResponse response = new IssueMemberCardManualResponse();
        response.setSuccess(result.isSuccess());
        response.setFailureReason(result.getFailureReason());
        response.setMemberNo(result.getMemberNo());
        response.setMemberCardNo(result.getMemberCardNo());
        response.setCardLevel(result.getCardLevel());
        response.setCardLevelName(result.getCardLevelName());
        response.setOperationType(result.getOperationType());
        return response;
    }

}
