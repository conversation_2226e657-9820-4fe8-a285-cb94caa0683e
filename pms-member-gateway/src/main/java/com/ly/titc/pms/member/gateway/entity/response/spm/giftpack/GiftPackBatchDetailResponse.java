package com.ly.titc.pms.member.gateway.entity.response.spm.giftpack;

import com.ly.titc.pms.member.gateway.annoation.Sensitive;
import com.ly.titc.pms.member.gateway.entity.enums.SensitiveFieldTypeEnum;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024-11-29 10:41
 */
@Data
@Accessors(chain = true)
public class GiftPackBatchDetailResponse {

    /**
     * 礼包明细产品类型
     * 1-优惠券 2-会员卡 3-积分 4-会员权益 5-联名权益
     */
    private Integer bizType;

    /**
     * 明细业务类型描述
     */
    private String bizTypeName;

    /**
     * 明细业务编码
     */
    private String bizNo;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 客户编号
     */
    private String customerNo;

    /**
     * 会员编号号
     */
    private String memberNo;

    /**
     * 手机号
     */
    private String mobile;
}
