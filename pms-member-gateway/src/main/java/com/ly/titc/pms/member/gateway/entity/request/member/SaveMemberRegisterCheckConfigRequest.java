package com.ly.titc.pms.member.gateway.entity.request.member;

import com.ly.titc.pms.member.gateway.entity.request.BaseRequest;
import lombok.Data;

import java.util.List;

/**
 * @Author：rui
 * @name：SaveMemberRegisterCheckConfigRequest
 * @Date：2024-11-25 11:47
 * @Filename：SaveMemberRegisterCheckConfigRequest
 */
@Data
public class SaveMemberRegisterCheckConfigRequest extends BaseRequest {

    /**
     * id,编辑时必填
     */
    private Long id;

    /**
     * 必填项json
     */
    private List<String> params;

    /**
     * 0 注册 1 手机验证设置 2 列表显示 3 列表快捷操作
     */
    private Integer type;
}
