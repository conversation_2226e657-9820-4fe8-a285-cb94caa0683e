package com.ly.titc.pms.member.gateway.entity.request.ratePlan;

import com.ly.titc.pms.member.gateway.entity.request.BaseRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * @Description:
 * @Author: huaizhu.zhong
 * @Date: 2024-12-13
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ListBlocRatePlanRequest extends BaseRequest {

    /**
     * 模式
     */
    @NotNull(message = "模式不能为空")
    private Integer rentMode;
}
