package com.ly.titc.pms.member.gateway.entity.request.spm.giftpack;

import com.ly.titc.pms.member.gateway.entity.request.BaseRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024-12-25 17:50
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class QueryGiftPackGrantDetailsRequest extends BaseRequest {

    @NotBlank(message = "receiverCode不能为空")
    private String receiverCode;

    @NotBlank(message = "receiverType不能为空")
    private String receiverType;

    @NotBlank(message = "activityCode不能为空")
    private String activityCode;

    @NotEmpty(message = "giftPackNos不能为空")
    private List<String> giftPackNos;

    /**
     * 触发礼包奖励业务编号
     */
    @NotBlank(message = "triggerBizNo")
    private String triggerBizNo;
}
