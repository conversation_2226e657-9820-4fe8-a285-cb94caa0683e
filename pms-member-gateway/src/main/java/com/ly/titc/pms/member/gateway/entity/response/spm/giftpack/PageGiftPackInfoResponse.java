package com.ly.titc.pms.member.gateway.entity.response.spm.giftpack;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Description:
 * @Author: huaizhu.zhong
 * @Date: 2024-11-22
 */
@Data
public class PageGiftPackInfoResponse {

    /**
     * 主体类型
     */
    private Integer masterType;

    /**
     * 主体对象
     */
    private String masterCode;

    /**
     * 礼包编码
     */
    private String giftPackNo;

    /**
     * 礼包名称
     */
    private String giftPackName;

    /**
     * 礼包描述
     */
    private String content;

    /**
     * 礼包开始时间
     */
    private String effectStartDate;

    /**
     * 礼包结束时间
     */
    private String effectEndDate;

    /**
     * 礼包金额
     */
    private BigDecimal amount;

    /**
     * 状态 1-已生效 2-已停用 3-已过期
     */
    private Integer state;

    /**
     * 礼包库存
     */
    private Integer availableQuantity;

    /**
     * 来源（PMS,CMS,WDF(微订房),ELONG(艺龙会),WPMS(微PMS),CRS ,CRM,ECRM）s
     */
    private String sourceClient;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 创建时间
     */
    private String gmtCreate;

    /**
     * 修改人
     */
    private String modifyUser;

    /**
     * 修改时间
     */
    private String gmtModified;
}
