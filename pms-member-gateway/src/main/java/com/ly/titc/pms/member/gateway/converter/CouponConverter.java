package com.ly.titc.pms.member.gateway.converter;

import com.google.common.collect.Lists;
import com.ly.titc.mdm.entity.response.hotel.HotelBaseInfoResp;
import com.ly.titc.pms.customer.dubbo.entity.request.customer.GetByCustomerNoReq;
import com.ly.titc.pms.customer.dubbo.entity.response.customer.CustomerInfoResp;
import com.ly.titc.pms.member.com.constant.SystemConstant;
import com.ly.titc.pms.member.dubbo.enums.MasterTypeEnum;
import com.ly.titc.pms.member.gateway.entity.request.spm.coupon.*;
import com.ly.titc.pms.member.gateway.entity.response.spm.coupon.*;
import com.ly.titc.pms.member.mediator.entity.dto.member.MemberDetailDto;
import com.ly.titc.pms.member.mediator.entity.dto.member.MemberInfoDto;
import com.ly.titc.pms.spm.dubbo.entity.dto.ReceiverInfoDto;
import com.ly.titc.pms.spm.dubbo.entity.request.coupon.*;
import com.ly.titc.pms.spm.dubbo.entity.response.coupon.*;
import com.ly.titc.pms.spm.dubbo.enums.ReceiverTypeEnum;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;


@Mapper(componentModel = "spring")
public interface CouponConverter {

    @Mapping(target = "hotelCode", ignore = true)
    @Mapping(target = "currPage", source = "req.pageIndex")
    GetCouponListReq convertGetCouponListReq(PageCouponRequest req);

    @Mapping(target = "receiverInfos", ignore = true)
    @Mapping(target = "sourceClient", expression = "java(com.ly.titc.pms.spm.dubbo.enums.SourceClientEnum.ECRM.getCode())")
    @Mapping(target = "isSmsNotification", ignore = true)
    @Mapping(target = "isAllowCrossStore", source = "crossShop")
    @Mapping(target = "grantEvent", constant = "1")
    GrantCouponBatchReq convertGrantCouponRequest2ApiReq(GrantCouponRequest req);

    ReceiverInfoDto convertCouponReceiverInfo(String receiverType,String receiverCode,String customerHotelCode,Integer grantNum);

    default GrantCouponBatchReq convertGrantCouponBatchReq(GrantCouponRequest req) {
        GrantCouponBatchReq grantCouponBatchReq = this.convertGrantCouponRequest2ApiReq(req);
        grantCouponBatchReq.setIsSmsNotification(BooleanUtils.toInteger(req.getToSmsNotify()));
        if(StringUtils.isNotBlank(req.getReceiverCode())){
            grantCouponBatchReq.setReceiverInfos(
                    Lists.newArrayList(this.convertCouponReceiverInfo(req.getReceiverType(),req.getReceiverCode(),
                            req.getCustomerHotelCode(),req.getGrantNum())));
        }
        return grantCouponBatchReq;
    }




    @Mapping(target = "grantStartTime", source = "grantTimeStart")
    @Mapping(target = "grantEndTime", source = "grantTimeEnd")
    @Mapping(target = "currPage", source = "pageIndex")
    @Mapping(target = "createUser", source = "operatorName")
    QueryCouponBatchListReq convertPageBatchRequest2ApiReq(PageCouponBatchRequest req);

    @Mapping(target = "hotelName", ignore = true)
    @Mapping(target = "crossShop", source = "isAllowCrossStore")
    @Mapping(target = "smsNotice", source = "isSmsNotification")
    @Mapping(target = "grantTime", source = "grantDate")
    @Mapping(target = "operator", source = "createUser")
    CouponBatchListResponse convertCouponBatchListResp2Response(CouponBatchListResp d);

    default CouponBatchListResponse buildCouponBatchListResp2Response(CouponBatchListResp d, Map<String, HotelBaseInfoResp> hotelBaseInfoMap){
        CouponBatchListResponse couponBatchListResponse = this.convertCouponBatchListResp2Response(d);
        if (StringUtils.isBlank(d.getHotelCode())) {
            couponBatchListResponse.setHotelName(SystemConstant.CENTRAL_GENERATION);
        } else {
            HotelBaseInfoResp hotelBaseInfoResp = hotelBaseInfoMap.get(d.getHotelCode());
            couponBatchListResponse.setHotelName(Objects.isNull(hotelBaseInfoResp) ? "" : hotelBaseInfoResp.getHotelName());
        }
        return couponBatchListResponse;
    }

    CouponExportSimpleResponse convertCouponExportSimpleResponse(CouponBatchDetailResponse couponBatchDetailResponse);
    List<CouponExportSimpleResponse> convertCouponExportSimpleResponse(List<CouponBatchDetailResponse> couponBatchDetailResponses);

    @Mapping(target = "currPage", source = "pageIndex")
    QueryCouponBatchDetailsReq convertPageBatchDetailsRequest2ApiReq(PageCouponBatchDetailsRequest req);

    @Mapping(target = "mobile", ignore = true)
    @Mapping(target = "memberNo", ignore = true)
    @Mapping(target = "hotelName", ignore = true)
    @Mapping(target = "customerNo", ignore = true)
    @Mapping(target = "customerName", ignore = true)
    CouponBatchDetailResponse convertCouponBatchDetailResp2Response(CouponBatchDetailResp couponBatchDetailResp);
    default CouponBatchDetailResponse buildCouponBatchDetailResponse(CouponBatchDetailResp couponBatchDetailResp,
                                                                     Map<String, MemberDetailDto> memberDetailMap,
                                                                     Map<String, CustomerInfoResp> customerInfoMap,
                                                                     Map<String, HotelBaseInfoResp> hotelBaseInfoMap){
        CouponBatchDetailResponse couponBatchDetailResponse = this.convertCouponBatchDetailResp2Response(couponBatchDetailResp);
        if(Objects.equals(couponBatchDetailResp.getReceiverType(),ReceiverTypeEnum.MEMBER.getCode())){
            MemberDetailDto memberDetailDto = memberDetailMap.get(couponBatchDetailResp.getReceiverCode());
            if(Objects.nonNull(memberDetailDto)){
                couponBatchDetailResponse.setMobile(memberDetailDto.getMobile());
                couponBatchDetailResponse.setCustomerName(memberDetailDto.getRealName());
                couponBatchDetailResponse.setMemberNo(couponBatchDetailResp.getReceiverCode());
            }
        }else{
            CustomerInfoResp customerInfoResp = customerInfoMap.get(couponBatchDetailResp.getReceiverCode());
            if(Objects.nonNull(customerInfoResp)){
                couponBatchDetailResponse.setMobile(customerInfoResp.getMobile());
                couponBatchDetailResponse.setCustomerName(customerInfoResp.getRealName());
                couponBatchDetailResponse.setCustomerNo(couponBatchDetailResp.getReceiverCode());
                couponBatchDetailResponse.setMemberNo(customerInfoResp.getMemberNo());
            }
        }
        if (Objects.equals(couponBatchDetailResp.getMasterType(),MasterTypeEnum.HOTEL.getType())) {
            HotelBaseInfoResp hotelBaseInfoResp = hotelBaseInfoMap.get(couponBatchDetailResp.getMasterCode());
            couponBatchDetailResponse.setHotelName(Objects.nonNull(hotelBaseInfoResp) ? hotelBaseInfoResp.getHotelName() : Strings.EMPTY);
        }else{
            couponBatchDetailResponse.setHotelName(SystemConstant.CENTRAL_GENERATION);
        }
        return couponBatchDetailResponse;
    }

    GetByCustomerNoReq convertGetByCustomerNoReq(String blocCode, String trackingId,String hotelCode,String customerNo);

    GetByCustomerNoReq convertGetCustomerNoReqByGetGrantReceiverRequest(GetGrantReceiverRequest request);


    default CouponDetailResponse buildCouponDetailResponse(CouponInfoResp couponInfoResp,
                                                           MemberInfoDto memberInfoDto,
                                                           CustomerInfoResp customerInfoResp) {
        CouponDetailResponse couponDetailResponse = new CouponDetailResponse();
        couponDetailResponse.setGrantTime(couponInfoResp.getGrantDate());
        if(ReceiverTypeEnum.MEMBER.getCode().equals(couponInfoResp.getReceiverType())){
            couponDetailResponse.setOwner(memberInfoDto.getRealName());
            couponDetailResponse.setMobile(memberInfoDto.getMobile());
            couponDetailResponse.setOwnerCustomerNo(Strings.EMPTY);
            couponDetailResponse.setOwnerMemberCardNo(memberInfoDto.getMemberNo());
        }else{
            couponDetailResponse.setOwner(customerInfoResp.getRealName());
            couponDetailResponse.setMobile(customerInfoResp.getMobile());
            couponDetailResponse.setOwnerCustomerNo(customerInfoResp.getCustomerNo());
            couponDetailResponse.setOwnerMemberCardNo(customerInfoResp.getMemberCardNo());
        }
        return couponDetailResponse;
    }

    @Mapping(target = "hotelCode", ignore = true)
    GetCouponInfoReq convertGetCouponRequest2GetRedeemReq(GetCouponRequest request);

    CouponRedeemInfoResponse convertCouponRedeemInfoResp2Response(CouponRedeemInfoResp apiResp);


    @Mapping(target = "effectiveTimeDesc", ignore = true)
    @Mapping(target = "mobile", ignore = true)
    @Mapping(target = "memberNo", ignore = true)
    @Mapping(target = "idCard", ignore = true)
    @Mapping(target = "owner", ignore = true)
    @Mapping(target = "customerNo", ignore = true)
    @Mapping(target = "useTime", source = "useDate")
    @Mapping(target = "couponTypeName", expression = "java(com.ly.titc.pms.spm.dubbo.enums.CouponTypeEnum.getEnum(d.getCouponType()).getDesc())")
    CouponListResponse convertCouponListResponse(CouponListResp d);
    default CouponListResponse buildCouponListResponse(CouponListResp couponListResp,
                                                       Map<String, MemberDetailDto> memberDetailMap,
                                                       Map<String, CustomerInfoResp> customerInfoMap) {
        CouponListResponse couponListResponse = this.convertCouponListResponse(couponListResp);
        if (ReceiverTypeEnum.MEMBER.getCode().equals(couponListResp.getReceiverType())) {
            MemberDetailDto memberDetailDto = memberDetailMap.get(couponListResp.getReceiverCode());
            couponListResponse.setMemberNo(couponListResp.getReceiverCode());
            if (!Objects.isNull(memberDetailDto)) {
                couponListResponse.setMobile(memberDetailDto.getMobile());
                couponListResponse.setIdCard(memberDetailDto.getIdNo());
                couponListResponse.setOwner(memberDetailDto.getRealName());
            }
        } else if (ReceiverTypeEnum.CUSTOMER.getCode().equals(couponListResp.getReceiverType())) {
            couponListResponse.setCustomerNo(couponListResp.getReceiverCode());
            CustomerInfoResp customerInfoResp = customerInfoMap.get(couponListResp.getReceiverCode());
            if (!Objects.isNull(customerInfoResp)) {
                couponListResponse.setMobile(customerInfoResp.getMobile());
                couponListResponse.setIdCard(customerInfoResp.getIdNo());
                couponListResponse.setOwner(customerInfoResp.getRealName());
            }
        }
        if (StringUtils.isNotBlank(couponListResp.getStartDate()) && couponListResp.getStartDate().length() > 10) {
            couponListResponse.setStartDate(couponListResp.getStartDate().substring(0, 10));
        }
        if (StringUtils.isNotBlank(couponListResp.getEndDate()) && couponListResp.getEndDate().length() > 10) {
            couponListResponse.setEndDate(couponListResp.getEndDate().substring(0, 10));
        }
        couponListResponse.setEffectiveTimeDesc(couponListResp.isLongTerm() ? "永久有效"
                : couponListResponse.getStartDate() + " - " + couponListResponse.getEndDate());
        return couponListResponse;
    }

    CouponExportResponse convertCouponExportResponse(CouponListResponse couponListResponse);
    List<CouponExportResponse> convertCouponExportResponses(List<CouponListResponse> couponListResponses);

    @Mapping(target = "hotelCode", ignore = true)
    StatisticsCouponReq convertStatisticsCouponReq(CouponStatisticsRequest req);

    CouponStatisticsResponse convertCouponStatisticsResponse(CouponStatisticsResp apiResp);

    @Mapping(target = "receiverInfos", expression = "java(buildCouponReceiverInfos(memberNos,grantNum))")
    @Mapping(target = "trackingId", ignore = true)
    @Mapping(target = "operator", source = "operator")
    @Mapping(target = "hotelCode", source = "hotelCode", defaultValue = "")
    @Mapping(target = "grantEvent", expression = "java(com.ly.titc.pms.spm.dubbo.enums.GrantEventEnum.MANUAL_GRANT.getEvent())")
    @Mapping(target = "sourceClient", expression = "java(com.ly.titc.pms.spm.dubbo.enums.SourceClientEnum.ECRM.getCode())")
    @Mapping(target = "isAllowCrossStore", source = "crossShop")
    @Mapping(target = "isSmsNotification", expression = "java(org.apache.commons.lang3.BooleanUtils.isTrue(toSmsNotify)?1:0)")
    GrantCouponBatchReq convertParamsToGrantCouponBatchReq(String templateCode, Integer version, Integer grantNum,
                                                           String blocCode, String hotelCode, Integer crossShop,
                                                           Boolean toSmsNotify, List<String> memberNos,String operator);

    default List<ReceiverInfoDto> buildCouponReceiverInfos(List<String> memberNos,Integer grantNum){
        return memberNos.stream().map(memberNo-> this.convertCouponReceiverInfo(ReceiverTypeEnum.MEMBER.getCode(),memberNo,"",grantNum))
                .collect(Collectors.toList());
    }

    @Mapping(target = "customerNo", ignore = true)
    @Mapping(target = "customerName", source = "realName")
    CouponReceiverInfoResponse convertCouponReceiverInfoResponseByMemberInfoDto(MemberInfoDto memberInfoDto);

    @Mapping(target = "customerNo", ignore = true)
    @Mapping(target = "customerName", source = "realName")
    CouponReceiverInfoResponse convertCouponReceiverInfoResponseByMemberDetailDto(MemberDetailDto memberDetailDto);

    @Mapping(target = "customerName", source = "realName")
    CouponReceiverInfoResponse convertCouponReceiverInfoResponseByCustomerInfoResp(CustomerInfoResp customerInfoResp);

    GetByCouponBatchCodeReq convertGetByCouponBatchCodeReq(CouponBatchInfoRequest req);

    GetByCouponCodeReq convertGetByCouponCodeReq(CouponInfoDetailRequest req);

    GetCouponInfoReq convertGetCouponInfoReq(CouponInfoDetailRequest request);

}
