package com.ly.titc.pms.member.gateway.entity.request.spm.activity;

import com.ly.titc.common.annotation.LegalEnum;
import com.ly.titc.pms.member.gateway.entity.request.PageBaseRequest;
import com.ly.titc.pms.spm.dubbo.enums.BenefitTypeEnum;
import com.ly.titc.pms.spm.dubbo.enums.SubActivityTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Description
 * <AUTHOR>
 * @Date 2025-3-5 15:45
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PageGrantInfoRequest extends PageBaseRequest {

    /**
     * 活动类型
     */
    @LegalEnum(target = SubActivityTypeEnum.class, message = "活动展示类型不合法",methodName = "getCode")
    private String displayType;

    /**
     * 活动名
     */
    private String activityName;

    /**
     * 活动优惠类型
     */
    @LegalEnum(target = BenefitTypeEnum.class, message = "活动优惠类型不合法",methodName = "getCode")
    private String discountType;

    /**
     * 奖励业务编号
     */
    private String benefitValue;

    /**
     * 奖励明细编号
     */
    private String discountBizDetail;

    /**
     * 奖励人类型
     */
    private String receiverType;

    /**
     * 奖励人编号
     */
    private String receiverCode;

    /**
     * 触发开始时间
     */
    private String triggerStartTime;

    /**
     * 触发结束时间
     */
    private String triggerEndTime;
}
