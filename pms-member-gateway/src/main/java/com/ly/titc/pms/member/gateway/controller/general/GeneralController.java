package com.ly.titc.pms.member.gateway.controller.general;

import com.ly.titc.common.constants.Constant;
import com.ly.titc.common.entity.Response;
import com.ly.titc.common.enums.ScopeSourceEnum;
import com.ly.titc.ehr.entity.response.UserDivisionResp;
import com.ly.titc.mdm.entity.response.area.AreaTreeResp;
import com.ly.titc.mdm.entity.response.brand.SelectBrandResp;
import com.ly.titc.mdm.entity.response.hotel.HotelBaseInfoResp;
import com.ly.titc.mdm.entity.response.hotel.SelectHotelResp;
import com.ly.titc.mdm.entity.response.region.RegionResp;
import com.ly.titc.mdm.enums.StateEnum;
import com.ly.titc.oauth.client.threadlocal.UserThreadHolder;
import com.ly.titc.pms.ecrm.dubbo.entity.response.member.MemberTagConfigInfoResp;
import com.ly.titc.pms.member.com.lang.TwoTuple;
import com.ly.titc.pms.member.com.utils.ConfigCenterUtil;
import com.ly.titc.pms.member.gateway.converter.GeneralConverter;
import com.ly.titc.pms.member.gateway.entity.request.BaseRequest;
import com.ly.titc.pms.member.gateway.entity.request.general.*;
import com.ly.titc.pms.member.gateway.entity.response.SelectResponse;
import com.ly.titc.pms.member.gateway.entity.response.general.*;
import com.ly.titc.pms.member.gateway.thread.local.UserInfoThreadHolder;
import com.ly.titc.pms.member.mediator.entity.dto.user.UserInfoDto;
import com.ly.titc.pms.member.mediator.rpc.dsf.ehr.UserDataDecorator;
import com.ly.titc.pms.member.mediator.rpc.dsf.mdm.AreaDecorator;
import com.ly.titc.pms.member.mediator.rpc.dsf.mdm.BrandDecorator;
import com.ly.titc.pms.member.mediator.rpc.dsf.mdm.HotelDecorator;
import com.ly.titc.pms.member.mediator.rpc.dsf.mdm.RegionDecorator;
import com.ly.titc.pms.member.mediator.rpc.dubbo.ecrm.MemberCardInfoDecorator;
import com.ly.titc.pms.member.mediator.service.MemberGeneralMedService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 通用组件相关
 *
 * @Author：rui
 * @name：GeneralController
 * @Date：2024-12-4 14:40
 * @Filename：GeneralController
 */
@Slf4j
@RestController
@RequestMapping("/general")
public class GeneralController {

    @Resource
    private MemberGeneralMedService memberGeneralMedService;
    @Resource
    private GeneralConverter converter;
    @Resource
    private BrandDecorator brandDecorator;
    @Resource
    private AreaDecorator areaDecorator;
    @Resource
    private RegionDecorator regionDecorator;
    @Resource
    private HotelDecorator hotelDecorator;
    @Resource
    private UserDataDecorator userDataDecorator;
    @Resource
    private MemberCardInfoDecorator decorator;

    /**
     * 查询行政区域
     *
     * @param request
     * @return
     */
    @PostMapping("/getAdministrativeRegion")
    public Response<String> getAdministrativeRegion(@Valid @RequestBody GetAdministrativeRegionRequest request){
        return Response.success(regionDecorator.getAdministrativeRegion(request.getCode()));
    }

    /**
     * 查询区域
     *
     * @param request
     * @return
     */
    @PostMapping("/listRegion")
    public Response<RegionResponse> listRegion(@Valid @RequestBody ListRegionByLevelRequest request) {
        List<RegionResp> resps = regionDecorator.listByLevel(converter.convertRegionReq(request));
        return Response.success(converter.convertRegionResponse(resps));
    }

    /**
     * 区域树
     *
     * @return
     */
    @PostMapping("/listRegionTrees")
    public Response<List<RegionResponse>> listRegionTrees(@Valid @RequestBody ListRegionTreeRequest req) {
        List<RegionResp> list = regionDecorator.listRegionTree(Constant.YES, req.getRegionId());
        return Response.success(list);
    }

    /**
     * 选择客户/会员组件，查询身份
     *
     * @param request
     * @return
     */
    @PostMapping("/queryMemberIdentity")
    public Response<List<MemberGeneralCardConfigResponse>> queryMemberIdentity(@RequestBody @Valid GeneralQueryBaseRequest request) {
        TwoTuple<Integer, String> masterTuple = ConfigCenterUtil.getMasterByBloc(request.getBlocCode());
        return Response.success(converter.convert(memberGeneralMedService.queryMemberIdentity(masterTuple.getFirst(), request.getBlocCode(), request.getName())));
    }

    /**
     * 选择客户/会员组件，查询来源
     *
     * @param request
     * @return
     */
    @PostMapping("/queryMemberSource")
    public Response<List<MemberSourceResponse>> queryMemberSource(@RequestBody @Valid GeneralQueryBaseRequest request) {
        TwoTuple<Integer, String> masterTuple = ConfigCenterUtil.getMasterByBloc(request.getBlocCode());
        return Response.success(converter.convertMemberSource(memberGeneralMedService.queryMemberSource(masterTuple.getFirst(), request.getBlocCode(), request.getName())));
    }

    /**
     * 选择客户组件--标签
     */
    @PostMapping("/queryMemberTag")
    public Response<List<MemberGeneralTagConfigInfoResponse>> queryMemberTag(@RequestBody @Valid GeneralQueryBaseRequest request) {
        TwoTuple<Integer, String> masterTuple = ConfigCenterUtil.getMasterByBloc(request.getBlocCode());
        List<MemberTagConfigInfoResp> list = decorator.listTagConfig(masterTuple.getFirst(), request.getBlocCode(), request.getName());
        return Response.success(converter.convertMemberGeneralTagConfigInfoResponse(list));
    }

    /**
     * 查询适用渠道
     *
     * @param request
     * @return
     */
    @PostMapping("/queryApplicableChannel")
    public Response<List<DictResponse>> queryApplicableChannel(@RequestBody @Valid BaseRequest request) {
        return Response.success(converter.convertDict(memberGeneralMedService.queryApplicableChannel(request.getTrackingId())));
    }

    /**
     * 查询订单渠道（集团渠道）
     *
     * @param request
     * @return
     */
    @PostMapping("/queryApplicableOrderChannel")
    public Response<List<DictResponse>> queryApplicableOrderChannel(@RequestBody @Valid BaseRequest request) {
        return Response.success(converter.convertDict(memberGeneralMedService.queryApplicableOrderChannel(request.getBlocCode())));
    }

    /**
     * 查询消费项目
     *
     * @param request
     * @return
     */
    @PostMapping("/queryApplicableExpense")
    public Response<List<DictResponse>> queryApplicableExpense(@RequestBody @Valid BaseRequest request) {
        return Response.success(converter.convertDict(memberGeneralMedService.queryApplicableExpense(request.getBlocCode())));
    }

    /**
     * 查询积分发放节点
     *
     * @param request
     * @return
     */
    @PostMapping("/queryPointsIssueNode")
    public Response<List<DictResponse>> queryPointsIssueNode(@RequestBody @Valid BaseRequest request) {
        return Response.success(converter.convertDict(memberGeneralMedService.queryPointsIssueNode(request.getBlocCode())));
    }

    /**
     * 查询来源
     */
    @PostMapping("/querySource")
    public Response<List<SelectResponse>> querySource() {
        List<SelectResponse> list = new ArrayList<>();
        for (ScopeSourceEnum value : ScopeSourceEnum.values()) {
            if (value.equals(ScopeSourceEnum.CLUB)) {
                continue;
            }
            SelectResponse selectResponse = new SelectResponse();
            selectResponse.setValue(value.getCode());
            selectResponse.setText(value.getDesc());
            list.add(selectResponse);
        }
        return Response.success(list);
    }

    /**
     * 获取品牌列表
     *
     * @return
     */
    @PostMapping("/listBrand")
    public Response<List<BrandInfoResponse>> listBrand(@RequestBody BrandListRequest request) {
        UserInfoDto user = UserInfoThreadHolder.getUser();
        String blocCode = user.getBlocCode();
        Long userId = user.getUserId();
        Integer state = StateEnum.VALID.getValue();
        String tenantCode = user.getTenantCode();
        String trackingId = UserThreadHolder.getTrackingId();
        List<SelectBrandResp> listBrandResp = brandDecorator.listBrand(blocCode, state, request.getBrandName());
        if (CollectionUtils.isEmpty(listBrandResp)) {
            return Response.success(Collections.emptyList());
        }
        UserDivisionResp userDivisionResp = new UserDivisionResp();
        if (request.getIsSearchHavingPermissionFlag()) {
            userDivisionResp = userDataDecorator.listDivisionByUserId(blocCode, tenantCode, userId, trackingId);
        } else {
            userDivisionResp.setBrandCheckAll(true);
        }
        return Response.success(converter.convertBrandListResponses(listBrandResp, userDivisionResp));
    }

    /**
     * 获取全部区域
     */
    @PostMapping("/areaTree")
    public Response<List<AreaTreeResponse>> listAreaTree(@RequestBody AreaListRequest request) {
        UserInfoDto user = UserInfoThreadHolder.getUser();
        String blocCode = user.getBlocCode();
        Long userId = user.getUserId();
        Integer state = StateEnum.VALID.getValue();
        String tenantCode = user.getTenantCode();
        String trackingId = UserThreadHolder.getTrackingId();
        List<AreaTreeResp> listAreaTree = areaDecorator.listAreaTree(blocCode, 0L, state);
        if (CollectionUtils.isEmpty(listAreaTree)) {
            return Response.success(Collections.emptyList());
        }
        UserDivisionResp userDivisionResp = new UserDivisionResp();
        if (request.getIsSearchHavingPermissionFlag()) {
            userDivisionResp = userDataDecorator.listDivisionByUserId(blocCode, tenantCode, userId, trackingId);
        } else {
            userDivisionResp.setAreaCheckAll(true);
        }
        return Response.success(converter.convertAreaTreesResponses(listAreaTree, userDivisionResp));
    }

    /**
     * 获取省市区
     */
    @PostMapping("/regionTree")
    public Response<List<RegionTreeResponse>> listRegionTree(@RequestBody RegionListRequest request) {
        UserInfoDto user = UserInfoThreadHolder.getUser();
        String blocCode = user.getBlocCode();
        Long userId = user.getUserId();
        String tenantCode = user.getTenantCode();
        String trackingId = UserThreadHolder.getTrackingId();
        List<RegionResp> listRegionTree = regionDecorator.listRegionTree(Constant.YES, null);
        if (CollectionUtils.isEmpty(listRegionTree)) {
            return Response.success(Collections.emptyList());
        }
        UserDivisionResp userDivisionResp = new UserDivisionResp();
        if (request.getIsSearchHavingPermissionFlag()) {
            userDivisionResp = userDataDecorator.listDivisionByUserId(blocCode, tenantCode, userId, trackingId);
        } else {
            userDivisionResp.setRegionCheckAll(true);
        }
        return Response.success(converter.assembleRegionTree(listRegionTree, request.getLevel(), userDivisionResp));
    }


    /**
     * 获取当前集团下所有酒店拥有的省市区
     */
    @PostMapping("/regionHotelTree")
    public Response<List<RegionHotelTreeResponse>> listRegionHotelTree() {
        UserInfoDto user = UserInfoThreadHolder.getUser();
        String blocCode = user.getBlocCode();
        Long userId = user.getUserId();
        String tenantCode = user.getTenantCode();
        String trackingId = UserThreadHolder.getTrackingId();
        List<HotelBaseInfoResp> hotelBaseInfos = hotelDecorator.listHotelBaseInfos(blocCode, Collections.emptyList());
        List<HotelInfoResponse> hotelInfoResponses = converter.convertHotelInfoResponse(hotelBaseInfos,user);
        if(CollectionUtils.isEmpty(hotelInfoResponses)){
            return Response.success(Collections.emptyList());
        }
        List<RegionResp> listRegionTree = regionDecorator.listRegionTree(Constant.YES, null);
        if (CollectionUtils.isEmpty(listRegionTree)) {
            return Response.success(Collections.emptyList());
        }
        Map<Integer, List<HotelInfoResponse>> regionAndHotelInfoMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(hotelInfoResponses)) {
            regionAndHotelInfoMap = hotelInfoResponses.stream().collect(Collectors.groupingBy(HotelInfoResponse::getRegionId));
        }
        UserDivisionResp userDivisionResp = userDataDecorator.listDivisionByUserId(blocCode, tenantCode, userId, trackingId);
        return Response.success(converter.assembleRegionHotelTree(listRegionTree, regionAndHotelInfoMap, userDivisionResp));
    }


    /**
     * 获取当前用户拥有权限的酒店信息及省市区
     */
    @PostMapping("/hotelRegionTree")
    public Response<List<RegionHotelTreeResponse>> listHotelRegionTree() {
        UserInfoDto user = UserInfoThreadHolder.getUser();
        String blocCode = user.getBlocCode();
        String trackingId = UserThreadHolder.getTrackingId();
        if(!user.getIsAdmin() && CollectionUtils.isEmpty(user.getHotelCodes())){
            return Response.success(Collections.emptyList());
        }
        List<String> hotelCodes = Objects.equals(user.getIsAdmin(),Boolean.TRUE) ? Collections.emptyList() : user.getHotelCodes();
        List<HotelBaseInfoResp> hotelBaseInfos = hotelDecorator.listHotelBaseInfos(blocCode,hotelCodes);
        if(CollectionUtils.isEmpty(hotelBaseInfos)){
            return Response.success(Collections.emptyList());
        }
        List<HotelInfoResponse> hotelInfoResponses = converter.convertHotelInfoResponse(hotelBaseInfos,user);
        if(CollectionUtils.isEmpty(hotelInfoResponses)){
            return Response.success(Collections.emptyList());
        }
        List<RegionResp> listRegionTree = regionDecorator.listRegionTree(Constant.YES, null);
        if (CollectionUtils.isEmpty(listRegionTree)) {
            return Response.success(Collections.emptyList());
        }
        Map<Integer, List<HotelInfoResponse>> regionAndHotelInfoMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(hotelInfoResponses)) {
            regionAndHotelInfoMap = hotelInfoResponses.stream().collect(Collectors.groupingBy(HotelInfoResponse::getRegionId));
        }
        UserDivisionResp userDivisionResp = new UserDivisionResp();
        userDivisionResp.setRegionCheckAll(true);
        return Response.success(converter.assembleRegionHotelTree(listRegionTree, regionAndHotelInfoMap,userDivisionResp));
    }

}
