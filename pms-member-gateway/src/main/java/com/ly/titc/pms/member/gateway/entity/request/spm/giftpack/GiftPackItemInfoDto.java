package com.ly.titc.pms.member.gateway.entity.request.spm.giftpack;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @Description:
 * @Author: huaizhu.zhong
 * @Date: 2024-11-22
 */
@Data
public class GiftPackItemInfoDto implements Serializable {

    /**
     * 明细唯一码
     */
    private String uniqueCode;

    /**
     * 礼包明细类型 1-优惠券 2-会员卡 3-积分 4-会员权益 5-联名权益
     */
    @NotNull(message = "礼包明细类型不能为空")
    private Integer bizType;

    /**
     * 礼包明细业务编号
     */
    private String bizNo;

    /**
     * 礼包明细业务名称
     */
    private String bizName;

    /**
     * 明细数量
     */
    @NotNull(message = "礼包明细数量不能为空")
    private Integer num;
}
