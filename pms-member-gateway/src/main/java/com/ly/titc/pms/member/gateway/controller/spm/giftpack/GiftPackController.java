package com.ly.titc.pms.member.gateway.controller.spm.giftpack;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.fastjson2.JSON;
import com.google.common.collect.Lists;
import com.ly.titc.common.entity.Pageable;
import com.ly.titc.common.entity.Response;
import com.ly.titc.common.util.PageableUtil;
import com.ly.titc.pms.customer.dubbo.entity.response.customer.CustomerInfoResp;
import com.ly.titc.pms.member.dubbo.enums.MasterTypeEnum;
import com.ly.titc.pms.member.dubbo.enums.MemberStateEnum;
import com.ly.titc.pms.member.gateway.converter.GiftPackConverter;
import com.ly.titc.pms.member.gateway.entity.request.spm.giftpack.*;
import com.ly.titc.pms.member.gateway.entity.response.spm.giftpack.*;
import com.ly.titc.pms.member.gateway.thread.local.UserInfoThreadHolder;
import com.ly.titc.pms.member.mediator.entity.dto.member.MemberDetailDto;
import com.ly.titc.pms.member.mediator.entity.dto.user.UserInfoDto;
import com.ly.titc.pms.member.mediator.rpc.dubbo.customer.CustomerDecorator;
import com.ly.titc.pms.member.mediator.rpc.dubbo.giftpack.GiftPackDecorator;
import com.ly.titc.pms.member.mediator.rpc.dubbo.giftpack.GiftPackGrantDecorator;
import com.ly.titc.pms.member.mediator.service.MemberMedService;
import com.ly.titc.pms.spm.dubbo.entity.request.giftpack.*;
import com.ly.titc.pms.spm.dubbo.entity.response.giftpack.GiftPackBatchDetailResp;
import com.ly.titc.pms.spm.dubbo.entity.response.giftpack.GiftPackBatchInfoResp;
import com.ly.titc.pms.spm.dubbo.entity.response.giftpack.GiftPackInfoResp;
import com.ly.titc.pms.spm.dubbo.entity.response.giftpack.PageGiftPackInfoResp;
import com.ly.titc.pms.spm.dubbo.enums.ReceiverTypeEnum;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 礼包接口
 */
@Slf4j
@RestController
@RequestMapping("/giftPack")
public class GiftPackController {
    @Resource(type = CustomerDecorator.class)
    private CustomerDecorator customerDecorator;
    @Resource(type = GiftPackDecorator.class)
    private GiftPackDecorator giftPackDecorator;
    @Resource(type = GiftPackConverter.class)
    private GiftPackConverter giftPackConverter;
    @Resource(type = MemberMedService.class)
    private MemberMedService memberMedService;
    @Resource(type = GiftPackGrantDecorator.class)
    private GiftPackGrantDecorator giftPackGrantDecorator;


    /**
     * 保存礼包信息
     */
    @PostMapping("/save")
    public Response<String> saveGiftPackInfo(@Valid @RequestBody SaveGiftPackInfoRequest request) {
        SaveGiftPackInfoReq req = giftPackConverter.convertSaveGiftPackInfoReq(request);
        return Response.success(giftPackDecorator.save(req));
    }

    /**
     * 礼包列表
     */
    @PostMapping("/page/list")
    public Response<Pageable<PageGiftPackInfoResponse>> pageGiftPackInfos(@RequestBody PageGiftPackInfoRequest request) {
        PageGiftPackInfoReq req = giftPackConverter.convertPageGiftPackInfoReq(request);
        Pageable<PageGiftPackInfoResp> pageable = giftPackDecorator.pageInfos(req);
        return Response.success(Pageable.convert(pageable, giftPackConverter.convertPageGiftPackInfoResponses(pageable.getDatas())));
    }

    /**
     * 启用/停用
     */
    @PostMapping("/update/state")
    public Response<String> enableAndDisableGiftPackInfo(@Valid @RequestBody UpdateGiftPackStateRequest request) {
        UpdateGiftPackStateReq req = giftPackConverter.convertUpdateGiftPackStateReq(request);
        giftPackDecorator.updateState(req);
        return Response.success();
    }

    /**
     * 礼包详情
     */
    @PostMapping("/info")
    public Response<GiftPackInfoResponse> getGiftPackInfo(@Valid @RequestBody GetGiftPackInfoRequest request) {
        GetGiftPackInfoReq req = giftPackConverter.convertGetGiftPackInfoReq(request);
        GiftPackInfoResp resp = giftPackDecorator.getGiftPackInfo(req);
        return Response.success(giftPackConverter.convertGiftPackInfoResponse(resp));
    }

    /**
     * 礼包删除
     */
    @PostMapping("/delete")
    public Response<String> delete(@Valid @RequestBody DeleteGiftPackRequest request) {
        DeleteGiftPackReq req = giftPackConverter.convertDeleteGiftPackReq(request);
        giftPackDecorator.delete(req);
        return Response.success();
    }


//    /**
//     * 查询某业务编号触发的礼包奖励详情列表
//     */
//    @PostMapping("/queryBizGrantDetails")
//    public Response<List<GrantGiftPackDetailResponse>> queryBizGrantDetails(@Valid @RequestBody QueryGiftPackGrantDetailsRequest request) {
//        QueryGiftPackGrantDetailsReq req = giftPackConverter.convertQueryGrantDetailsReq(request);
//        req.setGiftPackNos(request.getGiftPackNos());
//        List<GiftPackBatchDetailListResp> grantGiftPackDetails = giftPackDecorator.queryBizGrantDetails(req);
//
//        List<GrantGiftPackDetailResponse> grantGiftPackDetailResponses = giftPackConverter.convertGrantGiftPackDetailResponses(grantGiftPackDetails);
//        return Response.success(grantGiftPackDetailResponses);
//    }

    /**
     * 优惠礼包发放(单个发放)
     */
    @PostMapping("/grant")
    public Response<Void> grantGiftPackByBind(@Valid @RequestBody GrantGiftPackRequest req) {
        GrantGiftPackReq request = giftPackConverter.convertGrantGiftPackReq(req);
        request.setReceiverInfos(Lists.newArrayList(giftPackConverter.convertReceiverInfo(req)));
        giftPackGrantDecorator.batchGrant(request);
        return Response.success();
    }


    /**
     * 优惠礼包发放(批量发放)
     */
    @SneakyThrows
    @PostMapping("/grant/import")
    public Response<Void> grantGiftPackByImport(@RequestParam(value = "giftPackNo") String giftPackNo,
                                                @RequestParam(value = "version") Integer version,
                                                @RequestParam(value = "grantNum") Integer grantNum,
                                                @RequestParam("file") MultipartFile file) {
        final int BATCH_COUNT = 200;
        EasyExcel.read(file.getInputStream(), GrantGiftPackMemberDto.class, new AnalysisEventListener<GrantGiftPackMemberDto>() {
            final List<GrantGiftPackMemberDto> grantGiftPackInfos = Lists.newArrayList();

            @Override
            public void invoke(GrantGiftPackMemberDto data, AnalysisContext analysisContext) {
                log.info("解析到【批量发放礼包模版】一条数据:{}", JSON.toJSONString(data));
                grantGiftPackInfos.add(data);
                if (grantGiftPackInfos.size() >= BATCH_COUNT) {
                    batchGrantGiftPack(giftPackNo, version, grantNum, grantGiftPackInfos);
                    grantGiftPackInfos.clear();
                }
            }
            @Override
            public void doAfterAllAnalysed(AnalysisContext analysisContext) {
                log.info("【批量发放礼包模版】解析完成");
                if (!CollectionUtils.isEmpty(grantGiftPackInfos)) {
                    batchGrantGiftPack(giftPackNo, version, grantNum, grantGiftPackInfos);
                }
            }
        }).headRowNumber(1).sheet(0).doRead();
        return Response.success();
    }


    /**
     * 礼包发放批次分页列表查询
     */
    @PostMapping("/batch/page/list")
    public Response<Pageable<GiftPackBatchListResponse>> grantBatchPageInfos(@Valid @RequestBody PageGiftPackBatchRequest request) {
        PageGiftPackBatchInfoReq req = giftPackConverter.convertPageGiftPackBatchInfoReq(request);
        if (StringUtils.isNotBlank(request.getMobile())) {
            List<MemberDetailDto> memberDetails = memberMedService.listByMobiles(MasterTypeEnum.BLOC.getType(),
                    request.getBlocCode(), Collections.singletonList(request.getMobile()), MemberStateEnum.VALID.getState());
            String memberNo = CollUtil.getFirst(memberDetails.listIterator()).getMemberNo();
            req.setReceiverCode(memberNo);
        }
        Pageable<GiftPackBatchInfoResp> pageable = giftPackGrantDecorator.pageBatchInfo(req);
        return Response.success(PageableUtil.convert(pageable, d -> giftPackConverter.convertGiftPackBatchListResponse(d)));
    }


    /**
     * 礼包发放详情分页列表
     */
    @PostMapping("/batch/detail/page/list")
    public Response<Pageable<GiftPackBatchDetailResponse>> pageBatchDetail(@Valid @RequestBody PageGiftPackBatchDetailRequest request) {
        return Response.success(this.buildGiftPackBatchDetailResponse(request));
    }

    /**
     * 礼包发放详情列表导出
     */
    @SneakyThrows
    @PostMapping("/exportBatchDetails")
    public Response<Void> exportBatchDetails(@Valid @RequestBody PageGiftPackBatchDetailRequest request, HttpServletResponse response) {
        request.setPageSize(Integer.MAX_VALUE);
        request.setBlocCode(UserInfoThreadHolder.getUser().getBlocCode());
        Pageable<GiftPackBatchDetailResponse> pageable  = this.buildGiftPackBatchDetailResponse(request);
        log.info("礼包发放批次详细列表导出:{}", JSON.toJSONString(pageable.getDatas()));
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode("礼包发放详情", "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
        response.setHeader("Access-Control-Expose-Headers", "Content-Disposition,x-access-titc-c-token");
        List<GiftPackBatchDetailExportResponse> ans = giftPackConverter.convertGiftPackBatchDetailExportResponses(pageable.getDatas());
        EasyExcel.write(response.getOutputStream(), GiftPackBatchDetailExportResponse.class)
                .sheet("礼包发放")
                .doWrite(ans);
        return Response.success();
    }

    /**
     * 礼包批次作废
     */
    @PostMapping("/invalidBatch")
    public Response<Void> invalidBatch(@Valid @RequestBody GetBatchInfoRequest request) {
        GetBatchInfoReq req = giftPackConverter.convertGetBatchInfoReq(request);
        giftPackGrantDecorator.invalidBatch(req);
        return Response.success();
    }

    /**
     * 礼包批次删除
     */
    @PostMapping("/deleteBatch")
    public Response<Void> deleteBatch(@Valid @RequestBody GetBatchInfoRequest request) {
        GetBatchInfoReq req = giftPackConverter.convertGetBatchInfoReq(request);
        giftPackGrantDecorator.deleteBatch(req);
        return Response.success();
    }

    private void batchGrantGiftPack(String giftPackNo, Integer version, Integer grantNum, List<GrantGiftPackMemberDto> members) {
        UserInfoDto user = UserInfoThreadHolder.getUser();
        List<String> memberNos = new ArrayList<>();
        List<String> mobileNos = new ArrayList<>();
        members.forEach(memberDto -> {
            if (StringUtils.isNotBlank(memberDto.getMemberNo())) {
                memberNos.add(memberDto.getMemberNo());
            } else if (StringUtils.isNotBlank(memberDto.getMobile())) {
                mobileNos.add(memberDto.getMobile());
            }
        });
        if (CollectionUtils.isNotEmpty(mobileNos)) {
            memberNos.addAll(memberMedService.listByMobiles(MasterTypeEnum.BLOC.getType(), user.getBlocCode(), mobileNos,
                    MemberStateEnum.VALID.getState()).stream().map(MemberDetailDto::getMemberNo).collect(Collectors.toList()));
        }
        GrantGiftPackReq request = giftPackConverter.convertGrantGiftPackReq(user.getBlocCode(), user.getUserName(), giftPackNo, version, grantNum);
        request.setReceiverInfos(
                memberNos.stream().map(memberNo -> giftPackConverter.convertReceiverInfo(ReceiverTypeEnum.MEMBER.getCode(), memberNo, "", grantNum))
                        .collect(Collectors.toList())
        );
        giftPackGrantDecorator.batchGrant(request);
    }

    private Pageable<GiftPackBatchDetailResponse> buildGiftPackBatchDetailResponse(PageGiftPackBatchDetailRequest request){
        PageGiftPackGrantDetailReq req = giftPackConverter.convertPageGiftPackGrantDetailReq(request);
        Pageable<GiftPackBatchDetailResp> pageable = giftPackGrantDecorator.pageGrantDetailInfo(req);
        List<String> memberNos = new ArrayList<>();
        List<String> customerNos = new ArrayList<>();
        pageable.getDatas().forEach(item -> {
            if (ReceiverTypeEnum.MEMBER.getCode().equals(item.getReceiverType())) {
                memberNos.add(item.getReceiverCode());
            }
            if (ReceiverTypeEnum.CUSTOMER.getCode().equals(item.getReceiverType())) {
                customerNos.add(item.getReceiverCode());
            }
        });
        List<MemberDetailDto> memberDetails = new ArrayList<>();
        List<CustomerInfoResp> customerInfos = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(memberNos)) {
            memberDetails = memberMedService.listByMemberNos(MasterTypeEnum.BLOC.getType(), req.getBlocCode(), memberNos);
        }
        if (CollectionUtils.isNotEmpty(customerNos)) {
            customerInfos = customerDecorator.listByCustomerNos(req.getBlocCode(), customerNos);
        }
        Map<String, MemberDetailDto> memberDetailMap = memberDetails.stream().collect(Collectors.toMap(
                MemberDetailDto::getMemberNo, Function.identity(), (v1, v2) -> v1));
        Map<String, CustomerInfoResp> customerInfoMap = customerInfos.stream().collect(Collectors.toMap(
                CustomerInfoResp::getCustomerNo, Function.identity(), (v1, v2) -> v1));
        return PageableUtil.convert(pageable, resp -> giftPackConverter.buildGiftPackBatchDetailResponse(resp, memberDetailMap, customerInfoMap));
    }
}
