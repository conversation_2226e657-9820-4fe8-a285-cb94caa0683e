package com.ly.titc.pms.member.gateway.controller.spm.activity;

import com.ly.titc.common.entity.Pageable;
import com.ly.titc.common.entity.Response;
import com.ly.titc.common.util.PageableUtil;
import com.ly.titc.pms.member.gateway.converter.MemberActivityConverter;
import com.ly.titc.pms.member.gateway.entity.request.spm.activity.*;
import com.ly.titc.pms.member.gateway.entity.response.spm.activity.*;
import com.ly.titc.pms.member.mediator.rpc.dubbo.activity.MemberActivityDecorator;
import com.ly.titc.pms.spm.dubbo.entity.request.activity.member.*;
import com.ly.titc.pms.spm.dubbo.entity.response.activity.member.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.Objects;

/**
 * 营销活动相关 售卡活动，升级活动 储值活动，积分活动
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-12-17 19:45
 */
@Slf4j
@RestController
@RequestMapping("/activity")
public class MemberActivityController {
    @Resource(type = MemberActivityDecorator.class)
    private MemberActivityDecorator memberActivityDecorator;
    @Resource(type = MemberActivityConverter.class)
    private MemberActivityConverter memberActivityConverter;


    /**
     * 分页查询
     */
    @PostMapping("/page")
    public Response<Pageable<MemberActivityInfoResponse>> page(@RequestBody @Valid PageActivityRequest request) {
        PageMemberActivityReq req = memberActivityConverter.convertPageMemberActivityReq(request);
        Pageable<MemberActivityBaseResp> pageable = memberActivityDecorator.pageMemberActivity(req);
        return Response.success(PageableUtil.convert(pageable,memberActivityConverter.convertMemberActivityInfoResponse(pageable.getDatas())));
    }

    /**
     * 新增售卡活动
     */
    @PostMapping("/addSaleCardActivity")
    public Response<String> addSaleCardActivity(@RequestBody @Valid SaveSaleCardActivityRequest request) {
        SaveSaleCardActivityReq req = memberActivityConverter.buildSaleCardActivityReq(request);
        return Response.success(memberActivityDecorator.saveSaleCardActivity(req));
    }

    /**
     * 新增升级活动
     */
    @PostMapping("/addUpgradeActivity")
    public Response<String> addUpgradeActivity(@RequestBody @Valid SaveUpgradeActivityRequest request) {
        SaveUpgradeActivityReq req = memberActivityConverter.buildUpgradeActivityReq(request);
        return Response.success(memberActivityDecorator.saveUpgradeActivity(req));
    }

    /**
     * 新增储值活动
     */
    @PostMapping("/addRechargeActivity")
    public Response<String> addRechargeActivity(@RequestBody @Valid SaveRechargeActivityRequest request) {
        SaveRechargeActivityReq req = memberActivityConverter.buildRechargeActivityReq(request);
        return Response.success(memberActivityDecorator.saveRechargeActivity(req));
    }

    /**
     * 新增积分活动
     */
    @PostMapping("/addPointsActivity")
    public Response<Boolean> addPointsActivity(@RequestBody @Valid SavePointsActivityRequest request) {
        SavePointsActivityReq req = memberActivityConverter.buildSavePointsActivityReq(request);
        return Response.success(memberActivityDecorator.savePointsActivity(req));
    }

    /**
     * 编辑售卡活动
     */
    @PostMapping("/saveSaleCardActivity")
    public Response<String> saveSaleCardActivity(@RequestBody @Valid SaveSaleCardActivityRequest request) {
        SaveSaleCardActivityReq req = memberActivityConverter.buildSaleCardActivityReq(request);
        return Response.success(memberActivityDecorator.saveSaleCardActivity(req));
    }

    /**
     * 编辑升级活动
     */
    @PostMapping("/saveUpgradeActivity")
    public Response<String> saveUpgradeActivity(@RequestBody @Valid SaveUpgradeActivityRequest request) {
        SaveUpgradeActivityReq req = memberActivityConverter.buildUpgradeActivityReq(request);
        return Response.success(memberActivityDecorator.saveUpgradeActivity(req));
    }

    /**
     * 编辑储值活动
     */
    @PostMapping("/saveRechargeActivity")
    public Response<String> saveRechargeActivity(@RequestBody @Valid SaveRechargeActivityRequest request) {
        SaveRechargeActivityReq req = memberActivityConverter.buildRechargeActivityReq(request);
        return Response.success(memberActivityDecorator.saveRechargeActivity(req));
    }

    /**
     * 编辑积分活动
     */
    @PostMapping("/savePointsActivity")
    public Response<Boolean> savePointsActivity(@RequestBody @Valid SavePointsActivityRequest request) {
        SavePointsActivityReq req = memberActivityConverter.buildSavePointsActivityReq(request);
        return Response.success(memberActivityDecorator.savePointsActivity(req));
    }

    /**
     * 查看售卡活动详情
     */
    @PostMapping("/getSaleCardActivityDetail")
    public Response<MemberSaleCardActivityDetailResponse> getSaleCardActivityDetail(@RequestBody @Valid GetActivityRequest request) {
        GetMemberActivityReq req = memberActivityConverter.convertGetMemberActivityReq(request);
        MemberCardSaleActivityDetailResp activity = memberActivityDecorator.getSaleCardActivity(req);
        return Response.success(memberActivityConverter.buildSaleCardActivityResp(activity));
    }

    /**
     * 查询集团下可售卡
     */
    @PostMapping("/listSellableCard")
    public Response<List<MemberSellableCardResponse>> listSellableCard(@RequestBody @Valid QuerySellableCardRequest request){
        GetSellableCardReq req = memberActivityConverter.convertGetSellableCardReq(request);
        List<MemberSellableCardResp> cardList = memberActivityDecorator.listSellableCard(req);
        return Response.success(memberActivityConverter.convertMemberSellableCardResponseList(cardList));
    }

    /**
     * 查询命中的售卡活动
     */
    @PostMapping("/getSaleCardActiveActivityDetail")
    public Response<MemberSaleCardActivityDetailResponse> getSaleCardActiveActivityDetail(@RequestBody @Valid QueryMemberActiveActivityRequest request) {
        GetActiveMemberActivityReq req = memberActivityConverter.convertGetActiveMemberActivityReq(request);
        MemberCardSaleActivityDetailResp resp = memberActivityDecorator.getActiveSaleCardActivity(req);
        if(Objects.isNull(resp)){
            return Response.success(null);
        }
        return Response.success(memberActivityConverter.buildSaleCardActivityResp(resp));
    }

    /**
     * 查看升级活动详情
     */
    @PostMapping("/getUpgradeActivityDetail")
    public Response<MemberUpgradeActivityDetailResponse> getUpgradeActivityDetail(@RequestBody @Valid GetActivityRequest request) {
        GetMemberActivityReq req = memberActivityConverter.convertGetMemberActivityReq(request);
        MemberUpgradeActivityDetailResp activity = memberActivityDecorator.getUpgradeActivity(req);
        return Response.success(memberActivityConverter.buildUpgradeActivityResp(activity));
    }

    /**
     * 查询命中的升级活动详情
     */
    @PostMapping("/getUpgradeActiveActivityDetail")
    public Response<MemberUpgradeActivityDetailResponse> getUpgradeActiveActivityDetail(@RequestBody @Valid QueryMemberActiveActivityRequest request) {
        GetActiveMemberActivityReq req = memberActivityConverter.convertGetActiveMemberActivityReq(request);
        MemberUpgradeActivityDetailResp resp = memberActivityDecorator.getActiveUpgradeActivity(req);
        if(Objects.isNull(resp)){
            return Response.success(null);
        }
        return Response.success(memberActivityConverter.buildUpgradeActivityResp(resp));
    }

    /**
     * 查询储值充值活动详情
     */
    @PostMapping("/getRechargeActivityDetail")
    public Response<MemberRechargeActivityDetailResponse> getRechargeActivityDetail(@RequestBody @Valid GetActivityRequest request){
        GetMemberActivityReq req = memberActivityConverter.convertGetMemberActivityReq(request);
        MemberRechargeActivityDetailResp activity = memberActivityDecorator.getRechargeActivity(req);
        return Response.success(memberActivityConverter.buildRechargeActivityResp(activity));
    }

    /**
     * 查询命中的储值充值活动详情
     */
    @PostMapping("/getRechargeActiveActivityDetail")
    public Response<MemberRechargeActivityDetailResponse> getRechargeActiveActivityDetail(@RequestBody @Valid QueryMemberActiveActivityRequest request) {
        GetActiveMemberActivityReq req = memberActivityConverter.convertGetActiveMemberActivityReq(request);
        MemberRechargeActivityDetailResp resp = memberActivityDecorator.getActiveRechargeActivity(req);
        if(Objects.isNull(resp)){
            return Response.success(null);
        }
        return Response.success(memberActivityConverter.buildRechargeActivityResp(resp));
    }

    /**
     * 查询积分活动详情
     */
    @PostMapping("/getPointsActivityDetail")
    public Response<MemberPointsActivityDetailResponse> getPointsActivityDetail(@RequestBody @Valid GetActivityRequest request){
        GetMemberActivityReq req = memberActivityConverter.convertGetMemberActivityReq(request);
        MemberPointActivityDetailResp activity = memberActivityDecorator.getPointsActivity(req);
        return Response.success(memberActivityConverter.buildPointsActivityResp(activity));
    }

    /**
     * 查询命中的积分活动详情
     */
    @PostMapping("/getPointsActiveActivity")
    public Response<MemberUpgradeActivityDetailResponse> getPointsActiveActivity(@RequestBody @Valid QueryMemberActiveActivityRequest request) {
        GetActiveMemberActivityReq req = memberActivityConverter.convertGetActiveMemberActivityReq(request);
        MemberPointActivityDetailResp resp = memberActivityDecorator.getActivePointsActivity(req);
        if(Objects.isNull(resp)){
            return Response.success(null);
        }
        return Response.success(memberActivityConverter.buildPointsActivityResp(resp));
    }


    /**
     * 停用
     */
    @PostMapping("/disable")
    public Response<Boolean> disableEventActivity(@RequestBody @Valid GetActivityRequest request){
        memberActivityDecorator.disable(memberActivityConverter.convertUpdateActivityStateReq(request));
        return Response.success(Boolean.TRUE);
    }


    /**
     * 启用
     */
    @PostMapping("/enable")
    public Response<Boolean> enableEventActivity(@RequestBody @Valid GetActivityRequest request){
        memberActivityDecorator.enable(memberActivityConverter.convertUpdateActivityStateReq(request));
        return Response.success(Boolean.TRUE);
    }

}
