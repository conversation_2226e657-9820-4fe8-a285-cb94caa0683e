package com.ly.titc.pms.member.gateway.converter;

import com.alibaba.fastjson.JSON;
import com.ly.titc.pms.ecrm.dubbo.entity.response.member.MemberRelatedConfigResp;
import com.ly.titc.pms.member.com.converter.BaseConverter;
import com.ly.titc.pms.member.dal.entity.po.MemberContactInfo;
import com.ly.titc.pms.member.dal.entity.po.MemberExtendInfo;
import com.ly.titc.pms.member.dal.entity.po.MemberInfo;
import com.ly.titc.pms.member.gateway.entity.request.member.*;
import com.ly.titc.pms.member.gateway.entity.response.member.MemberRelatedConfigResponse;
import com.ly.titc.pms.member.gateway.entity.response.member.RegisterMemberResultResponse;
import com.ly.titc.pms.member.gateway.entity.response.member.MemberCardInfoResponse;
import com.ly.titc.pms.member.gateway.entity.response.member.MemberContactResponse;
import com.ly.titc.pms.member.gateway.entity.response.member.MemberResponse;
import com.ly.titc.pms.member.mediator.entity.dto.member.*;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.util.List;

/**
 * @Author：rui
 * @name：MemberConverter
 * @Date：2024-11-18 17:26
 * @Filename：MemberConverter
 */
@Mapper(componentModel = "spring")
public interface MemberConverter extends BaseConverter {

    @Mappings({
            @Mapping(target = "masterType", source = "masterType"),
            @Mapping(target = "masterCode", source = "masterCode"),
    })
    PageMemberParamDto convertPageMemberParamBO(PageMemberRequest request, Integer masterType, String masterCode);

    List<MemberResponse> convertMemberResponseList(List<MemberDetailDto> dtoList);


    RegisterMemberDto convertRegisterMemberDto(MemberRegisterRequest request);

    @Mappings({
            @Mapping(target = "memberContactInfo", source = "memberContactInfo"),
            @Mapping(target = "memberCardInfos", source = "memberCardInfos")
    })
    MemberResponse convertMemberResponse(MemberDetailDto dto);

    MemberContactResponse convertMemberResponse(MemberContactInfoDto dto);

    MemberCardInfoResponse convertMemberResponse(MemberCardInfoDto dto);

    default UpdateMemberInfoDto convertUpdateMemberInfoDto(SaveMemberRequest request) {
        UpdateMemberInfoDto dto = new UpdateMemberInfoDto();
        dto.setMemberInfo(convertMemberInfoDto(request.getMemberInfo(), request.getOperator()));
        dto.setMemberExtendInfo(convertMemberExtendInfo(request.getMemberExtendInfo(), request.getOperator()));
        dto.setMemberContactInfo(convertMemberContactInfo(request.getMemberContactInfo(), request.getOperator()));
        return dto;
    }

    @Mapping(target = "modifyUser", source = "operator")
    MemberInfo convertMemberInfoDto(MemberInfoRequest request, String operator);

    MemberExtendInfo convertMemberExtendInfo(MemberExtendInfoRequest request, String operator);

    MemberContactInfo convertMemberContactInfo(MemberContactInfoRequest request, String operator);

    @Mapping(target = "modifyUser", source = "operator")
    MemberCardInfoDto convertMemberCardInfoDto(MemberCardInfoRequest request, String operator);

    RegisterMemberResultResponse convertRegisterMemberResultResponse(RegisterMemberResultDto dto);

    default MemberRelatedConfigResponse convertMemberRegisterCheckConfigResponse(MemberRelatedConfigResp resp) {
        if (resp == null) {
            return null;
        }

        MemberRelatedConfigResponse memberRegisterCheckConfigResponse = new MemberRelatedConfigResponse();

        if (resp.getId() != null) {
            memberRegisterCheckConfigResponse.setId(resp.getId());
        }
        memberRegisterCheckConfigResponse.setMasterType(resp.getMasterType());
        memberRegisterCheckConfigResponse.setMasterCode(resp.getMasterCode());
        memberRegisterCheckConfigResponse.setParams(JSON.parseArray(resp.getContent(), String.class));
        if (StringUtils.isNotEmpty(resp.getContent())) {
            memberRegisterCheckConfigResponse.setType(resp.getType());
        }
        memberRegisterCheckConfigResponse.setCreateUser(resp.getCreateUser());
        memberRegisterCheckConfigResponse.setModifyUser(resp.getModifyUser());
        memberRegisterCheckConfigResponse.setGmtCreate(localDatetime2String(resp.getGmtCreate()));
        memberRegisterCheckConfigResponse.setGmtModified(localDatetime2String(resp.getGmtModified()));

        return memberRegisterCheckConfigResponse;
    }

    MemberResponse convertMemberResponse(MemberInfoDto dto);

}
