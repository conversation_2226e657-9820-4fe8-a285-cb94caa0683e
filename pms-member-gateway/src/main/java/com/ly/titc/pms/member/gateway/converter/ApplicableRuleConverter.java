package com.ly.titc.pms.member.gateway.converter;

import com.ly.titc.pms.member.com.converter.BaseConverter;
import com.ly.titc.pms.member.gateway.entity.request.spm.activity.dto.ApplicableMemberLevelDto;
import com.ly.titc.pms.member.gateway.entity.response.spm.ApplicableHotelInfoResponse;
import com.ly.titc.pms.member.mediator.entity.dto.user.UserInfoDto;
import com.ly.titc.pms.spm.dubbo.entity.dto.rule.use.applicable.ApplicableHotelsDto;
import com.ly.titc.pms.spm.dubbo.entity.dto.rule.use.applicable.ApplicableRuleDataInfoDto;
import com.ly.titc.pms.spm.dubbo.entity.dto.rule.use.applicable.ApplicableRuleInfoDto;
import com.ly.titc.pms.spm.dubbo.entity.dto.rule.use.applicable.CommonInfoDto;
import com.ly.titc.pms.spm.dubbo.enums.ApplicableRuleTypeEnum;
import com.ly.titc.pms.spm.dubbo.enums.ApplicableScopeTypeEnum;
import com.ly.titc.pms.spm.dubbo.enums.MasterTypeEnum;
import org.apache.logging.log4j.util.Strings;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Description:
 * @Author: huaizhu.zhong
 * @Date: 2025-3-12
 */
public interface ApplicableRuleConverter extends BaseConverter {


    default ApplicableRuleInfoDto<ApplicableRuleDataInfoDto> convertApplicableHotelInfo(ApplicableHotelsDto applicableHotelInfoDto,
                                                                                        UserInfoDto userInfoDto) {
        if(Objects.isNull(applicableHotelInfoDto)){
            return null;
        }
        ApplicableRuleInfoDto<ApplicableRuleDataInfoDto> hotelApplicableInfo = new ApplicableRuleInfoDto<>();
        hotelApplicableInfo.setApplicableParentRuleType(ApplicableRuleTypeEnum.HOTEL.getParentType());
        String applicableScopeType = applicableHotelInfoDto.getAllHotels() ? ApplicableScopeTypeEnum.ALL.getScopeType()
                : ApplicableScopeTypeEnum.APPOINT_AVAILABLE.getScopeType();
        hotelApplicableInfo.setApplicableScopeType(applicableScopeType);
        if (Objects.equals(applicableScopeType, ApplicableScopeTypeEnum.ALL.getScopeType())) {
            return hotelApplicableInfo;
        }
        List<ApplicableRuleDataInfoDto> applicableRuleDataInfos = new ArrayList<>();
        if (!CollectionUtils.isEmpty(applicableHotelInfoDto.getBrandLabels())) {
            applicableRuleDataInfos.add(buildApplicableRuleDataInfoDto(MasterTypeEnum.BLOC.getCode(),userInfoDto.getBlocCode(),
                    ApplicableScopeTypeEnum.APPOINT_AVAILABLE.getScopeType(),ApplicableRuleTypeEnum.BRAND.getRuleType(),applicableHotelInfoDto.getBrandLabels()));
        }
        if (!CollectionUtils.isEmpty(applicableHotelInfoDto.getAreaLabels())) {
            applicableRuleDataInfos.add(buildApplicableRuleDataInfoDto(MasterTypeEnum.BLOC.getCode(),userInfoDto.getBlocCode(),
                    ApplicableScopeTypeEnum.APPOINT_AVAILABLE.getScopeType(),ApplicableRuleTypeEnum.AREA.getRuleType(),applicableHotelInfoDto.getAreaLabels()));
        }
        if (!CollectionUtils.isEmpty(applicableHotelInfoDto.getRegionLabels())) {
            applicableRuleDataInfos.add(buildApplicableRuleDataInfoDto(MasterTypeEnum.BLOC.getCode(),userInfoDto.getBlocCode(),
                    ApplicableScopeTypeEnum.APPOINT_AVAILABLE.getScopeType(),ApplicableRuleTypeEnum.REGION.getRuleType(),applicableHotelInfoDto.getRegionLabels()));
        }
        if (!CollectionUtils.isEmpty(applicableHotelInfoDto.getHotelCodes())) {
            List<CommonInfoDto> commonInfos = applicableHotelInfoDto.getHotelCodes().stream().map(hotelCode -> buildCommonInfoDto(hotelCode, Strings.EMPTY)).collect(Collectors.toList());
            applicableRuleDataInfos.add(buildApplicableRuleDataInfoDto(MasterTypeEnum.BLOC.getCode(),userInfoDto.getBlocCode(),
                    ApplicableScopeTypeEnum.APPOINT_AVAILABLE.getScopeType(),ApplicableRuleTypeEnum.HOTEL.getRuleType(),commonInfos));
        }
        hotelApplicableInfo.setApplicableRuleDataInfo(applicableRuleDataInfos);
        return hotelApplicableInfo;
    }

    default ApplicableRuleDataInfoDto buildApplicableRuleDataInfoDto(Integer masterType,String masterCode,String applicableScopeType,
                                                                     String applicableRuleType,List<CommonInfoDto> commonInfos) {
        ApplicableRuleDataInfoDto applicableRuleDataInfoDto = new ApplicableRuleDataInfoDto();
        applicableRuleDataInfoDto.setMasterType(masterType);
        applicableRuleDataInfoDto.setMasterCode(masterCode);
        applicableRuleDataInfoDto.setApplicableRuleType(applicableRuleType);
        applicableRuleDataInfoDto.setApplicableScopeType(applicableScopeType);
        applicableRuleDataInfoDto.setBizData(commonInfos);
        return applicableRuleDataInfoDto;
    }

    default CommonInfoDto buildCommonInfoDto(String code,String name) {
        CommonInfoDto commonInfoDto = new CommonInfoDto();
        commonInfoDto.setCode(code);
        commonInfoDto.setName(name);
        return commonInfoDto;
    }

    default ApplicableHotelInfoResponse buildApplicableHotelInfoResponse(ApplicableRuleInfoDto<ApplicableRuleDataInfoDto> hotelRuleInfo) {
        ApplicableHotelInfoResponse response = new ApplicableHotelInfoResponse();
        if(Objects.isNull(hotelRuleInfo)){
            return response;
        }
        if (Objects.equals(hotelRuleInfo.getApplicableScopeType(), ApplicableScopeTypeEnum.ALL.getScopeType())) {
            response.setAllHotels(Boolean.TRUE);
            return response;
        }
        response.setAllHotels(Boolean.FALSE);
        List<ApplicableRuleDataInfoDto> applicableRuleDataInfo = hotelRuleInfo.getApplicableRuleDataInfo();
        applicableRuleDataInfo.stream().parallel().forEach(ruleDataInfo -> {
            List<CommonInfoDto> dataInfo = ruleDataInfo.getBizData();
            if (Objects.equals(ruleDataInfo.getApplicableRuleType(), ApplicableRuleTypeEnum.BRAND.getRuleType())) {
                response.setBrandInfos(dataInfo);
            } else if (Objects.equals(ruleDataInfo.getApplicableRuleType(), ApplicableRuleTypeEnum.AREA.getRuleType())) {
                response.setAreaInfos(dataInfo);
            } else if (Objects.equals(ruleDataInfo.getApplicableRuleType(), ApplicableRuleTypeEnum.REGION.getRuleType())) {
                response.setRegionInfos(dataInfo);
            } else if (Objects.equals(ruleDataInfo.getApplicableRuleType(), ApplicableRuleTypeEnum.HOTEL.getRuleType())) {
                response.setHotelInfos(dataInfo);
            }
        });
        return response;
    }

    default ApplicableHotelsDto buildApplicableHotelsDto(ApplicableRuleInfoDto<ApplicableRuleDataInfoDto> hotelRuleInfo) {
        ApplicableHotelsDto hotelInfo = new ApplicableHotelsDto();
        if (Objects.equals(hotelRuleInfo.getApplicableScopeType(), ApplicableScopeTypeEnum.ALL.getScopeType())) {
            hotelInfo.setAllHotels(Boolean.TRUE);
        } else {
            hotelInfo.setAllHotels(Boolean.FALSE);
            List<ApplicableRuleDataInfoDto> applicableRuleDataInfo = hotelRuleInfo.getApplicableRuleDataInfo();
            applicableRuleDataInfo.stream().parallel().forEach(ruleDataInfo -> {
                if (Objects.equals(ruleDataInfo.getApplicableRuleType(), ApplicableRuleTypeEnum.BRAND.getRuleType())) {
                    hotelInfo.setBrandLabels(ruleDataInfo.getBizData());
                } else if (Objects.equals(ruleDataInfo.getApplicableRuleType(), ApplicableRuleTypeEnum.AREA.getRuleType())) {
                    hotelInfo.setAreaLabels(ruleDataInfo.getBizData());
                } else if (Objects.equals(ruleDataInfo.getApplicableRuleType(), ApplicableRuleTypeEnum.REGION.getRuleType())) {
                    hotelInfo.setRegionLabels(ruleDataInfo.getBizData());
                } else if (Objects.equals(ruleDataInfo.getApplicableRuleType(), ApplicableRuleTypeEnum.HOTEL.getRuleType())) {
                    hotelInfo.setHotelCodes(ruleDataInfo.getBizData().stream().map(CommonInfoDto::getCode).collect(Collectors.toList()));
                }
            });
        }
       return hotelInfo;
    }

    default ApplicableRuleInfoDto<CommonInfoDto> convertApplicableChannelRuleInfo(List<CommonInfoDto> applicablePlatformChannels){
        if(!CollectionUtils.isEmpty(applicablePlatformChannels)){
            ApplicableRuleInfoDto<CommonInfoDto> channelApplicableRuleInfo = new ApplicableRuleInfoDto<>();
            channelApplicableRuleInfo.setApplicableParentRuleType(ApplicableRuleTypeEnum.CHANNEL.getParentType());
            channelApplicableRuleInfo.setApplicableScopeType(ApplicableScopeTypeEnum.APPOINT_AVAILABLE.getScopeType());
            channelApplicableRuleInfo.setApplicableRuleDataInfo(applicablePlatformChannels);
            return channelApplicableRuleInfo;
        }
        return null;
    }

    default ApplicableRuleInfoDto<CommonInfoDto> convertApplicableMemberBaseLevelRuleInfo(ApplicableMemberLevelDto applicableMemberLevelDto){
        if(Objects.isNull(applicableMemberLevelDto)){
            return null;
        }
        ApplicableRuleInfoDto<CommonInfoDto> applicableRuleInfoDto = new ApplicableRuleInfoDto<>();
        applicableRuleInfoDto.setApplicableParentRuleType(ApplicableRuleTypeEnum.MEMBER_BASE_LEVEL.getParentType());
        applicableRuleInfoDto.setApplicableScopeType(applicableMemberLevelDto.getApplicableScopeType());
        if(!CollectionUtils.isEmpty(applicableMemberLevelDto.getMemberLevelCodes())){
            applicableRuleInfoDto.setApplicableRuleDataInfo(
                    applicableMemberLevelDto.getMemberLevelCodes().stream().map(code-> buildCommonInfoDto(code,Strings.EMPTY)).collect(Collectors.toList())
            );
        }
        return applicableRuleInfoDto;
    }


    default ApplicableRuleInfoDto<CommonInfoDto> convertApplicableRentModeRuleInfo(List<String> rentModeCodes){
        if(CollectionUtils.isEmpty(rentModeCodes)){
            return null;
        }
        ApplicableRuleInfoDto<CommonInfoDto> applicableRuleInfoDto = new ApplicableRuleInfoDto<>();
        applicableRuleInfoDto.setApplicableParentRuleType(ApplicableRuleTypeEnum.RENT_MODE.getParentType());
        applicableRuleInfoDto.setApplicableScopeType(ApplicableScopeTypeEnum.APPOINT_AVAILABLE.getScopeType());
        if(!CollectionUtils.isEmpty(rentModeCodes)){
            applicableRuleInfoDto.setApplicableRuleDataInfo(
                    rentModeCodes.stream().map(code-> buildCommonInfoDto(code,Strings.EMPTY)).collect(Collectors.toList())
            );
        }
        return applicableRuleInfoDto;
    }

    default ApplicableRuleInfoDto<CommonInfoDto> convertApplicableUserTypesRuleInfo(List<String> applicableUserTypes){
        if(CollectionUtils.isEmpty(applicableUserTypes)){
            return null;
        }
        ApplicableRuleInfoDto<CommonInfoDto> applicableRuleInfoDto = new ApplicableRuleInfoDto<>();
        applicableRuleInfoDto.setApplicableParentRuleType(ApplicableRuleTypeEnum.USER_TYPE.getParentType());
        applicableRuleInfoDto.setApplicableScopeType(ApplicableScopeTypeEnum.APPOINT_AVAILABLE.getScopeType());
        if(!CollectionUtils.isEmpty(applicableUserTypes)){
            applicableRuleInfoDto.setApplicableRuleDataInfo(
                    applicableUserTypes.stream().map(code-> buildCommonInfoDto(code,Strings.EMPTY)).collect(Collectors.toList())
            );
        }
        return applicableRuleInfoDto;
    }

}
