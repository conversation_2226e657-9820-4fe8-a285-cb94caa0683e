package com.ly.titc.pms.member.gateway.entity.request.spm.activity;

import com.ly.titc.pms.member.gateway.entity.request.BaseRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;


/**
 * @Description
 * <AUTHOR>
 * @Date 2024-12-16 11:41
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class QueryMemberActiveActivityRequest extends BaseRequest {

    /**
     * 当前用户是否是会员
     */
    @NotNull(message = "当前用户是否是会员不能为空")
    private Boolean isMember;

    /**
     * 会员号
     */
    private String memberNo;

    /**
     * 会员卡ID
     */
    private String cardId;

    /**
     * 当前用户会员等级
     */
    private String memberLevel;
}
