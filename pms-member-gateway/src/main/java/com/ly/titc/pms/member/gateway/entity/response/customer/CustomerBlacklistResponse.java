package com.ly.titc.pms.member.gateway.entity.response.customer;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ly.titc.pms.member.dubbo.entity.response.DictResp;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 客户黑名单信息
 *
 * <AUTHOR>
 * @date 2024/12/13 15:04
 */
@Data
public class CustomerBlacklistResponse {

    /**
     * 集团编号
     */
    private String blocCode;

    /**
     * 酒店编号
     */
    private String hotelCode;

    /**
     * 黑名单编号
     */
    private String blacklistNo;

    /**
     * 客户编号
     */
    private String customerNo;

    /**
     * 场景适用范围 0 部分 1 全部
     */
    private Integer sceneScope;

    /**
     * 适用场景列表
     */
    private List<DictResp> scenes;

    /**
     * 平台渠道
     */
    private List<DictResp> platformChannels;

    /**
     * 原因
     */
    private String reason;

    /**
     * 状态 0 无效 1 有效
     */
    private Integer state;

    /**
     * 来源类型
     */
    private Integer sourceType;

    /**
     * 来源（集团/门店）
     */
    private String source;

    /**
     * 来源
     */
    private String sourceName;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 修改人
     */
    private String modifyUser;

    /**
     * 创建时间
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime gmtModified;
}
