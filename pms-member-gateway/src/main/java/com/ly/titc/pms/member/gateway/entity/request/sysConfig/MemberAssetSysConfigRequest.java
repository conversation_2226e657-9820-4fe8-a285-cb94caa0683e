package com.ly.titc.pms.member.gateway.entity.request.sysConfig;

import com.ly.titc.pms.member.gateway.entity.request.BaseRequest;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
 * 会员资产系统配置请求参数
 * <AUTHOR>
 * @date 2025/3/31
 */
@Data
@Accessors(chain = true)
public class MemberAssetSysConfigRequest extends BaseRequest {

    /**
     * 储值支付是否需要验证 (0-否, 1-是)
     */
    @NotNull(message =  "储值支付是否需要验证不能为空")
    private Integer isPayVerifyRequired;

    /**
     * 是否允许酒店修改验证设置 (仅集团层级有效)
     */
    @NotNull(message =  "是否允许酒店修改验证设置不能为空")
    private Integer allowPayVerifyModify;

    /**
     * 是否支持他卡付款 (0-否, 1-是)
     */
    @NotNull(message =  "是否支持他卡付款不能为空")
    private Integer isSupportOtherCard;

    /**
     * 是否允许酒店修改他卡付款设置 (仅集团层级有效)
     */
    @NotNull(message =  "是否允许酒店修改他卡付款设置不能为空")
    private Integer allowOtherCardModify;
}
