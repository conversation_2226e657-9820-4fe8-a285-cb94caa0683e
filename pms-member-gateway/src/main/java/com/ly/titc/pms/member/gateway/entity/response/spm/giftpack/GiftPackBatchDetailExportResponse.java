package com.ly.titc.pms.member.gateway.entity.response.spm.giftpack;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024-11-29 10:48
 */
@Getter
@Setter
@EqualsAndHashCode
public class GiftPackBatchDetailExportResponse {

    /**
     * 明细业务类型描述
     */
    @ExcelProperty("产品类型")
    private String bizTypeName;


    /**
     * 明细业务编码(产品ID)
     */
    @ExcelProperty("产品ID")
    private String bizNo;

    /**
     * 会员名
     */
    @ExcelProperty("会员名")
    private String memberName;

    /**
     * 会员编号
     */
    @ExcelProperty("会员编号")
    private String memberNo;

    /**
     * 手机号
     */
    @ExcelProperty("手机号")
    private String mobile;
}
