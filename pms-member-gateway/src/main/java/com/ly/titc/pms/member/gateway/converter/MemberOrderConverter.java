package com.ly.titc.pms.member.gateway.converter;

import com.ly.titc.common.enums.MasterTypeEnum;
import com.ly.titc.common.enums.PlatformChannelEnum;
import com.ly.titc.pms.member.com.converter.BaseConverter;
import com.ly.titc.pms.member.com.enums.ChangeTypeEnum;
import com.ly.titc.pms.member.com.enums.MemberSceneEnum;
import com.ly.titc.pms.member.com.lang.TwoTuple;
import com.ly.titc.pms.member.com.utils.ConfigCenterUtil;
import com.ly.titc.pms.member.gateway.entity.request.member.IssueMemberCardRequest;
import com.ly.titc.pms.member.gateway.entity.request.member.MemberRegisterRequest;
import com.ly.titc.pms.member.gateway.entity.request.member.asset.store.MemberStoreRechargeRequest;
import com.ly.titc.pms.member.gateway.entity.request.order.*;
import com.ly.titc.pms.member.gateway.entity.response.order.CreateOrderResponse;
import com.ly.titc.pms.member.gateway.entity.response.order.CreateRefundOrderResponse;
import com.ly.titc.pms.member.gateway.entity.response.order.MemberPayOrderResponse;
import com.ly.titc.pms.member.mediator.entity.dto.member.RegisterMemberDto;
import com.ly.titc.pms.member.mediator.entity.dto.member.UpgradeMemberDto;
import com.ly.titc.pms.member.mediator.entity.dto.order.*;
import com.ly.titc.pms.member.mediator.entity.dto.recharge.MemberStoreRechargeDto;
import com.ly.titc.pms.spm.dubbo.enums.SubActivityTypeEnum;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-11-27 13:58
 */
@Mapper(componentModel = "spring")
public interface MemberOrderConverter extends BaseConverter {

    default CreateOrderDto<RegisterMemberDto> convert(CreateRegisterOrderRequest request) {
        TwoTuple<Integer, String> masterTuple = ConfigCenterUtil.getMasterByBloc(request.getBlocCode());
        CreateOrderDto<RegisterMemberDto> orderDto = convertBase(request, masterTuple.getFirst(), masterTuple.getSecond());
        IssueMemberCardRequest cardRequest = request.getRegisterRequest().getMemberCardInfo();
        orderDto.setMemberScene(MemberSceneEnum.REGISTER.getScene());
        orderDto.setMemberSceneDesc(String.format("注册（%s-%s）", cardRequest.getCardName(), cardRequest.getCardLevelName()));
        RegisterMemberDto convert = convert(request.getBlocCode(), request.getRegisterRequest(), masterTuple.getFirst(), masterTuple.getSecond(), request.getOperator());
        convert.setSource(PlatformChannelEnum.CRM.getPlatformChannel());
        orderDto.setMemberSceneNoteDto(convert);
        ActivityOrderDto activityOrderDto = convertActivity(request);
        activityOrderDto.setEventType(SubActivityTypeEnum.MEMBER_CARD_SALE.getCode());
        orderDto.setActivityOrderDto(activityOrderDto);
        orderDto.setPlatformChannel(PlatformChannelEnum.CRM.getPlatformChannel());
        orderDto.setMemberCardNo(cardRequest.getMemberCardNo());
        return orderDto;
    }

    default CreateOrderDto<PurchaseCardDto> convert(CreatePurchaseCardOrderRequest request) {
        TwoTuple<Integer, String> masterTuple = ConfigCenterUtil.getMasterByBloc(request.getBlocCode());
        CreateOrderDto<PurchaseCardDto> orderDto = convertBase(request, masterTuple.getFirst(), masterTuple.getSecond());
        orderDto.setMemberScene(MemberSceneEnum.PURCHASECARD.getScene());
        orderDto.setMemberSceneDesc(String.format("售卡（%s-%s）", request.getCardName(), request.getCardLevelName()));
        orderDto.setMemberSceneNoteDto(convertPurchaseCardDto(request, masterTuple.getFirst(), masterTuple.getSecond(), request.getOperator()));
        ActivityOrderDto activityOrderDto = convertActivity(request);
        activityOrderDto.setEventType(SubActivityTypeEnum.MEMBER_CARD_SALE.getCode());
        orderDto.setActivityOrderDto(activityOrderDto);
        orderDto.setPlatformChannel(PlatformChannelEnum.CRM.getPlatformChannel());
        orderDto.setMemberCardNo(request.getMemberCardNo());
        return orderDto;
    }

    default CreateOrderDto<UpgradeMemberDto> convert(CreateUpgradeOrderRequest request) {
        TwoTuple<Integer, String> masterTuple = ConfigCenterUtil.getMasterByBloc(request.getBlocCode());
        CreateOrderDto<UpgradeMemberDto> orderDto = convertBase(request, masterTuple.getFirst(), masterTuple.getSecond());
        orderDto.setMemberScene(MemberSceneEnum.UPGRADE.getScene());
        orderDto.setMemberSceneDesc(String.format("升级（%s-%s升级至%s）", request.getCardName(), request.getPreLevelName(), request.getAfterLevelName()));
        orderDto.setMemberSceneNoteDto(convertUpgradeMemberDto(request, masterTuple.getFirst(), masterTuple.getSecond(), request.getOperator(), ChangeTypeEnum.UPGRADE_PURCHASE.getType()));
        ActivityOrderDto activityOrderDto = convertActivity(request);
        activityOrderDto.setEventType(SubActivityTypeEnum.MEMBER_PAID_UPGRADE.getCode());
        orderDto.setActivityOrderDto(activityOrderDto);
        orderDto.setPlatformChannel(PlatformChannelEnum.CRM.getPlatformChannel());
        orderDto.setMemberCardNo(request.getMemberCardNo());
        orderDto.setMemberNo(request.getMemberNo());
        return orderDto;
    }

    default CreateOrderDto<MemberStoreRechargeDto> convert(CreateStoreOrderRequest request){
        CreateOrderDto<MemberStoreRechargeDto> orderDto = convertBase(request, MasterTypeEnum.BLOC.getType(), request.getBlocCode());
        MemberStoreRechargeRequest rechargeRequest = request.getRechargeRequest();
        orderDto.setMemberScene(MemberSceneEnum.RECHARGE.getScene());
        orderDto.setMemberSceneDesc(String.format("%s-%s", "充值", rechargeRequest.getCapitalAmount().add(rechargeRequest.getGiftAmount())));
        MemberStoreRechargeDto rechargeDto = convertDto(rechargeRequest,request);
        rechargeDto.setMasterType(MasterTypeEnum.BLOC.getType());
        rechargeDto.setPlatformChannel(PlatformChannelEnum.CRM.getPlatformChannel());
        orderDto.setMemberSceneNoteDto(rechargeDto);
        ActivityOrderDto activityOrderDto = convertActivity(request);
        activityOrderDto.setEventType(SubActivityTypeEnum.STORE_GIFT.getCode());
        orderDto.setActivityOrderDto(activityOrderDto);
        orderDto.setPlatformChannel(PlatformChannelEnum.CRM.getPlatformChannel());
        orderDto.setMemberNo(request.getMemberNo());
        return orderDto;
    }

    MemberStoreRechargeDto convertDto(MemberStoreRechargeRequest rechargeRequest, CreateStoreOrderRequest request);

    MemberPayOrderDto convert(MemberPayOrderRequest request);
    @Mappings({
            @Mapping(target = "memberOrderPayNo", source = "memberOrderPayTradeNo")
    })
    GetPayStateDto convert(GetPayStateRequest request);

    ActivityOrderDto convertActivity(CreateOrderBaseRequest request);

    CreateOrderDto<RegisterMemberDto> convertBase(CreateRegisterOrderRequest request, Integer masterType, String masterCode);

    CreateOrderDto<PurchaseCardDto> convertBase(CreatePurchaseCardOrderRequest request, Integer masterType, String masterCode);

    CreateOrderDto<UpgradeMemberDto> convertBase(CreateUpgradeOrderRequest request, Integer masterType, String masterCode);

    CreateOrderDto<MemberStoreRechargeDto> convertBase(CreateStoreOrderRequest request, Integer masterType, String masterCode);


    @Mappings({
            @Mapping(target = "blocCode", source = "blocCode"),
            @Mapping(target = "operator", source = "operator")
    })
    RegisterMemberDto convert(String blocCode, MemberRegisterRequest registerRequest, Integer masterType, String masterCode, String operator);

    @Mappings({
            @Mapping(target = "operator", source = "operator")
    })
    PurchaseCardDto convertPurchaseCardDto(CreatePurchaseCardOrderRequest registerRequest, Integer masterType, String masterCode, String operator);

    @Mappings({
            @Mapping(target = "operator", source = "operator")
    })
    UpgradeMemberDto convertUpgradeMemberDto(CreateUpgradeOrderRequest registerRequest, Integer masterType, String masterCode,  String operator, Integer changeType);

    CreateOrderResponse convert(CreateOrderResultDto resultDto);

    @Mappings({
            @Mapping(target = "memberOrderPayTradeNo", source = "memberOrderPayNo"),
            @Mapping(target = "tradeState", source = "payState"),
    })
    MemberPayOrderResponse convert(PayOrderResultDto resultDto);

    RefundOrderDto convert(RefundOrderRequest request, Integer masterType, String masterCode);
    @Mappings({
            @Mapping(target = "memberOrderPayTradeNo", source = "refundPayNo"),
            @Mapping(target = "tradeState", source = "refundState"),
    })
    CreateRefundOrderResponse convert(RefundResultDto refundResultDto);
    @Mappings({
            @Mapping(target = "refundPayNo", source = "memberOrderPayTradeNo"),
    })
    GetRefundStateDto convert(GetRefundStateRequest request);
}
