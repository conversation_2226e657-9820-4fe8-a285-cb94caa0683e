package com.ly.titc.pms.member.gateway.entity.response.spm.coupon;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

/**
 * @Description 优惠券发放记录导出类
 * <AUTHOR>
 * @Date 2024-11-5 10:57
 */
@Getter
@Setter
@EqualsAndHashCode
public class CouponExportResponse {

    @ExcelProperty("券编号")
    private String couponCode;


    @ExcelProperty("券类型")
    private String couponTypeName;


    @ExcelProperty("券名称")
    private String couponName;


    @ExcelProperty("券优惠")
    private String couponValue;


    @ExcelProperty("生成门店")
    private String hotelName;


    @ExcelProperty("发放事件")
    private String issueEvent;


    @ExcelProperty("发放日期")
    private String issueDate;


    @ExcelProperty("有效时间")
    private String effectiveTimeDesc;


    @ExcelProperty("状态")
    private String stateName;


    @ExcelProperty("所属人名称")
    private String owner;


    @ExcelProperty("使用时间")
    private String useTime;
}
