package com.ly.titc.pms.member.gateway.entity.response.asset;

import lombok.Data;

import java.math.BigDecimal;
import java.sql.Timestamp;

/**
 * @Author：rui
 * @name：MemberSoreConsumeRecordResponse
 * @Date：2024-12-9 15:57
 * @Filename：MemberSoreConsumeRecordResponse
 */
@Data
public class MemberStoreConsumeRecordResponse {

    /**
     * 自增id
     */
    private Long id;

    /**
     * 会员号
     */
    private String memberNo;

    /**
     * 消费主体类型  1:集团 2:门店 3:酒馆
     */
    private Integer masterType;

    /**
     * 消费主体CODE ELONG 集团编码 门店编码
     */
    private String masterCode;

    /**
     * 消费主体名称
     */
    private String masterName;

    /**
     * 酒馆组code ELONG (冗余)
     */
    private String clubCode;

    /**
     * 集团code （冗余）
     */
    private String blocCode;

    /**
     * 酒店code （冗余）
     */
    private String hotelCode;

    /**
     * 消费记录号
     */
    private String consumeRecordNo;

    /**
     * 原消费记录号(交易类型为退款时)
     */
    private String originalConsumeRecordNo;

    /**
     * 消费类型：pay:支付，feeze冻结, refund 退款
     */
    private String consumeType;

    /**
     * 消费描述（内容） 冻结为冻结原因
     */
    private String consumeDesc;

    /**
     * 平台渠道
     */
    private String platformChannel;

    /**
     * 本金金额
     */
    private BigDecimal capitalAmount;

    /**
     * 赠送金额
     */
    private BigDecimal giftAmount;

    /**
     * 总金额
     */
    private BigDecimal totalAmount;

    /**
     * 使用场景
     */
    private String scene;

    /**
     * 业务线唯一号
     */
    private String businessNo;

    /**
     *  WXBOOKINGPAY(微订房支付) ,PMSPAY(PMS支付)
     */
    private String businessType;

    /**
     * 业务描述
     */
    private String businessNote;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 修改人
     */
    private String modifyUser;

    /**
     * 创建时间
     */
    private String gmtCreate;

    /**
     * 修改时间
     */
    private String gmtModified;


    /**
     * 0 未冻结 1 已冻结
     */
    private Integer freezeState;

    /**
     * 是否长期冻结 0 否 1 是
     */
    private Integer isFreezeLong;

    /**
     * 冻结有效期 yyyy-mm-dd
     */
    private String freezeDate;

    /**
     * 解冻人
     */
    private String unfreezeUser;

    /**
     * 解冻日期
     */
    private String unfreezeDate;

    /**
     * 解冻原因
     */
    private String unfreezeReason;
}
