package com.ly.titc.pms.member.gateway.entity.response.spm.coupon.template.dto;

import lombok.Data;

import java.util.List;

/**
 * 价格方案适用范围
 * @Description:
 * @Author: huaizhu.zhong
 * @Date: 2024-11-12
 */
@Data
public class TemplateHotelRatePlanInfoDto {

    /**
     * 适用范围类型 ALL-全部可用 APPOINT_AVAILABLE-指定可用 APPOINT_UNAVAILABLE-指定不可用 ALL_UNAVAILABLE-全部不可用
     */
    private String applicableScopeType;

    /**
     * 酒店价格方案信息
     */
    private List<TemplateHotelRatePlanDataInfoDto> hotelRatePlanInfos;
}
