package com.ly.titc.pms.member.gateway.entity.response.spm.activity;


import lombok.Data;

@Data
public class MemberActivityInfoResponse {

    /**
     * 活动code
     */
    private String activityCode;

    /**
     * 活动名称
     */
    private String name;

    /**
     * 活动展示类型
     */
    private String displayType;

    /**
     * 活动展示类型
     */
    private String displayTypeDesc;

    /**
     * 活动开始时间
     */
    private String effectiveStartTime;

    /**
     * 活动失效时间
     */
    private String effectiveEndTime;

    /**
     * 是否长期有效
     */
    private Boolean isLongTerm;

    /**
     * 活动优先级
     */
    private Integer priority;

    /**
     * 活动描述
     */
    private String desc;

    /**
     * 活动状态  un_start 未开始 ongoing 进行中  end 结束 closed 关闭
     * {@link com.ly.titc.pms.spm.dubbo.enums.ActivityStateEnum}
     */
    private String displayState;

    /**
     * 修改人
     */
    private String modifyUser;

    /**
     * 修改时间
     */
    private String gmtModified;
}
