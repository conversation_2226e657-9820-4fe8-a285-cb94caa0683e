package com.ly.titc.pms.member.gateway.entity.request.member;

import com.ly.titc.pms.member.gateway.entity.request.BaseRequest;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @Author：rui
 * @name：DeleteBaseReq
 * @Date：2024-11-13 15:24
 * @Filename：DeleteBaseReq
 */
@Data
public class DeleteBaseRequest extends BaseRequest {

    @NotNull(message = "id不能为空")
    private Long id;
}
