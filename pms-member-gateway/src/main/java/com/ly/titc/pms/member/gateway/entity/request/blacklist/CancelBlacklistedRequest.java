package com.ly.titc.pms.member.gateway.entity.request.blacklist;

import com.ly.titc.pms.member.gateway.entity.request.BaseRequest;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 取消拉黑
 *
 * @Author：rui
 * @name：UnBlacklistedRequest
 * @Date：2024-12-10 20:07
 * @Filename：UnBlacklistedRequest
 */
@Data
public class CancelBlacklistedRequest extends BaseRequest {

    /**
     * 会员编号编号
     */
    @NotBlank(message = "会员编号不能为空")
    private String memberNo;

    /**
     * 黑名单编号
     */
    @NotBlank(message = "黑名单编号不能为空")
    private String blacklistNo;
}
