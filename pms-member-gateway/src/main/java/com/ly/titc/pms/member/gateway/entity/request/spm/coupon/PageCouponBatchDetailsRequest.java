package com.ly.titc.pms.member.gateway.entity.request.spm.coupon;

import com.ly.titc.pms.member.gateway.entity.request.PageBaseRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024-11-7 9:57
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class PageCouponBatchDetailsRequest extends PageBaseRequest {

    /**
     * 客户编号
     */
    private String ownerCustomerNo;

    /**
     * 会员卡号
     */
    private String ownerMemberCardNo;

    /**
     * 券批次编码
     */
    @NotBlank(message = "批次号不能为空")
    private String batchCode;
}
