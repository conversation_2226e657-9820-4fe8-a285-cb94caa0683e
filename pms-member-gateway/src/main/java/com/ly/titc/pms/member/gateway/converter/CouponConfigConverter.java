package com.ly.titc.pms.member.gateway.converter;


import com.ly.titc.pms.member.gateway.entity.request.BaseRequest;
import com.ly.titc.pms.member.gateway.entity.request.spm.coupon.SaveCouponConfigRequest;
import com.ly.titc.pms.member.gateway.entity.response.spm.coupon.CouponConfigResponse;
import com.ly.titc.pms.spm.dubbo.entity.request.coupon.config.GetBlocCouponConfigReq;
import com.ly.titc.pms.spm.dubbo.entity.request.coupon.config.SaveBlocCouponConfigReq;
import com.ly.titc.pms.spm.dubbo.entity.response.coupon.config.BlocCouponConfigResp;
import org.mapstruct.Mapper;

/**
 * @Description:
 * @Author: huaizhu.zhong
 * @Date: 2024-11-12
 */
@Mapper(componentModel = "spring")
public interface CouponConfigConverter {

    SaveBlocCouponConfigReq convertSaveBlocCouponConfigReq(SaveCouponConfigRequest request);

    CouponConfigResponse convertCouponConfigResponse(BlocCouponConfigResp dto);

    GetBlocCouponConfigReq convertGetBlocCouponConfigReq(BaseRequest request);
}
