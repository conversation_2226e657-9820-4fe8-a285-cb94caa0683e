package com.ly.titc.pms.member.gateway.controller.member;

import com.ly.titc.cc.sdk.log.annotation.LogRecord;
import com.ly.titc.common.entity.Pageable;
import com.ly.titc.common.entity.Response;
import com.ly.titc.common.util.PageableUtil;
import com.ly.titc.pms.ecrm.dubbo.entity.response.member.MemberTagConfigInfoResp;
import com.ly.titc.pms.member.com.annotation.OperationLog;
import com.ly.titc.pms.member.com.constant.ActionConstants;
import com.ly.titc.pms.member.com.constant.CommonConstant;
import com.ly.titc.pms.member.com.constant.ModuleConstants;
import com.ly.titc.pms.member.com.enums.MemberTagEnum;
import com.ly.titc.pms.member.com.lang.TwoTuple;
import com.ly.titc.pms.member.com.utils.ConfigCenterUtil;
import com.ly.titc.pms.member.dubbo.enums.MasterTypeEnum;
import com.ly.titc.pms.member.gateway.converter.MemberCardConverter;
import com.ly.titc.pms.member.gateway.entity.request.BaseRequest;
import com.ly.titc.pms.member.gateway.entity.request.SelectRequest;
import com.ly.titc.pms.member.gateway.entity.request.member.GetDetailBaseRequest;
import com.ly.titc.pms.member.gateway.entity.request.member.card.DeleteTagRequest;
import com.ly.titc.pms.member.gateway.entity.request.member.tag.PageMemberTagConfigInfoRequest;
import com.ly.titc.pms.member.gateway.entity.request.member.tag.SaveMemberTagConfigRequest;
import com.ly.titc.pms.member.gateway.entity.response.SelectResponse;
import com.ly.titc.pms.member.gateway.entity.response.member.MemberTagConfigInfoResponse;
import com.ly.titc.pms.member.mediator.entity.dto.member.MemberDetailDto;
import com.ly.titc.pms.member.mediator.entity.dto.member.PageMemberParamDto;
import com.ly.titc.pms.member.mediator.entity.dto.profile.MemberProfileTagCountDto;
import com.ly.titc.pms.member.mediator.rpc.dubbo.ecrm.MemberCardInfoDecorator;
import com.ly.titc.pms.member.mediator.service.MemberMedService;
import com.ly.titc.pms.member.mediator.service.MemberProfileMedService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 标签管理
 *
 * @Author：rui
 * @name：MemberTagController
 * @Date：2024-11-15 9:50
 * @Filename：MemberTagController
 */
@Slf4j
@RestController
@RequestMapping("/member/tag")
public class MemberTagController {

    @Resource
    private MemberCardConverter converter;

    @Resource
    private MemberCardInfoDecorator decorator;

    @Resource
    private MemberMedService memberMedService;

    @Resource
    private MemberProfileMedService memberProfileMedService;

    /**
     * 新增标签
     *
     * @param request request
     * @return id
     */
    @PostMapping("/addTagConfig")
    @LogRecord(tenant = CommonConstant.PROJECT_CODE, bizCode = ModuleConstants.TAG_MANAGE_CODE, bizName = ModuleConstants.TAG_MANAGE_NAME,
            category = ActionConstants.ADD_CODE, categoryName = ActionConstants.ADD_NAME)
    @OperationLog(operateRecord = "#memberTag_addMemberTag(#request)")
    public Response<Long> addTagConfig(@RequestBody @Valid SaveMemberTagConfigRequest request) {
        TwoTuple<Integer, String> masterTuple = ConfigCenterUtil.getMasterByBloc(request.getBlocCode());
        return Response.success(decorator.saveTagConfig(converter.convertSaveMemberTagConfigReq(request, masterTuple.getFirst())));
    }

    /**
     * 更新标签
     *
     * @param request request
     * @return id
     */
    @PostMapping("/updateTagConfig")
    @LogRecord(tenant = CommonConstant.PROJECT_CODE, bizCode = ModuleConstants.TAG_MANAGE_CODE, bizName = ModuleConstants.TAG_MANAGE_NAME,
            category = ActionConstants.SAVE_CODE, categoryName = ActionConstants.SAVE_NAME)
    @OperationLog(operateRecord = "#memberTag_updateMemberTag(#request)")
    public Response<Long> updateTagConfig(@RequestBody @Valid SaveMemberTagConfigRequest request) {
        TwoTuple<Integer, String> masterTuple = ConfigCenterUtil.getMasterByBloc(request.getBlocCode());
        return Response.success(decorator.saveTagConfig(converter.convertSaveMemberTagConfigReq(request, masterTuple.getFirst())));
    }

    /**
     * 删除标签
     *
     * @param request request
     * @return 结果
     */
    @PostMapping("/deleteTagConfig")
    @LogRecord(tenant = CommonConstant.PROJECT_CODE, bizCode = ModuleConstants.TAG_MANAGE_CODE, bizName = ModuleConstants.TAG_MANAGE_NAME,
            category = ActionConstants.DELETE_CODE, categoryName = ActionConstants.DELETE_NAME)
    @OperationLog(operateRecord = "#memberTag_deleteMemberTag(#request)")
    public Response deleteTagConfig(@RequestBody @Valid DeleteTagRequest request) {
        decorator.deleteTagConfig(request.getId(), request.getOperator());
        return Response.success();
    }

    /**
     * 获取标签详情
     *
     * @param request request
     * @return 详情
     */
    @PostMapping("/getTagConfig")
    public Response<MemberTagConfigInfoResponse> getTagConfig(@RequestBody @Valid GetDetailBaseRequest request) {
        MemberTagConfigInfoResp resp = decorator.getTagConfig(request.getId());
        return Response.success(resp);
    }

    /**
     * 分页查询标签
     *
     * @param request request
     * @return 分页结果
     */
    @PostMapping("/page")
    public Response<Pageable<MemberTagConfigInfoResponse>> page(@RequestBody @Valid PageMemberTagConfigInfoRequest request) {
        TwoTuple<Integer, String> masterTuple = ConfigCenterUtil.getMasterByBloc(request.getBlocCode());
        Pageable<MemberTagConfigInfoResp> pageable = decorator.pageTagConfig(converter.convertPageMemberTagConfigInfoReq(request, masterTuple.getFirst()));
        List<MemberTagConfigInfoResp> list = pageable.getDatas();
        if (CollectionUtils.isEmpty(list)) {
            return Response.success(pageable);
        }
        List<Long> tagIds = list.stream().map(MemberTagConfigInfoResp::getId).collect(Collectors.toList());
        List<MemberProfileTagCountDto> memberProfileTagCounts = memberProfileMedService.listMemberTagCount(tagIds);
        Map<Long, Integer> tagCountMap = memberProfileTagCounts.stream().collect(Collectors.toMap(MemberProfileTagCountDto::getTagId, MemberProfileTagCountDto::getCount));
        List<MemberTagConfigInfoResponse> records = converter.convertMemberTagConfigInfoResponseList(list);
        records.forEach(item -> item.setMemberCount(tagCountMap.get(item.getId())));
        return Response.success(PageableUtil.convert(pageable, records));
    }

    /**
     * 用户画像标签下拉框
     *
     * @param request request
     */
    @PostMapping("/selectTag")
    public Response<List<MemberTagConfigInfoResponse>> selectTag(@RequestBody @Valid SelectRequest request) {
        TwoTuple<Integer, String> masterTuple = ConfigCenterUtil.getMasterByBloc(request.getBlocCode());
        List<MemberTagConfigInfoResp> list = decorator.listTagConfig(masterTuple.getFirst(), request.getBlocCode(), request.getName());
        return Response.success(converter.convertMemberTagConfigInfoResponseList(list));
    }

    /**
     * 标签类型下拉框
     */
    @PostMapping("/selectTagType")
    public Response<List<SelectResponse>> selectTagType() {
        return Response.success(Arrays.stream(MemberTagEnum.values())
                .map(item -> new SelectResponse().setText(item.getDesc()).setValue(item.getType().toString())).collect(Collectors.toList()));
    }

}
