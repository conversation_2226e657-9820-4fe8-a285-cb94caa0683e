package com.ly.titc.pms.member.gateway.entity.response.spm.coupon.template;

import com.ly.titc.pms.member.gateway.entity.request.spm.coupon.template.dto.EffectDateConfigDto;
import com.ly.titc.pms.member.gateway.entity.response.spm.coupon.template.dto.TemplateUseRuleInfoDto;
import com.ly.titc.pms.member.gateway.entity.response.spm.coupon.template.dto.TemplateGrantRuleInfoDto;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 具体某个版本优惠券模板的信息
 * @Description
 * <AUTHOR>
 * @Date 2024-11-6 17:38
 */
@Data
@Accessors(chain = true)
public class CouponTemplateInfoResponse {

    /**
     * 模板code
     */
    private String templateCode;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 券名称
     */
    private String couponName;

    /**
     * 券类型 1.折扣券,2.代金券,3.免房券,4.餐饮券,5.抵扣券,6.小时券,7.延时券,8.升房券,9.体验券,10.通用券
     */
    private Integer couponType;

    /**
     * 券类型名称
     */
    private String couponTypeName;

    /**
     * 券值
     */
    private String couponValue;

    /**
     * 券值类型1.固定金额 2-折扣率 3-延长小时 4-延长小时点 5-券后房价
     */
    private Integer couponValueType;

    /**
     * 券使用提醒
     */
    private String couponTips;

    /**
     * 券使用说明
     */
    private String couponContent;

    /**
     * 成本归属 1-集团 2-门店
     */
    private Integer costAttribution;

    /**
     * 核销方式 0-全部 1-仅券号 2-二维码 3-一维码
     */
    private Integer redeemType;

    /**
     * 上一个操作人id
     */
    private String operatorId;

    /**
     * 修改时间
     */
    private String gmtModified;

    /**
     * 入账方式 0-不入账 1-消费项 2-结算项
     */
    private Integer postingType = 0;

    /**
     * 入账项code
     */
    private String postingItemCode;

    /**
     * 入账项名称
     */
    private String postingItemName;

    /**
     * 是否生效一个房晚 0-否 1-是
     */
    private Integer isEffectOneNight;

    /**
     * 夜审或加收房费前是否允许取消使用券
     */
    private Boolean nightAuditBeforeCancel;

    /**
     * 订单取消后返回
     */
    private Boolean orderCancelReturn;

    /**
     * 券有效期配置
     */
    private EffectDateConfigDto effectDateInfo;

    /**
     * 券使用规则
     */
    private TemplateUseRuleInfoDto useRuleInfo;

    /**
     * 优惠券发放规则
     */
    private TemplateGrantRuleInfoDto grantRuleInfo;

    private Integer isSys;
}
