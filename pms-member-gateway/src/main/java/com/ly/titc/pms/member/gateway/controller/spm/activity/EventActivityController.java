package com.ly.titc.pms.member.gateway.controller.spm.activity;

import com.ly.titc.common.entity.Pageable;
import com.ly.titc.common.entity.Response;
import com.ly.titc.common.util.PageableUtil;
import com.ly.titc.pms.member.gateway.converter.EventActivityConverter;
import com.ly.titc.pms.member.gateway.entity.request.spm.activity.*;
import com.ly.titc.pms.member.gateway.entity.response.spm.activity.EventActivityDetailResponse;
import com.ly.titc.pms.member.gateway.entity.response.spm.activity.EventActivityListResponse;
import com.ly.titc.pms.member.gateway.entity.response.spm.activity.EventActivityTypeResponse;
import com.ly.titc.pms.member.gateway.entity.response.spm.activity.EventGrantInfoResponse;
import com.ly.titc.pms.member.mediator.rpc.dubbo.activity.EventActivityDecorator;
import com.ly.titc.pms.spm.dubbo.entity.request.activity.*;
import com.ly.titc.pms.spm.dubbo.entity.response.activity.EventActivityDetailResp;
import com.ly.titc.pms.spm.dubbo.entity.response.activity.EventActivityGrantInfoResp;
import com.ly.titc.pms.spm.dubbo.entity.response.activity.EventActivityInfoResp;
import com.ly.titc.pms.spm.dubbo.enums.ActivityTypeEnum;
import com.ly.titc.pms.spm.dubbo.enums.SubActivityTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 赠券事件相关接口
 */
@Slf4j
@RestController
@RequestMapping("/activity/event")
public class EventActivityController {
    @Resource(type = EventActivityDecorator.class)
    private EventActivityDecorator activityDecorator;
    @Resource(type = EventActivityConverter.class)
    private EventActivityConverter eventActivityConverter;

    /**
     * 事件活动类型枚举
     */
    @PostMapping("/displayTypes")
    public Response<List<EventActivityTypeResponse>> getDisplayTypes() {
        List<EventActivityTypeResponse> ans = Arrays.stream(SubActivityTypeEnum.values())
                .filter(enums -> Objects.equals(enums.getActivityTypeEnum().getCode(), ActivityTypeEnum.EVENT_ACTIVITY.getCode()))
                .map(enums -> eventActivityConverter.convertEventActivityTypeResponse(enums)).collect(Collectors.toList());
        return Response.success(ans);
    }


    /**
     * 事件活动详情
     */
    @PostMapping("/getEventActivityDetail")
    public Response<EventActivityDetailResponse> getEventActivityDetail(@Valid @RequestBody GetActivityRequest request) {
        GetEventActivityReq req = eventActivityConverter.convertGetEventActivityReq(request);
        EventActivityDetailResp resp = activityDecorator.getDetail(req);
        return Response.success(eventActivityConverter.buildEventActivityDetailResponse(resp));
    }


    /**
     * 赠券事件分页列表
     */
    @PostMapping("/pageInfos")
    public Response<Pageable<EventActivityListResponse>> pageInfos(@Valid @RequestBody PageActivityRequest request) {
        PageEventActivityInfoReq req = eventActivityConverter.convertPageEventActivityInfoReq(request);
        Pageable<EventActivityInfoResp> pageable = activityDecorator.pageInfo(req);
        return Response.success(PageableUtil.convert(pageable,eventActivityConverter.convertEventActivityListResponseList(pageable.getDatas())));
    }

    /**
     * 事件活动的赠送详情
     */
    @PostMapping("/pageRewardDetails")
    public Response<Pageable<EventGrantInfoResponse>> pageRewardDetails(@Valid @RequestBody PageGrantInfoRequest request) {
        PageActivityGrantInfoReq req = eventActivityConverter.convertPageActivityGrantInfoReq(request);
        Pageable<EventActivityGrantInfoResp> pageable = activityDecorator.pageGrantInfos(req);
        return Response.success(PageableUtil.convert(pageable,eventActivityConverter.convertEventGrantInfoResponseList(pageable.getDatas())));
    }

    /**
     * 创建事件活动
     */
    @PostMapping("/create")
    public Response<Void> create(@Valid @RequestBody CreateEventActivityRequest request) {
        activityDecorator.save(eventActivityConverter.buildSaveEventActivityReq(request));
        return Response.success();
    }

    /**
     * 更新事件活动
     */
    @PostMapping("/update")
    public Response<Void> update(@Valid @RequestBody UpdateEventActivityRequest request) {
        activityDecorator.save(eventActivityConverter.buildUpdateEventActivityReq(request));
        return Response.success();
    }

    /**
     * 启用活动
     */
    @PostMapping("/enable")
    public Response<Void> enable(@Valid @RequestBody GetActivityRequest request) {
        UpdateActivityStateReq req = eventActivityConverter.convertUpdateActivityStateReq(request);
        activityDecorator.enable(req);
        return Response.success();
    }

    /**
     * 禁用活动
     */
    @PostMapping("/disable")
    public Response<Void> disable(@Valid @RequestBody GetActivityRequest request) {
        UpdateActivityStateReq req = eventActivityConverter.convertUpdateActivityStateReq(request);
        activityDecorator.disable(req);
        return Response.success();
    }

    /**
     * 删除活动
     */
    @PostMapping("/delete")
    public Response<Void> delete(@Valid @RequestBody GetActivityRequest request) {
        DeleteActivityReq req = eventActivityConverter.convertDeleteActivityReq(request);
        activityDecorator.delete(req);
        return Response.success();
    }

}
