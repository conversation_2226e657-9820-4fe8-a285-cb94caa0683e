package com.ly.titc.pms.member.gateway.converter;

import com.google.common.collect.Lists;
import com.ly.titc.pms.member.com.constant.SystemConstant;
import com.ly.titc.pms.member.com.converter.BaseConverter;
import com.ly.titc.pms.member.gateway.entity.request.spm.activity.*;
import com.ly.titc.pms.member.gateway.entity.request.spm.activity.dto.CostBearInfoDto;
import com.ly.titc.pms.member.gateway.entity.request.spm.activity.dto.MemberCardSaleGearsDto;
import com.ly.titc.pms.member.gateway.entity.request.spm.activity.dto.MemberRechargeGearsDto;
import com.ly.titc.pms.member.gateway.entity.request.spm.activity.dto.MemberUpgradeGearsDto;
import com.ly.titc.pms.member.gateway.entity.response.spm.activity.*;
import com.ly.titc.pms.spm.dubbo.entity.dto.activity.BearRatioDto;
import com.ly.titc.pms.spm.dubbo.entity.dto.activity.CostBearDto;
import com.ly.titc.pms.spm.dubbo.entity.dto.activity.DiscountBenefitDto;
import com.ly.titc.pms.spm.dubbo.entity.dto.activity.gear.MemberCardSaleGearDto;
import com.ly.titc.pms.spm.dubbo.entity.dto.activity.gear.MemberCardUpgradeGearDto;
import com.ly.titc.pms.spm.dubbo.entity.dto.activity.gear.MemberPointGearDto;
import com.ly.titc.pms.spm.dubbo.entity.dto.activity.gear.MemberRechargeGearDto;
import com.ly.titc.pms.spm.dubbo.entity.dto.rule.use.MemberActivityUseRuleDto;
import com.ly.titc.pms.spm.dubbo.entity.dto.rule.use.applicable.ApplicableOrderChannelDto;
import com.ly.titc.pms.spm.dubbo.entity.request.activity.UpdateActivityStateReq;
import com.ly.titc.pms.spm.dubbo.entity.request.activity.member.*;
import com.ly.titc.pms.spm.dubbo.entity.response.activity.member.*;
import com.ly.titc.pms.spm.dubbo.enums.BenefitTypeEnum;
import com.ly.titc.pms.spm.dubbo.enums.CostBearTypeEnum;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.mapstruct.Builder;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 会员活动converter
 */
@Mapper(componentModel = "spring", builder = @Builder(disableBuilder = true))
public interface MemberActivityConverter extends BaseConverter {

    @Mapping(target = "hotelCode", ignore = true)
    @Mapping(target = "sourceClient", ignore = true)
    @Mapping(target = "currPage", source = "pageIndex")
    @Mapping(target = "subActivityType", source = "displayType")
    @Mapping(target = "activityType", expression = "java(com.ly.titc.pms.spm.dubbo.enums.ActivityTypeEnum.MEMBER_ACTIVITY.getCode())")
    PageMemberActivityReq convertPageMemberActivityReq(PageActivityRequest request);

    @Mapping(target = "displayTypeDesc", source = "subActivityTypeName")
    @Mapping(target = "displayType", source = "subActivityType")
    @Mapping(target = "displayState", source = "state")
    MemberActivityInfoResponse convertMemberActivityInfoResponse(MemberActivityBaseResp ans);

    List<MemberActivityInfoResponse> convertMemberActivityInfoResponse(List<MemberActivityBaseResp> ans);

    @Mapping(target = "useRuleInfo", ignore = true)
    @Mapping(target = "hotelCode", ignore = true)
    @Mapping(target = "costBearDto", ignore = true)
    @Mapping(target = "sourceClient", constant = "CRS")
    @Mapping(target = "subActivityType", source = "displayType")
    SaveMemberActivityBaseReq convertSaveMemberActivityBaseReq(MemberActivityCommonRequest request);

    default CostBearDto buildcostBearDto(CostBearInfoDto dto) {
        CostBearDto ans = new CostBearDto();
        if (Objects.isNull(dto)) {
            return ans;
        }
        ans.setCostBearType(dto.getCostBearType());
        if (!Objects.equals(dto.getCostBearType(), CostBearTypeEnum.COLLECTION.getCode())) {
            ans.setPlatformBearRatio(dto.getBearRatioDto().getPlatform());
            ans.setBrandBearRatio(dto.getBearRatioDto().getBrand());
            ans.setBlocBearRatio(dto.getBearRatioDto().getBloc());
            ans.setHotelBearRatio(dto.getBearRatioDto().getHotel());
        }
        return ans;
    }

    default ApplicableOrderChannelDto buildApplicableOrderChannelDto(MemberActivityCommonRequest request) {
        if (Objects.isNull(request.getApplicableOrderChannels())) {
            return null;
        }
        ApplicableOrderChannelDto ans = new ApplicableOrderChannelDto();
        ans.setIsAllOrderChannel(Objects.equals(request.getScopeOrderChannelRange(), NumberUtils.INTEGER_ONE));
        ans.setOrderChannelInfos(request.getApplicableOrderChannels());
        return ans;
    }

    default MemberActivityUseRuleDto buildMemberActivityUseRuleDto(MemberActivityCommonRequest request) {
        MemberActivityUseRuleDto ans = new MemberActivityUseRuleDto();
        ans.setApplicableHotel(request.getApplicableHotelsDto());
        ans.setApplicableChannels(request.getApplicablePlatformChannels());
        ans.setApplicableOrderChannel(buildApplicableOrderChannelDto(request));
        ans.setApplicableScopeSources(request.getApplicableScopeSources());
        ans.setApplicableUsers(request.getApplicableUsersDto());
        ans.setApplicableExpenses(request.getApplicableExpensesDto());
        ans.setApplicableTimeRangeDto(request.getApplicableTimeRangeDto());
        return ans;
    }

    default SaveMemberActivityBaseReq buildSaveMemberActivityBaseReq(MemberActivityCommonRequest request) {
        SaveMemberActivityBaseReq req = convertSaveMemberActivityBaseReq(request);
        req.setCostBearDto(buildcostBearDto(request.getCostBearDto()));
        req.setUseRuleInfo(buildMemberActivityUseRuleDto(request));
        return req;
    }

    @Mapping(target = "gearInfos", ignore = true)
    SaveSaleCardActivityReq convertSaveSaleCardActivityReq(SaveMemberActivityBaseReq req);

    @Mapping(target = "itemCode", source = "dto.gearCode")
    @Mapping(target = "salePoint", source = "dto.point")
    @Mapping(target = "saleAmount", source = "dto.amount")
    @Mapping(target = "benefitInfos", source = "dto.benefitDtoList")
    MemberCardSaleGearDto convertMemberCardSaleGearDto(MemberCardSaleGearsDto dto, String memberCardName, String memberCardId);

    default List<MemberCardSaleGearDto> buildSaleCardGearInfos(SaveSaleCardActivityRequest request) {
        return request.getCarriers().stream().map(item ->
                convertMemberCardSaleGearDto(item, request.getCardName(), request.getCardId())).collect(Collectors.toList());
    }

    @Mapping(target = "itemCode", source = "dto.gearCode")
    @Mapping(target = "memberCardLevelName", source = "dto.targetMemberCardLevelName")
    @Mapping(target = "memberCardLevel", source = "dto.targetMemberCardLevel")
    @Mapping(target = "salePoint", source = "dto.point")
    @Mapping(target = "saleAmount", source = "dto.amount")
    @Mapping(target = "benefitInfos", source = "dto.benefitDtoList")
    MemberCardUpgradeGearDto convertMemberCardUpgradeGearDto(MemberUpgradeGearsDto dto, String memberCardName, String memberCardId);

    default List<MemberCardUpgradeGearDto> buildCardUpgradeGearInfos(SaveUpgradeActivityRequest request) {
        return request.getCarriers().stream().map(item ->
                convertMemberCardUpgradeGearDto(item, request.getCardName(), request.getCardId())).collect(Collectors.toList());
    }

    @Mapping(target = "itemCode", source = "gearCode")
    @Mapping(target = "saleAmount", source = "discountAmount")
    @Mapping(target = "benefitInfos", source = "benefitDtoList")
    MemberRechargeGearDto convertMemberRechargeGearDto(MemberRechargeGearsDto dto);


    default List<MemberRechargeGearDto> buildRechargeGearInfos(SaveRechargeActivityRequest request) {
        return request.getCarriers().stream().map(this::convertMemberRechargeGearDto).collect(Collectors.toList());
    }

    default DiscountBenefitDto buildPointBenefitDto(Integer exchangePoint) {
        DiscountBenefitDto ans = new DiscountBenefitDto();
        ans.setDiscountType(BenefitTypeEnum.POINT.getCode());
        ans.setBenefitValue(String.valueOf(exchangePoint));
        ans.setDiscountCount(NumberUtils.INTEGER_ONE);
        return ans;
    }

    default List<MemberPointGearDto> buildPointGearInfos(SavePointsActivityRequest request) {
        MemberPointGearDto ans = new MemberPointGearDto();
        ans.setConsumptionAmount(request.getExchangeAmount());
        ans.setBenefitInfos(Lists.newArrayList(buildPointBenefitDto(request.getExchangePoint())));
        return Lists.newArrayList(ans);
    }

    default SaveSaleCardActivityReq buildSaleCardActivityReq(SaveSaleCardActivityRequest request) {
        SaveSaleCardActivityReq ans = convertSaveSaleCardActivityReq(buildSaveMemberActivityBaseReq(request));
        ans.setGearInfos(buildSaleCardGearInfos(request));
        return ans;
    }

    @Mapping(target = "gearInfos", ignore = true)
    SaveUpgradeActivityReq convertSaveUpgradeActivityReq(SaveMemberActivityBaseReq req);

    default SaveUpgradeActivityReq buildUpgradeActivityReq(SaveUpgradeActivityRequest request) {
        SaveUpgradeActivityReq req = convertSaveUpgradeActivityReq(buildSaveMemberActivityBaseReq(request));
        req.setGearInfos(buildCardUpgradeGearInfos(request));
        return req;
    }

    @Mapping(target = "giftAmountIsLongTerm", ignore = true)
    @Mapping(target = "giftAmountEffectTimeUnit", ignore = true)
    @Mapping(target = "giftAmountEffectTime", ignore = true)
    @Mapping(target = "gearInfos", ignore = true)
    SaveRechargeActivityReq convertSaveRechargeActivityReq(SaveMemberActivityBaseReq req);

    default SaveRechargeActivityReq buildRechargeActivityReq(SaveRechargeActivityRequest request) {
        SaveMemberActivityBaseReq baseReq = buildSaveMemberActivityBaseReq(request);
        SaveRechargeActivityReq req = convertSaveRechargeActivityReq(baseReq);
        if (request.getIsLongTerm()) {
            req.setGiftAmountEffectTime(SystemConstant.GIFT_MONEY_LONG_PERIOD);
        } else {
            req.setGiftAmountEffectTime(request.getStoreLimit());
        }
        req.setGiftAmountEffectTimeUnit(request.getStoreLimitUnit());
        req.setGiftAmountIsLongTerm(BooleanUtils.toInteger(request.getIsLongTerm()));
        req.setGearInfos(buildRechargeGearInfos(request));
        return req;
    }

    @Mapping(target = "issueNodes", ignore = true)
    @Mapping(target = "gearInfos", ignore = true)
    SavePointsActivityReq convertSavePointsActivityReq(SaveMemberActivityBaseReq req);

    default SavePointsActivityReq buildSavePointsActivityReq(SavePointsActivityRequest request) {
        SaveMemberActivityBaseReq baseReq = buildSaveMemberActivityBaseReq(request);
        SavePointsActivityReq req = convertSavePointsActivityReq(baseReq);
        req.setGearInfos(buildPointGearInfos(request));
        req.setIssueNodes(request.getIssueNodes());
        return req;
    }

    @Mapping(target = "state", ignore = true)
    UpdateActivityStateReq convertUpdateActivityStateReq(GetActivityRequest request);

    GetMemberActivityReq convertGetMemberActivityReq(GetActivityRequest request);

    @Mapping(target = "costBearDto", expression = "java(convertCostBearInfoDto(response.getCostBearDto()))")
    @Mapping(target = "displayType", source = "subActivityType")
    @Mapping(target = "scopeOrderChannelRange", ignore = true)
    @Mapping(target = "applicableUsersDto", ignore = true)
    @Mapping(target = "applicableTimeRangeDto", ignore = true)
    @Mapping(target = "applicableScopeSources", ignore = true)
    @Mapping(target = "applicablePlatformChannels", ignore = true)
    @Mapping(target = "applicableOrderChannels", ignore = true)
    @Mapping(target = "applicableHotelsDto", ignore = true)
    @Mapping(target = "applicableExpensesDto", ignore = true)
    MemberActivityCommonResponse convertMemberActivityCommonResponse(MemberActivityBaseResp response);

    @Mapping(target = "platform", source = "platformBearRatio")
    @Mapping(target = "hotel", source = "hotelBearRatio")
    @Mapping(target = "brand", source = "brandBearRatio")
    @Mapping(target = "bloc", source = "blocBearRatio")
    BearRatioDto convertBearRatioDto(CostBearDto costBearDto);

    default CostBearInfoDto convertCostBearInfoDto(CostBearDto costBearDto) {
        CostBearInfoDto ans = new CostBearInfoDto();
        ans.setCostBearType(costBearDto.getCostBearType());
        ans.setBearRatioDto(convertBearRatioDto(costBearDto));
        return ans;
    }

    default MemberActivityCommonResponse buildMemberActivityCommonResponse(MemberActivityBaseResp resp) {
        MemberActivityCommonResponse response = convertMemberActivityCommonResponse(resp);
        if (Objects.nonNull(resp.getUseRuleInfo().getApplicableOrderChannel())) {
            response.setScopeOrderChannelRange(
                    BooleanUtils.isTrue(resp.getUseRuleInfo().getApplicableOrderChannel().getIsAllOrderChannel()) ? 1 : 2
            );
            response.setApplicableOrderChannels(resp.getUseRuleInfo().getApplicableOrderChannel().getOrderChannelInfos());
        }
        response.setApplicableUsersDto(resp.getUseRuleInfo().getApplicableUsers());
        response.setApplicableTimeRangeDto(resp.getUseRuleInfo().getApplicableTimeRangeDto());
        response.setApplicableScopeSources(resp.getUseRuleInfo().getApplicableScopeSources());
        response.setApplicablePlatformChannels(resp.getUseRuleInfo().getApplicableChannels());
        response.setApplicableHotelsDto(resp.getUseRuleInfo().getApplicableHotel());
        response.setApplicableExpensesDto(resp.getUseRuleInfo().getApplicableExpenses());
        return response;
    }

    @Mapping(target = "carriers", ignore = true)
    @Mapping(target = "cardName", ignore = true)
    @Mapping(target = "cardId", ignore = true)
    MemberSaleCardActivityDetailResponse convertSaleCardDetail(MemberActivityCommonResponse response);

    @Mapping(target = "gearType", ignore = true)
    @Mapping(target = "gearDesc", ignore = true)
    @Mapping(target = "point", source = "salePoint")
    @Mapping(target = "benefitDtoList", source = "benefitInfos")
    @Mapping(target = "amount", source = "saleAmount")
    MemberCardSaleGearsDto convertMemberCardSaleGearsDto(MemberCardSaleGearDto dto);

    List<MemberCardSaleGearsDto> convertMemberCardSaleGearsDtoList(List<MemberCardSaleGearDto> list);

    default MemberSaleCardActivityDetailResponse buildSaleCardActivityResp(MemberCardSaleActivityDetailResp activity) {
        MemberActivityCommonResponse response = buildMemberActivityCommonResponse(activity);
        MemberSaleCardActivityDetailResponse ans = convertSaleCardDetail(response);
        ans.setCarriers(convertMemberCardSaleGearsDtoList(activity.getGearInfos()));
        MemberCardSaleGearDto dto = activity.getGearInfos().iterator().next();
        ans.setCardId(dto.getMemberCardId());
        ans.setCardName(dto.getMemberCardName());
        return ans;
    }

    @Mapping(target = "carriers", ignore = true)
    @Mapping(target = "cardName", ignore = true)
    @Mapping(target = "cardId", ignore = true)
    MemberUpgradeActivityDetailResponse convertUpgradeDetail(MemberActivityCommonResponse response);

    @Mapping(target = "targetMemberCardLevelName", source = "memberCardLevelName")
    @Mapping(target = "targetMemberCardLevel", source = "memberCardLevel")
    @Mapping(target = "gearType", ignore = true)
    @Mapping(target = "gearDesc", ignore = true)
    @Mapping(target = "point", source = "salePoint")
    @Mapping(target = "benefitDtoList", source = "benefitInfos")
    @Mapping(target = "amount", source = "saleAmount")
    MemberUpgradeGearsDto convertMemberUpgradeGearsDto(MemberCardUpgradeGearDto dto);

    List<MemberUpgradeGearsDto> convertMemberUpgradeGearsDtoList(List<MemberCardUpgradeGearDto> list);

    default MemberUpgradeActivityDetailResponse buildUpgradeActivityResp(MemberUpgradeActivityDetailResp activity) {
        MemberActivityCommonResponse response = buildMemberActivityCommonResponse(activity);
        MemberUpgradeActivityDetailResponse ans = convertUpgradeDetail(response);
        ans.setCarriers(convertMemberUpgradeGearsDtoList(activity.getGearInfos()));
        MemberCardUpgradeGearDto dto = activity.getGearInfos().iterator().next();
        ans.setCardId(dto.getMemberCardId());
        ans.setCardName(dto.getMemberCardName());
        return ans;
    }

    @Mapping(target = "storeLimitUnit", ignore = true)
    @Mapping(target = "storeLimit", ignore = true)
    @Mapping(target = "isPerpetualEffect", ignore = true)
    @Mapping(target = "carriers", ignore = true)
    MemberRechargeActivityDetailResponse convertRechargeDetail(MemberActivityCommonResponse response);

    @Mapping(target = "gearType", ignore = true)
    @Mapping(target = "gearDesc", ignore = true)
    @Mapping(target = "benefitDtoList", source = "benefitInfos")
    @Mapping(target = "discountAmount", source = "saleAmount")
    MemberRechargeGearsDto convertMemberRechargeGearsDto(MemberRechargeGearDto dto);

    default MemberRechargeActivityDetailResponse buildRechargeActivityResp(MemberRechargeActivityDetailResp activity) {
        MemberActivityCommonResponse response = buildMemberActivityCommonResponse(activity);
        MemberRechargeActivityDetailResponse ans = convertRechargeDetail(response);
        ans.setStoreLimit(activity.getGiftAmountEffectTime());
        ans.setStoreLimitUnit(activity.getGiftAmountEffectTimeUnit());
        ans.setIsPerpetualEffect(activity.getIsLongTerm());
        ans.setCarriers(activity.getGearInfos().stream().map(this::convertMemberRechargeGearsDto).collect(Collectors.toList()));
        return ans;
    }

    @Mapping(target = "issueNodes", ignore = true)
    @Mapping(target = "exchangePoint", ignore = true)
    @Mapping(target = "exchangeAmount", ignore = true)
    MemberPointsActivityDetailResponse convertPointsActivityDetail(MemberActivityCommonResponse response);

    default MemberPointsActivityDetailResponse buildPointsActivityResp(MemberPointActivityDetailResp activity) {
        MemberActivityCommonResponse response = buildMemberActivityCommonResponse(activity);
        MemberPointsActivityDetailResponse ans = convertPointsActivityDetail(response);
        ans.setIssueNodes(activity.getIssueNodes());
        for (MemberPointGearDto memberPointGearDto : activity.getGearInfos()) {
            ans.setExchangeAmount(memberPointGearDto.getConsumptionAmount());
            for (DiscountBenefitDto benefitDto : memberPointGearDto.getBenefitInfos()) {
                if (Objects.equals(benefitDto.getDiscountType(), BenefitTypeEnum.POINT.getCode())) {
                    ans.setExchangePoint(Integer.parseInt(benefitDto.getBenefitValue()));
                    break;
                }
            }
        }
        return ans;
    }

    @Mapping(target = "scopeSource", expression = "java(com.ly.titc.common.enums.ScopeSourceEnum.BLOC.getCode())")
    @Mapping(target = "platformChannel", expression = "java(com.ly.titc.common.enums.PlatformChannelEnum.CRS.getPlatformChannel())")
    @Mapping(target = "hotelCode", ignore = true)
    GetSellableCardReq convertGetSellableCardReq(QuerySellableCardRequest request);

    MemberSellableCardResponse convertMemberSellableCardResponse(MemberSellableCardResp resp);

    List<MemberSellableCardResponse> convertMemberSellableCardResponseList(List<MemberSellableCardResp> ans);

    @Mapping(target = "hotelCode", ignore = true)
    GetActiveMemberActivityReq convertGetActiveMemberActivityReq(QueryMemberActiveActivityRequest request);


}
