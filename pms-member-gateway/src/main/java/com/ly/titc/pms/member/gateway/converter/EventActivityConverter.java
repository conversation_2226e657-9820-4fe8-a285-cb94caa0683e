package com.ly.titc.pms.member.gateway.converter;

import com.ly.titc.common.constants.Constant;
import com.ly.titc.pms.member.com.constant.SystemConstant;
import com.ly.titc.pms.member.gateway.entity.request.spm.activity.*;
import com.ly.titc.pms.member.gateway.entity.request.spm.activity.dto.ApplicableMemberLevelDto;
import com.ly.titc.pms.member.gateway.entity.request.spm.activity.dto.EventActivityExtendDto;
import com.ly.titc.pms.member.gateway.entity.request.spm.activity.dto.EventActivityGearDto;
import com.ly.titc.pms.member.gateway.entity.request.spm.activity.dto.StoredValueRuleDto;
import com.ly.titc.pms.member.gateway.entity.response.spm.activity.EventActivityDetailResponse;
import com.ly.titc.pms.member.gateway.entity.response.spm.activity.EventActivityListResponse;
import com.ly.titc.pms.member.gateway.entity.response.spm.activity.EventActivityTypeResponse;
import com.ly.titc.pms.member.gateway.entity.response.spm.activity.EventGrantInfoResponse;
import com.ly.titc.pms.spm.dubbo.entity.dto.activity.gear.EventGearDto;
import com.ly.titc.pms.spm.dubbo.entity.dto.rule.use.EventActivityUseRuleDto;
import com.ly.titc.pms.spm.dubbo.entity.dto.rule.use.EventCumulativeDto;
import com.ly.titc.pms.spm.dubbo.entity.request.activity.*;
import com.ly.titc.pms.spm.dubbo.entity.response.activity.EventActivityDetailResp;
import com.ly.titc.pms.spm.dubbo.entity.response.activity.EventActivityGrantInfoResp;
import com.ly.titc.pms.spm.dubbo.entity.response.activity.EventActivityInfoResp;
import com.ly.titc.pms.spm.dubbo.enums.ApplicableScopeTypeEnum;
import com.ly.titc.pms.spm.dubbo.enums.SubActivityTypeEnum;
import org.apache.commons.lang3.math.NumberUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;
import java.util.Objects;


@Mapper(componentModel = "spring")
public interface EventActivityConverter {

    @Mapping(target = "displayTypeDesc", source = "desc")
    @Mapping(target = "displayType", source = "code")
    EventActivityTypeResponse convertEventActivityTypeResponse(SubActivityTypeEnum subActivityTypeEnum);

    GetEventActivityReq convertGetEventActivityReq(GetActivityRequest request);

    @Mapping(target = "state", ignore = true)
    UpdateActivityStateReq convertUpdateActivityStateReq(GetActivityRequest request);

    DeleteActivityReq convertDeleteActivityReq(GetActivityRequest request);

    @Mapping(target = "sourceClient", constant = "CRM")
    @Mapping(target = "activityDesc", source = "desc")
    @Mapping(target = "subActivityType", source = "displayType")
    @Mapping(target = "activityCode", ignore = true)
    @Mapping(target = "clubCode", ignore = true)
    @Mapping(target = "hotelCode", ignore = true)
    @Mapping(target = "useRuleInfo", ignore = true)
    @Mapping(target = "gearInfos", ignore = true)
    @Mapping(target = "eventCumulativeInfo", ignore = true)
    SaveEventActivityReq convertCreateEventActivityReq(CreateEventActivityRequest request);

    @Mapping(target = "cumulativeStatisticsTimeLimit", ignore = true)
    @Mapping(target = "cumulativeStatisticsTime", ignore = true)
    @Mapping(target = "cumulativeCalcGiftAmount", ignore = true)
    @Mapping(target = "cumulativeValueType", source = "storedValueType")
    @Mapping(target = "cumulativeFixedValue", source = "fixedStoredValue")
    @Mapping(target = "cumulativeStairsValueType", source = "isStairsSame")
    @Mapping(target = "cumulativeStairsValue", source = "stairsStoredValues")
    EventCumulativeDto convertEventCumulativeDto(StoredValueRuleDto dto);

    @Mapping(target = "memberCardLevelName", source = "memberCardLevelDesc")
    @Mapping(target = "cumulativeValue", source = "value")
    @Mapping(target = "benefitInfos", source = "benefitDtoList")
    EventGearDto convertEventGearDto(EventActivityGearDto gearDto);

    List<EventGearDto> convertGearInfos(List<EventActivityGearDto> gearInfos);

    @Mapping(target = "subActivityType", source = "displayType")
    @Mapping(target = "sourceClient", constant = "CRS")
    @Mapping(target = "activityDesc", source = "desc")
    @Mapping(target = "clubCode", ignore = true)
    @Mapping(target = "hotelCode", ignore = true)
    @Mapping(target = "useRuleInfo", ignore = true)
    @Mapping(target = "gearInfos", ignore = true)
    @Mapping(target = "eventCumulativeInfo", ignore = true)
    SaveEventActivityReq convertUpdateEventActivityReq(UpdateEventActivityRequest request);

    @Mapping(target = "gearList", ignore = true)
    @Mapping(target = "extendDto", ignore = true)
    @Mapping(target = "displayTypeDesc", source = "subActivityTypeName")
    @Mapping(target = "displayType", source = "subActivityType")
    EventActivityDetailResponse convertEventActivityDetailResponse(EventActivityDetailResp resp);

    @Mapping(target = "storedValueType", source = "cumulativeValueType")
    @Mapping(target = "stairsStoredValues", source = "cumulativeStairsValue")
    @Mapping(target = "isStairsSame", source = "cumulativeStairsValueType")
    @Mapping(target = "fixedStoredValue", source = "cumulativeFixedValue")
    StoredValueRuleDto convertEventActivityExtendDto(EventCumulativeDto eventCumulativeDto);

    @Mapping(target = "value", source = "cumulativeValue")
    @Mapping(target = "memberCardLevelDesc", source = "memberCardLevelName")
    @Mapping(target = "benefitDtoList", source = "benefitInfos")
    EventActivityGearDto convertEventActivityGearDto(EventGearDto gearDto);

    List<EventActivityGearDto> convertEventActivityGearInfos(List<EventGearDto> gearInfos);

    @Mapping(target = "clubCode", ignore = true)
    @Mapping(target = "hotelCode", ignore = true)
    @Mapping(target = "sourceClient", ignore = true)
    @Mapping(target = "currPage", source = "pageIndex")
    @Mapping(target = "subActivityType", source = "displayType")
    @Mapping(target = "activityType", expression = "java(com.ly.titc.pms.spm.dubbo.enums.ActivityTypeEnum.EVENT_ACTIVITY.getCode())")
    PageEventActivityInfoReq convertPageEventActivityInfoReq(PageActivityRequest pageActivityRequest);

    @Mapping(target = "priority", ignore = true)
    @Mapping(target = "displayTypeDesc", source = "subActivityTypeName")
    @Mapping(target = "displayType", source = "subActivityType")
    @Mapping(target = "displayState", source = "state")
    EventActivityListResponse convertEventActivityListResponse(EventActivityInfoResp ans);

    List<EventActivityListResponse> convertEventActivityListResponseList(List<EventActivityInfoResp> ans);

    @Mapping(target = "subActivityType", source = "displayType")
    @Mapping(target = "hotelCode", ignore = true)
    @Mapping(target = "grantStartTime", source = "triggerStartTime")
    @Mapping(target = "grantEndTime", source = "triggerEndTime")
    @Mapping(target = "currPage", source = "pageIndex")
    @Mapping(target = "benefitType", source = "discountType")
    @Mapping(target = "activityType", expression = "java(com.ly.titc.pms.spm.dubbo.enums.ActivityTypeEnum.EVENT_ACTIVITY.getCode())")
    PageActivityGrantInfoReq convertPageActivityGrantInfoReq(PageGrantInfoRequest request);


    @Mapping(target = "triggerRule", source = "remark")
    @Mapping(target = "displayTypeDesc", source = "subActivityTypeName")
    @Mapping(target = "discountTypeDesc", source = "benefitTypeName")
    @Mapping(target = "discountCount", source = "benefitCount")
    @Mapping(target = "discountBizName", source = "benefitValueName")
    EventGrantInfoResponse convertEventGrantInfoResponse(EventActivityGrantInfoResp resp);

    List<EventGrantInfoResponse> convertEventGrantInfoResponseList(List<EventActivityGrantInfoResp> ans);

    default EventActivityExtendDto buildEventActivityExtendDto(EventActivityDetailResp resp) {
        EventActivityExtendDto extendDto = new EventActivityExtendDto();
        if (Objects.nonNull(resp.getCumulativeRuleInfo())) {
            extendDto.setStoredValueRule(convertEventActivityExtendDto(resp.getCumulativeRuleInfo()));
            extendDto.setCalcGiftAmount(resp.getCumulativeRuleInfo().getCumulativeCalcGiftAmount());
            extendDto.setCumulativeTimeRange(
                    Objects.equals(resp.getCumulativeRuleInfo().getCumulativeStatisticsTimeLimit(), NumberUtils.INTEGER_ZERO)
                            ? SystemConstant.FOREVER : SystemConstant.ONE_YEAR);
        }
        if (Objects.nonNull(resp.getUseRuleInfo())) {
            extendDto.setApplicableHotelsDto(resp.getUseRuleInfo().getHotelInfo());
            extendDto.setApplicablePlatformChannels(resp.getUseRuleInfo().getChannelInfos());
            extendDto.setApplicableMemberLevel(buildApplicableMemberLevelDto(resp.getUseRuleInfo().getMemberLevels()));
            extendDto.setApplicableUserTypes(resp.getUseRuleInfo().getUserTypes());
            extendDto.setApplicableRentModes(resp.getUseRuleInfo().getRentModes());
            extendDto.setStackableActivityTypeList(resp.getUseRuleInfo().getStackSubTypes());
        }
        return extendDto;
    }

    default ApplicableMemberLevelDto buildApplicableMemberLevelDto(List<String> memberLevels) {
        ApplicableMemberLevelDto memberLevelDto = new ApplicableMemberLevelDto();
        memberLevelDto.setApplicableScopeType(ApplicableScopeTypeEnum.APPOINT_AVAILABLE.getScopeType());
        memberLevelDto.setMemberLevelCodes(memberLevels);
        return memberLevelDto;
    }


    default EventActivityDetailResponse buildEventActivityDetailResponse(EventActivityDetailResp resp) {
        EventActivityDetailResponse response = convertEventActivityDetailResponse(resp);
        response.setGearList(convertEventActivityGearInfos(resp.getGearInfos()));
        response.setExtendDto(buildEventActivityExtendDto(resp));
        return response;
    }

    default EventCumulativeDto buildEventCumulativeDto(EventActivityExtendDto extendDto) {
        EventCumulativeDto eventCumulativeDto;
        if (Objects.nonNull(extendDto.getStoredValueRule())) {
            eventCumulativeDto = convertEventCumulativeDto(extendDto.getStoredValueRule());
        } else {
            eventCumulativeDto = new EventCumulativeDto();
        }
        eventCumulativeDto.setCumulativeCalcGiftAmount(extendDto.getCalcGiftAmount());
        if (Objects.nonNull(extendDto.getCumulativeTimeRange())) {
            if (Objects.equals(extendDto.getCumulativeTimeRange(), SystemConstant.FOREVER)) {
                eventCumulativeDto.setCumulativeStatisticsTimeLimit(NumberUtils.INTEGER_ZERO);//不限
            } else {
                eventCumulativeDto.setCumulativeStatisticsTimeLimit(NumberUtils.INTEGER_ONE);//限制
                eventCumulativeDto.setCumulativeStatisticsTime(String.valueOf(Constant.ONE));//限制一年
            }
        }
        return eventCumulativeDto;
    }

    default EventActivityUseRuleDto buildEventActivityUseRuleDto(EventActivityExtendDto extendDto) {
        EventActivityUseRuleDto eventActivityUseRuleDto = new EventActivityUseRuleDto();
        eventActivityUseRuleDto.setStackSubTypes(extendDto.getStackableActivityTypeList());
        eventActivityUseRuleDto.setUserTypes(extendDto.getApplicableUserTypes());
        eventActivityUseRuleDto.setRentModes(extendDto.getApplicableRentModes());
        eventActivityUseRuleDto.setChannelInfos(extendDto.getApplicablePlatformChannels());
        eventActivityUseRuleDto.setHotelInfo(extendDto.getApplicableHotelsDto());
        if (Objects.nonNull(extendDto.getApplicableMemberLevel())) {
            eventActivityUseRuleDto.setMemberLevels(extendDto.getApplicableMemberLevel().getMemberLevelCodes());
        }
        return eventActivityUseRuleDto;
    }

    default SaveEventActivityReq buildSaveEventActivityReq(CreateEventActivityRequest request) {
        SaveEventActivityReq req = convertCreateEventActivityReq(request);
        req.setEventCumulativeInfo(buildEventCumulativeDto(request.getExtendDto()));
        req.setUseRuleInfo(buildEventActivityUseRuleDto(request.getExtendDto()));
        req.setGearInfos(convertGearInfos(request.getCarrierDtoList()));
        return req;
    }

    default SaveEventActivityReq buildUpdateEventActivityReq(UpdateEventActivityRequest request) {
        SaveEventActivityReq req = convertUpdateEventActivityReq(request);
        req.setEventCumulativeInfo(buildEventCumulativeDto(request.getExtendDto()));
        req.setUseRuleInfo(buildEventActivityUseRuleDto(request.getExtendDto()));
        req.setGearInfos(convertGearInfos(request.getCarrierDtoList()));
        return req;
    }
}
