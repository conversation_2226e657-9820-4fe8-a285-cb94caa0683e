package com.ly.titc.pms.member.gateway.entity.response.spm.giftpack;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024-12-25 17:47
 */
@Data
public class  GrantGiftPackDetailResponse implements Serializable {

    /**
     * 礼包明细产品类型
     * 1-优惠券 2-会员卡 3-积分 4-会员权益 5-联名权益
     */
    private Integer grantItemType;

    /**
     * 明细业务类型描述
     */
    private String grantItemTypeDesc;

    /**
     * 明细业务编码
     */
    private String grantBizNo;

    /**
     * 礼包编号
     */
    private String giftPackNo;

    /**
     * 礼包名字
     */
    private String giftPackName;

    /**
     * 发放时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime grantTime;

    /**
     * 是否可退
     */
    private Boolean refundable;
}
