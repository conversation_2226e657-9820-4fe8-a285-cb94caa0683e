package com.ly.titc.pms.member.gateway.controller.member;

import com.ly.titc.common.entity.Pageable;
import com.ly.titc.common.entity.Response;
import com.ly.titc.pms.member.com.lang.TwoTuple;
import com.ly.titc.pms.member.com.utils.ConfigCenterUtil;
import com.ly.titc.pms.member.gateway.converter.MemberDataConverter;
import com.ly.titc.pms.member.gateway.entity.request.member.MemberBaseRequest;
import com.ly.titc.pms.member.gateway.entity.request.member.record.PageCheckinRecordRequest;
import com.ly.titc.pms.member.gateway.entity.request.member.record.PageLevelChangeRecordRequest;
import com.ly.titc.pms.member.gateway.entity.request.member.record.PagePurchaseCardRecordRequest;
import com.ly.titc.pms.member.gateway.entity.response.data.CheckInStatisticsResponse;
import com.ly.titc.pms.member.gateway.entity.response.data.CheckinRecordResponse;
import com.ly.titc.pms.member.gateway.entity.response.data.MemberLevelChangeRecordResponse;
import com.ly.titc.pms.member.gateway.entity.response.data.PurchaseCardRecordResponse;
import com.ly.titc.pms.member.mediator.entity.dto.data.CheckInRecordDto;
import com.ly.titc.pms.member.mediator.entity.dto.data.MemberLevelChangeRecordDto;
import com.ly.titc.pms.member.mediator.entity.dto.data.PurchaseCardRecordDto;
import com.ly.titc.pms.member.mediator.rpc.dubbo.ecrm.MemberCardInfoDecorator;
import com.ly.titc.pms.member.mediator.service.MemberDataRecordMedService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.stream.Collectors;

/**
 * 会员数据记录
 * @Author：rui
 * @name：MemberDataController
 * @Date：2024-12-11 21:22
 * @Filename：MemberDataController
 */
@Slf4j
@RestController
@RequestMapping("/member/data")
public class MemberDataController {

    @Resource
    private MemberDataConverter memberDataConverter;

    @Resource
    private MemberDataRecordMedService memberDataRecordMedService;

    /**
     * 查询会员入住记录
     */
    @RequestMapping("/pageCheckInRecord")
    public Response<Pageable<CheckinRecordResponse>> pageCheckinRecord(@RequestBody @Valid PageCheckinRecordRequest request) {
        Pageable<CheckInRecordDto> pageable = memberDataRecordMedService.pageCheckinRecord(memberDataConverter.convertPageCheckinRecordDto(request));
        return Response.success(Pageable.convert(pageable, memberDataConverter.convertPageCheckinRecord(pageable.getDatas())));
    }

    /**
     * 查询会员入住统计
     */
    @RequestMapping("/getCheckInStatistics")
    public Response<CheckInStatisticsResponse> getCheckInStatistics(@RequestBody @Valid MemberBaseRequest request) {
        return Response.success(memberDataConverter.convertCheckInStatistics(memberDataRecordMedService.getCheckInStatistics(request.getMemberNo())));
    }

    /**
     * 查询会员购卡记录
     */
    @RequestMapping("/pagePurchaseCardRecord")
    public Response<Pageable<PurchaseCardRecordResponse>> pagePurchaseCardRecord(@RequestBody @Valid PagePurchaseCardRecordRequest request) {
        Pageable<PurchaseCardRecordDto> pageable = memberDataRecordMedService.pagePurchaseCardRecord(memberDataConverter.convertPagePurchaseCardRecordDto(request, request.getMasterType(), request.getMasterCode()));
        return Response.success(Pageable.convert(pageable, pageable.getDatas().stream().map(memberDataConverter::convertPurchaseCardRecordResponse).collect(Collectors.toList())));
    }

    /**
     * 查询会员升降级记录
     */
    @RequestMapping("/pageLevelChangeRecord")
    public Response<Pageable<MemberLevelChangeRecordResponse>> pageLevelChangeRecord(@RequestBody @Valid PageLevelChangeRecordRequest request) {
        TwoTuple<Integer, String> tuple = ConfigCenterUtil.getMasterByBloc(request.getBlocCode());
        Pageable<MemberLevelChangeRecordDto> pageable = memberDataRecordMedService.pageMemberLevelChangeRecord(memberDataConverter.convertPageLevelChangeRecordDto(request, tuple.getFirst(), tuple.getSecond()));
        return Response.success(Pageable.convert(pageable, pageable.getDatas().stream().map(memberDataConverter::convertMemberLevelChangeRecordResponse).collect(Collectors.toList())));
    }

}
