package com.ly.titc.pms.member.gateway.entity.response.spm.giftpack;

import lombok.Data;
import lombok.experimental.Accessors;


@Data
@Accessors(chain = true)
public class GiftPackBatchListResponse {

    /**
     * 发放批次号
     */
    private String batchNo;

    /**
     * 礼包编码
     */
    private String giftPackNo;

    /**
     * 礼包名
     */
    private String giftPackName;

    /**
     * 发放数量
     */
    private Integer grantNum;

    /**
     * 发放时间
     */
    private String grantDate;

    /**
     * 发放事件
     */
    private String grantEventName;

    /**
     * 礼包版本
     */
    private Integer version;

    /**
     * 发放来源
     */
    private String sourceClient;

    /**
     * 状态  0-无效 1-有效
     */
    private Integer state;

    /**
     * 操作人
     */
    private String operator;
}
