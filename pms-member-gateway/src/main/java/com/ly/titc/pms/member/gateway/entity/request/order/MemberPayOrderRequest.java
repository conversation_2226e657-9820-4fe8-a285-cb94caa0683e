package com.ly.titc.pms.member.gateway.entity.request.order;

import com.ly.titc.cashier.dubbo.enums.PayProductEnum;
import com.ly.titc.common.annotation.LegalEnum;
import com.ly.titc.pms.member.com.enums.AmountTypeEnum;
import com.ly.titc.pms.member.gateway.entity.request.BaseRequest;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-11-27 16:31
 */
@Data
@Accessors(chain = true)
public class MemberPayOrderRequest extends BaseRequest {
    /**
     * 会员订单号
     */
    @NotEmpty(message = "会员订单号不能为空")
    private String memberOrderNo;


    /**
     * 金额 （保留到分）
     */
    @NotNull(message = "支付金额不能为空")
    private BigDecimal amount;



}
