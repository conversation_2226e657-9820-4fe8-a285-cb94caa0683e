package com.ly.titc.pms.member.gateway.entity.request.hotel;

import com.ly.titc.pms.member.gateway.entity.request.BaseRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * @Description:
 * @Author: huaizhu.zhong
 * @Date: 2024-12-12
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SelectHotelsByFuzzyRequest extends BaseRequest {

    /**
     * 酒店名称 或 code
     */
    private String fuzzyNameOrCode;

    /**
     * 区域id
     */
    private Long areaId;

    /**
     * 国家id
     */
    private Integer countryId;

    /**
     * 省
     */
    private Integer provinceId;

    /**
     * 市
     */
    private Integer cityId;

    /**
     * 区
     */
    private Integer districtId;

    /**
     * 品牌code
     */
    private String brandCode;

    /**
     * 状态
     */
    private Integer state;

    /**
     * 酒店编码
     */
    private List<String> hotelCodes;
}
