package com.ly.titc.pms.member.gateway.converter;

import com.ly.titc.chm.entity.response.BlocRoomTypeResp;
import com.ly.titc.mdm.entity.response.hotel.room.type.RoomTypeBaseInfoResp;
import com.ly.titc.pms.member.gateway.entity.response.room.BlocRoomTypeInfoResponse;
import com.ly.titc.pms.member.gateway.entity.response.room.RoomTypeInfoResponse;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * @Description:
 * @Author: huaizhu.zhong
 * @Date: 2024-12-13
 */
@Mapper(componentModel = "spring")
public interface RoomTypeConverter {
    RoomTypeInfoResponse convertRoomTypeInfoResponse(RoomTypeBaseInfoResp resp);
    List<RoomTypeInfoResponse> convertRoomTypeInfoResponses(List<RoomTypeBaseInfoResp> roomTypeBaseInfoResps);

    BlocRoomTypeInfoResponse convertBlocRoomTypeInfoResponse(BlocRoomTypeResp resp);
    List<BlocRoomTypeInfoResponse> convertBlocRoomTypeInfoResponses(List<BlocRoomTypeResp> blocRoomTypeResps);
}
