package com.ly.titc.pms.member.gateway.entity.response.customer;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 客户信息返回
 *
 * <AUTHOR>
 * @date 2024/12/11 19:16
 */
@Data
public class CustomerInfoResponse {

    /**
     * 集团编号
     */
    private String blocCode;

    /**
     * 酒店编号
     */
    private String hotelCode;

    /**
     * 客户编号
     */
    private String customerNo;

    /**
     * 姓名
     */
    private String realName;

    /**
     * 昵称
     */
    private String nickName;

    /**
     * 英文名
     */
    private String enName;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 证件号分类
     */
    private Integer idType;

    /**
     * 证件号
     */
    private String idNo;

    /**
     * 性别 1:男;2:女;3:保密
     */
    private Integer gender;

    /**
     * 生日
     */
    private String birthday;

    /**
     * 状态 1 有效 2 注销
     */
    private Integer state;

    /**
     * 转成的会员号
     */
    private String memberNo;

    /**
     * 会员等级
     */
    private Integer cardLevel;

    /**
     * 会员等级名称
     */
    private String cardLevelName;

    /**
     * 当前在住房间
     */
    private String roomNo;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 修改人
     */
    private String modifyUser;

    /**
     * 创建时间
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime gmtModified;

}
