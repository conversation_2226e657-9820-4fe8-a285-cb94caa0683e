package com.ly.titc.pms.member.gateway.entity.response.blacklist;

import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ly.titc.pms.member.mediator.entity.dto.general.DictDto;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @Author：rui
 * @name：QueryBlacklistedResponse
 * @Date：2024-12-10 20:16
 * @Filename：QueryBlacklistedResponse
 */
@Data
public class BlacklistedResponse {

    /**
     * 黑名单编号
     */
    private String blacklistNo;

    /**
     * 归属（1:集团;2:门店;3:艺龙）
     */
    private Integer masterType;

    /**
     * 归属值
     */
    private String masterCode;

    /**
     * 会员编号
     */
    private String memberNo;

    /**
     * 场景适用范围 0 部分 1 全部
     */
    private Integer sceneScope;

    /**
     * 适用场景列表
     */
    private List<DictDto> scenes;

    /**
     * 渠道适用范围 0 部分 1 全部
     */
    private Integer platformChannelScope;

    /**
     * 适用渠道列表
     */
    private List<DictDto> platformChannels;

    /**
     * 原因
     */
    private String reason;

    /**
     * 状态 0 无效 1 有效
     */
    private Integer state;

    /**
     * 来源类型
     */
    private Integer sourceType;

    /**
     * 来源（集团/门店）
     */
    private String source;

    /**
     * 来源名称
     */
    private String sourceName;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 修改人
     */
    private String modifyUser;

    /**
     * 创建时间
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime gmtModified;

}
