package com.ly.titc.pms.member.gateway.entity.request.spm.giftpack;

import com.ly.titc.pms.member.gateway.entity.request.PageBaseRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024-11-29 10:45
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class PageGiftPackBatchDetailRequest extends PageBaseRequest {
    /**
     * 接收者编码
     */
    private String receiverCode;

    /**
     * 接收者类型 customer-客户 member-会员
     */
    private Integer receiverType;

    /**
     * 发放批次号
     */
    @NotBlank(message = "批次号不能为空")
    private String batchNo;


}
