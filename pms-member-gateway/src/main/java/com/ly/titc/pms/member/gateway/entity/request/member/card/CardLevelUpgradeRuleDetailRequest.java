package com.ly.titc.pms.member.gateway.entity.request.member.card;

import lombok.Data;

/**
 * @Author：rui
 * @name：CardLevelUpgradeRuleDetailRequest
 * @Date：2024-11-14 14:16
 * @Filename：CardLevelUpgradeRuleDetailRequest
 */
@Data
public class CardLevelUpgradeRuleDetailRequest {

    private Long id;

    /**
     * 条件类型： 0-入住次数 1-入住房晚 2-充值金额 3-消费金额（不含赠送） 4-积分 5-平均房费 6-未入住天数 7-注册天数
     */
    private Integer conditionType;

    /**
     * 条件值
     */
    private String conditionValue;

    /**
     * 计算方式计算方式 1 大于等于 2 大于 3 小于等于 4 小于
     */
    private Integer calculateType;
}
