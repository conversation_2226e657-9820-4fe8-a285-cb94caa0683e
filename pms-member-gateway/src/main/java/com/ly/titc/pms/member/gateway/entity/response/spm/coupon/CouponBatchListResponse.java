package com.ly.titc.pms.member.gateway.entity.response.spm.coupon;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 券发放批次列表
 * @Description
 * <AUTHOR>
 * @Date 2024-11-6 17:52
 */
@Data
@Accessors(chain = true)
public class CouponBatchListResponse {

    /**
     * 批次号
     */
    private String batchCode;

    /**
     * 券类型
     */
    private Integer couponType;

    /**
     * 券类型名
     */
    private String couponTypeName;

    /**
     * 券名称
     */
    private String couponName;

    /**
     * 生成门店
     */
    private String hotelName;

    /**
     * 券模板code
     */
    private String templateCode;

    /**
     * 发放数量
     */
    private Integer grantNum;

    /**
     * 是否支持跨店使用
     * 1:支持 0:不支持
     */
    private Integer crossShop;

    /**
     * 是否发布短信通知
     * 1:是 0:否
     */
    private Integer smsNotice;

    /**
     * 优惠券模版版本
     */
    private String version;

    /**
     * 发放时间
     */
    private String grantTime;

    /**
     * 发放来源
     */
    private String sourceClient;

    /**
     * 操作人
     */
    private String operator;

}
