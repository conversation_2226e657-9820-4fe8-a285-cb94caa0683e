package com.ly.titc.pms.member.gateway.converter;

import com.ly.titc.pms.account.dubbo.entity.response.SettlementInfoResp;
import com.ly.titc.pms.member.gateway.entity.response.account.PostingItemResponse;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * @Description:
 * @Author: huaizhu.zhong
 * @Date: 2025-2-8
 */
@Mapper(componentModel = "spring")
public interface AccountConverter {

    PostingItemResponse convertSettlementItemResponse(SettlementInfoResp settlementInfoResp);
    List<PostingItemResponse> convertSettlementItemResponses(List<SettlementInfoResp> settlementInfos);
}
