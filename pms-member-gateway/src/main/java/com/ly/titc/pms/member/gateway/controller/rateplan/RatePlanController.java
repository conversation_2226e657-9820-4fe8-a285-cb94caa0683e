package com.ly.titc.pms.member.gateway.controller.rateplan;

import com.ly.titc.cdm.dubbo.entity.response.plan.HotelRatePlanListResp;
import com.ly.titc.chm.entity.response.BlocRatePlanInfoResp;
import com.ly.titc.common.entity.Response;
import com.ly.titc.mdm.entity.response.hotel.PageHotelsResp;
import com.ly.titc.oauth.client.threadlocal.UserThreadHolder;
import com.ly.titc.pms.member.gateway.converter.RatePlanConverter;
import com.ly.titc.pms.member.gateway.entity.request.ratePlan.ListBlocRatePlanRequest;
import com.ly.titc.pms.member.gateway.entity.request.ratePlan.ListRatePlanRequest;
import com.ly.titc.pms.member.gateway.entity.response.rateplan.BlocRatePlanListResponse;
import com.ly.titc.pms.member.gateway.entity.response.rateplan.RatePlanListResponse;
import com.ly.titc.pms.member.gateway.thread.local.UserInfoThreadHolder;
import com.ly.titc.pms.member.mediator.entity.dto.user.UserInfoDto;
import com.ly.titc.pms.member.mediator.rpc.dsf.chm.ChmRatePlanDecorator;
import com.ly.titc.pms.member.mediator.rpc.dsf.mdm.HotelDecorator;
import com.ly.titc.pms.member.mediator.rpc.dubbo.cdm.RatePlanDecorator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 价格方案管理
 *
 * @Description:
 * @Author: huaizhu.zhong
 * @Date: 2024-12-13
 */
@Slf4j
@RestController
@RequestMapping("/ratePlan")
public class RatePlanController {
    @Resource(type = HotelDecorator.class)
    private HotelDecorator hotelDecorator;
    @Resource(type = RatePlanDecorator.class)
    private RatePlanDecorator ratePlanDecorator;
    @Resource(type = RatePlanConverter.class)
    private RatePlanConverter ratePlanConverter;
    @Resource(type = ChmRatePlanDecorator.class)
    private ChmRatePlanDecorator chmRatePlanDecorator;


    @PostMapping("/list")
    public Response<List<RatePlanListResponse>> listRatePlan(@RequestBody ListRatePlanRequest request){
        Map<String,String> hotelCodeAndNameMap = new HashMap<>();
        List<PageHotelsResp> pageHotels = hotelDecorator.selectHotelsByFuzzy(request.getBlocCode(),request.getHotelFuzzyNameOrCode(),request.getState());
        if(!CollectionUtils.isEmpty(pageHotels)){
            hotelCodeAndNameMap = pageHotels.stream().collect(
                    Collectors.toMap(PageHotelsResp::getHotelCode,PageHotelsResp::getHotelName,(o1,o2)->o2));
        }
        List<String> hotelCodes = new ArrayList<>(hotelCodeAndNameMap.keySet());
        if(!CollectionUtils.isEmpty(request.getHotelCodes())){
            if(CollectionUtils.isEmpty(hotelCodes)){
                hotelCodes = request.getHotelCodes();
            }else{
                hotelCodes.retainAll(request.getHotelCodes());
            }
        }
        List<HotelRatePlanListResp> hotelRatePlanList = ratePlanDecorator.listRatePlan(request.getBlocCode(),
                request.getLkNameOrCode(), hotelCodes, request.getState());
        return Response.success(ratePlanConverter.convertRatePlanListResponses(hotelRatePlanList,hotelCodeAndNameMap));
    }

    /**
     * 集团价格方案列表
     * @param request
     * @return
     */
    @PostMapping("/bloc/list")
    public Response<List<BlocRatePlanListResponse>> listBlocRatePlan(@RequestBody ListBlocRatePlanRequest request){
        List<BlocRatePlanInfoResp> blocRatePlanInfos = chmRatePlanDecorator.listBlocRatePlanByRentMode(request.getBlocCode(),request.getRentMode());
        return Response.success(ratePlanConverter.convertBlocRatePlanListResponses(blocRatePlanInfos));
    }

}
