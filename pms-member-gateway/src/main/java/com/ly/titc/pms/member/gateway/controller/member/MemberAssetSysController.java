package com.ly.titc.pms.member.gateway.controller.member;

import com.ly.titc.common.entity.Response;
import com.ly.titc.pms.ecrm.dubbo.entity.request.MemberAssetSysConfigGetReq;
import com.ly.titc.pms.ecrm.dubbo.entity.request.MemberAssetSysConfigReq;
import com.ly.titc.pms.ecrm.dubbo.entity.response.MemberAssetSysConfigResp;
import com.ly.titc.pms.member.dubbo.enums.MasterTypeEnum;
import com.ly.titc.pms.member.gateway.converter.MemberConfigConverter;
import com.ly.titc.pms.member.gateway.entity.request.BaseRequest;
import com.ly.titc.pms.member.gateway.entity.request.sysConfig.MemberAssetSysConfigRequest;
import com.ly.titc.pms.member.gateway.entity.response.config.MemberAssetSysConfigResponse;
import com.ly.titc.pms.member.mediator.rpc.dubbo.ecrm.MemberAssetSysConfigDecorator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 会员储值资产系统设置
 *
 * <AUTHOR>
 * @date 2025/3/31
 */
@Slf4j
@RestController
@RequestMapping("/member/asset/sys/config")
public class MemberAssetSysController {

    @Resource(type = MemberAssetSysConfigDecorator.class)
    private MemberAssetSysConfigDecorator configDecorator;
    @Resource
    private MemberConfigConverter memberConfigConverter;

    /**
     * 保存会员储值卡设置
     */
    @PostMapping("/save")
    public Response<Boolean> save(@RequestBody @Valid MemberAssetSysConfigRequest request) {
        MemberAssetSysConfigReq req = memberConfigConverter.convert(request);
        req.setMasterType(MasterTypeEnum.BLOC.getType());
        req.setMasterCode(request.getBlocCode());
        configDecorator.save(req);
        return Response.success(true);
    }

    /**
     * 查询会员储值卡设置
     */
    @PostMapping("/get")
    public Response<MemberAssetSysConfigResponse> get(@RequestBody BaseRequest request) {
        MemberAssetSysConfigGetReq req = new MemberAssetSysConfigGetReq();
        req.setMasterType(MasterTypeEnum.BLOC.getType());
        req.setMasterCode(request.getBlocCode());
        req.setTrackingId(request.getTrackingId());
        req.setBlocCode(request.getBlocCode());
        MemberAssetSysConfigResp resp = configDecorator.get(req);

        return Response.success(memberConfigConverter.convert(resp));
    }

}
