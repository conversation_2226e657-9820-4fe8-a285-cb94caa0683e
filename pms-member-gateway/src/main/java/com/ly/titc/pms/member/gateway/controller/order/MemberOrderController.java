package com.ly.titc.pms.member.gateway.controller.order;

import com.ly.titc.common.entity.Response;
import com.ly.titc.pms.member.com.lang.TwoTuple;
import com.ly.titc.pms.member.com.utils.ConfigCenterUtil;
import com.ly.titc.pms.member.gateway.converter.MemberOrderConverter;
import com.ly.titc.pms.member.gateway.entity.request.order.*;
import com.ly.titc.pms.member.gateway.entity.response.order.CreateOrderResponse;
import com.ly.titc.pms.member.gateway.entity.response.order.CreateRefundOrderResponse;
import com.ly.titc.pms.member.gateway.entity.response.order.MemberPayOrderResponse;
import com.ly.titc.pms.member.mediator.entity.dto.member.RegisterMemberDto;
import com.ly.titc.pms.member.mediator.entity.dto.member.UpgradeMemberDto;
import com.ly.titc.pms.member.mediator.entity.dto.order.*;
import com.ly.titc.pms.member.mediator.entity.dto.recharge.MemberStoreRechargeDto;
import com.ly.titc.pms.member.mediator.service.MemberOrderMedService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 会员订单相关，注册 购卡 储值订单
 * <AUTHOR>
 * @classname
 * @descrition 会员订单相关，注册 购卡 储值订单
 * @since 2024-11-26 11:45
 */
@Slf4j
@RestController
@RequestMapping("/member/order")
public class MemberOrderController {

    @Resource
    private MemberOrderMedService orderMedService;

    @Resource
    private MemberOrderConverter orderConverter;

    /**
     * 会员注册创建订单
     */
    @PostMapping("/register/create")
    public Response<CreateOrderResponse> createOrder(@RequestBody @Valid CreateRegisterOrderRequest request) {
        CreateOrderDto<RegisterMemberDto> orderDto = orderConverter.convert(request);
        CreateOrderResultDto resultDto = orderMedService.createOrder(orderDto);
        return Response.success(orderConverter.convert(resultDto));
    }

    /**
     * 购卡订单创建
     *
     * @param request
     * @return
     */
    @PostMapping("/purchaseCard/create")
    public Response<CreateOrderResponse> createPurchaseCardOrder(@RequestBody @Valid CreatePurchaseCardOrderRequest request) {
        CreateOrderDto<PurchaseCardDto> orderDto = orderConverter.convert(request);
        CreateOrderResultDto resultDto = orderMedService.createOrder(orderDto);
        return Response.success(orderConverter.convert(resultDto));
    }

    /**
     * 升级订单创建
     */
    @PostMapping("/upgrade/create")
    public Response<CreateOrderResponse> createUpgradeOrder(@RequestBody @Valid CreateUpgradeOrderRequest request) {
        CreateOrderDto<UpgradeMemberDto> orderDto = orderConverter.convert(request);
        CreateOrderResultDto resultDto = orderMedService.createOrder(orderDto);
        return Response.success(orderConverter.convert(resultDto));
    }

    /**
     * 储值订单创建
     */
    @PostMapping("/store/create")
    public Response<CreateOrderResponse> createStoreOrder(@RequestBody @Valid CreateStoreOrderRequest request) {
        CreateOrderDto<MemberStoreRechargeDto> orderDto = orderConverter.convert(request);
        CreateOrderResultDto resultDto = orderMedService.createOrder(orderDto);
        return Response.success(orderConverter.convert(resultDto));
    }

    /**
     * 发起支付
     */
    @PostMapping("/payOrder")
    public Response<MemberPayOrderResponse> payOrder(@RequestBody @Valid MemberPayOrderRequest request) {
//        MemberPayUnifiedOrderDto resultDto = orderMedService.payUnifiedOrder(orderConverter.convert(request));
//        return Response.success(orderConverter.convert(resultDto));
        return null;
    }


    /**
     * 查询订单支付状态
     */
    @PostMapping("/getMemberBizPayState")
    public Response<MemberPayOrderResponse> getMemberBizPayState(@Valid @RequestBody GetPayStateRequest request) {
        PayOrderResultDto resultDto = orderMedService.getPayState(orderConverter.convert(request));
        return Response.success(orderConverter.convert(resultDto));
    }

    /**
     * 退款
     *
     * @param request
     * @return
     */
    @PostMapping("/refund")
    public Response<CreateRefundOrderResponse> refundOrder(@Valid @RequestBody RefundOrderRequest request) {
        TwoTuple<Integer, String> masterTuple = ConfigCenterUtil.getMasterByBloc(request.getBlocCode());
        RefundOrderDto refundOrderDto = orderConverter.convert(request, masterTuple.getFirst(), masterTuple.getSecond());
        refundOrderDto.setReason("退款");
        RefundResultDto refundResultDto = orderMedService.refundOrder(refundOrderDto);
        return Response.success(orderConverter.convert(refundResultDto));
    }

    /**
     * 查询退款状态
     *
     * @param request
     * @return
     */
    @PostMapping("/getRefundState")
    public Response<CreateRefundOrderResponse> getRefundState(@Valid @RequestBody GetRefundStateRequest request) {
        GetRefundStateDto getRefundStateDto = orderConverter.convert(request);
        RefundResultDto refundResultDto = orderMedService.getRefundState(getRefundStateDto);
        return Response.success(orderConverter.convert(refundResultDto));
    }
}
