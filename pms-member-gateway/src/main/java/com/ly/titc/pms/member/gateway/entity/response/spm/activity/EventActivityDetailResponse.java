package com.ly.titc.pms.member.gateway.entity.response.spm.activity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ly.titc.pms.member.gateway.entity.request.spm.activity.dto.EventActivityExtendDto;
import com.ly.titc.pms.member.gateway.entity.request.spm.activity.dto.EventActivityGearDto;
import com.ly.titc.pms.spm.dubbo.entity.dto.activity.gear.BaseGearDto;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2025-3-6 17:47
 */
@Data
@Accessors(chain = true)
public class EventActivityDetailResponse {

    /**
     * 活动code
     */
    private String activityCode;

    /**
     * 活动名称
     */
    private String name;

    /**
     * 活动展示类型
     */
    private String displayType;

    /**
     * 活动展示类型
     */
    private String displayTypeDesc;

    /**
     * 活动开始时间
     */
    private String effectiveStartTime;

    /**
     * 活动失效时间
     */
    private String effectiveEndTime;

    /**
     * 是否长期有效
     */
    private Boolean isLongTerm;

    /**
     * 活动描述
     */
    private String desc;

    /**
     * 事件活动扩展信息
     */
    private EventActivityExtendDto extendDto;

    /**
     * 档位
     */
    private List<EventActivityGearDto> gearList;


}
