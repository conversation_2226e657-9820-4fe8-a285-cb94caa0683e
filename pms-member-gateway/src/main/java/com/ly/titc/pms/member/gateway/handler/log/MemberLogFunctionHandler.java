package com.ly.titc.pms.member.gateway.handler.log;

import com.alibaba.excel.util.StringUtils;
import com.alibaba.fastjson.JSONObject;
import com.ly.titc.common.constants.Constant;
import com.ly.titc.pms.member.com.annotation.LogRecordFunction;
import com.ly.titc.pms.member.com.enums.*;
import com.ly.titc.pms.member.com.lang.TwoTuple;
import com.ly.titc.pms.member.com.utils.ConfigCenterUtil;
import com.ly.titc.pms.member.dubbo.enums.MasterTypeEnum;
import com.ly.titc.pms.member.gateway.entity.request.asset.point.MemberPointAdjustRequest;
import com.ly.titc.pms.member.gateway.entity.request.asset.store.MemberStoreConsumeRequest;
import com.ly.titc.pms.member.gateway.entity.request.asset.store.MemberStoreFreezeRequest;
import com.ly.titc.pms.member.gateway.entity.request.asset.store.MemberStoreUnFreezeRequest;
import com.ly.titc.pms.member.gateway.entity.request.blacklist.BlacklistedRequest;
import com.ly.titc.pms.member.gateway.entity.request.blacklist.CancelBlacklistedRequest;
import com.ly.titc.pms.member.gateway.entity.request.member.MemberBaseRequest;
import com.ly.titc.pms.member.gateway.entity.request.member.profile.*;
import com.ly.titc.pms.member.gateway.entity.request.member.SaveMemberRequest;
import com.ly.titc.pms.member.mediator.entity.dto.member.MemberCardInfoDto;
import com.ly.titc.pms.member.mediator.entity.dto.member.MemberDetailDto;
import com.ly.titc.pms.member.mediator.entity.dto.member.MemberInfoDto;
import com.ly.titc.pms.member.mediator.entity.dto.profile.MemberProfileAddressInfoDto;
import com.ly.titc.pms.member.mediator.entity.dto.profile.MemberProfileInvoiceHeaderInfoDto;
import com.ly.titc.pms.member.mediator.entity.dto.profile.MemberProfileOccupantsInfoDto;
import com.ly.titc.pms.member.mediator.rpc.dubbo.ecrm.MemberCardInfoDecorator;
import com.ly.titc.pms.member.mediator.service.MemberCardMedService;
import com.ly.titc.pms.member.mediator.service.MemberMedService;
import com.ly.titc.pms.member.mediator.service.MemberProfileMedService;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author：rui
 * @name：MemberLogFunctionHandler
 * @Date：2024-11-18 21:01
 * @Filename：MemberLogFunctionHandler
 */
@LogRecordFunction("member")
public class MemberLogFunctionHandler extends AbstractModuleServiceHandler implements ApplicationContextAware {


    private static MemberCardInfoDecorator decorator;

    private static MemberMedService memberMedService;

    private static MemberCardMedService memberCardMedService;

    private static MemberProfileMedService memberProfileMedService;

    @Override
    public List<String> execute(String blocCode, String hotelCode, String name, String trackingId) {
        return null;
    }

    @Override
    public Integer getModuleId() {
        return ModuleEnum.MEMBER_MANAGE.getCode();
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        memberMedService = applicationContext.getBean(MemberMedService.class);
        memberCardMedService = applicationContext.getBean(MemberCardMedService.class);
        decorator = applicationContext.getBean(MemberCardInfoDecorator.class);
        memberProfileMedService = applicationContext.getBean(MemberProfileMedService.class);
    }

//    @LogRecordFunction("register")
//    public static String register(MemberRegisterRequest request) {
//        JSONObject json = new JSONObject();
//        json.put("blocCode", request.getBlocCode());
//        json.put("needHandler", true);
//        List<String> records = new ArrayList<>();
//        StringBuilder stringBuilder = new StringBuilder("");
//        stringBuilder.append(String.format("【%s-todo-memberNo】注册成功，【支付金额】%s元，【注册等级】%s-%s,【会员卡号】%s", request.getRealName(), "", request.getMemberCardInfo().getCardPrice(), request.getMemberCardInfo().getCardName(), request.getMemberCardInfo().getCardLevelName(), request.getMemberCardInfo().getMemberCardNo()));
//        records.add(stringBuilder.toString());
//        json.put("records", records);
//        return json.toJSONString();
//    }

    @LogRecordFunction("update")
    public static String update(SaveMemberRequest request) {
        JSONObject json = new JSONObject();
        json.put("name", request.getMemberInfo().getMemberNo());
        json.put("blocCode", request.getBlocCode());
        List<String> records = new ArrayList<>();
        records.add(String.format("【%s-%s】修改信息", request.getMemberInfo().getRealName(), request.getMemberInfo().getMemberNo()));
        // 查询会员信息
        TwoTuple<Integer, String> masterTuple = ConfigCenterUtil.getMasterByBloc(request.getBlocCode());
        MemberDetailDto origin = memberMedService.getDetailByMemberNo(masterTuple.getFirst(), request.getBlocCode(), request.getMemberInfo().getMemberNo());
        MemberCardInfoDto memberCardInfo = memberCardMedService.getByMemberCardNo(masterTuple.getFirst(), request.getBlocCode(), origin.getCustomizeMemberNo());
        if (!origin.getRealName().equals(request.getMemberInfo().getRealName())) {
            records.add(String.format("【姓名】%s->%s", origin.getRealName(), request.getMemberInfo().getRealName()));
        }
        if (!origin.getMobile().equals(request.getMemberInfo().getMobile())) {
            records.add(String.format("【手机号】%s->%s", origin.getMobile(), request.getMemberInfo().getMobile()));
        }
        if (!origin.getNickName().equals(request.getMemberInfo().getNickName())) {
            records.add(String.format("【称呼】%s->%s", origin.getNickName(), request.getMemberInfo().getNickName()));
        }
        if (!origin.getRemark().equals(request.getMemberExtendInfo().getRemark())) {
            records.add(String.format("【备注】%s->%s", origin.getRemark(), request.getMemberExtendInfo().getRemark()));
        }
        if (!origin.getIdType().equals(request.getMemberInfo().getIdType())) {
            records.add(String.format("【证件类型】%s->%s", IdTypeEnum.getNameByType(origin.getIdType()), IdTypeEnum.getNameByType(request.getMemberInfo().getIdType())));
        }
        if (!origin.getIdNo().equals(request.getMemberInfo().getIdNo())) {
            records.add(String.format("【证件号码】%s->%s", origin.getIdNo(), request.getMemberInfo().getIdNo()));
        }
        if (!origin.getGender().equals(request.getMemberInfo().getGender())) {
            records.add(String.format("【性别】%s->%s", GenderEnum.getNameByType(origin.getGender()), GenderEnum.getNameByType(request.getMemberInfo().getGender())));
        }
        if (!origin.getBirthday().equals(request.getMemberInfo().getBirthday())) {
            records.add(String.format("【生日】%s->%s", origin.getBirthday(), request.getMemberInfo().getBirthday()));
        }
        if (!origin.getNationality().equals(request.getMemberExtendInfo().getNationality())) {
            records.add(String.format("【国籍】%s->%s", origin.getNationality(), request.getMemberExtendInfo().getNationality()));
        }
        if (!origin.getNation().equals(request.getMemberExtendInfo().getNation())) {
            records.add(String.format("【民族】%s->%s", EthnicityEnum.getLabelByValue(origin.getNation()), EthnicityEnum.getLabelByValue(request.getMemberExtendInfo().getNation())));
        }
        if (!origin.getNativePlace().equals(request.getMemberExtendInfo().getNativePlace())) {
            records.add(String.format("【籍贯】%s->%s", origin.getNativePlace(), request.getMemberExtendInfo().getNativePlace()));
        }
//        List<MemberCardConfigResp> cardConfigRespList = decorator.listCardConfig(masterTuple.getFirst(), request.getBlocCode(), request.getTrackingId());
//        Map<Long, String> map = cardConfigRespList.stream().collect(Collectors.toMap(MemberCardConfigResp::getId, MemberCardConfigResp::getCardName));
//        List<MemberCardLevelConfigInfoResp> cardLevelConfigInfoRespList = decorator.listMemberCardLevel(cardConfigRespList.stream().map(MemberCardConfigResp::getId).collect(Collectors.toList()), request.getTrackingId());
//        Map<String, String> cardLevelMap = cardLevelConfigInfoRespList.stream().collect(Collectors.toMap(item -> String.format("%s-%s", item.getCardId(), item.getCardLevel()), MemberCardLevelConfigInfoResp::getCardLevelName));
//        if (!memberCardInfo.getCardLevel().equals(memberCardInfo.getCardLevel()) || !memberCardInfo.getCardId().equals(memberCardInfo.getCardId())) {
//            stringBuilder.append(String.format("【会员等级】%s-%s->%s-%s", map.get(memberCardInfo.getCardId()), cardLevelMap.get(String.format("%s-%s", memberCardInfo.getCardId(), memberCardInfo.getCardLevel())), request.getMemberCardInfo().getCardName(), request.getMemberCardInfo().getCardLevelName()));
//        }
        if (request.getMemberContactInfo().getAddress() != null && !request.getMemberContactInfo().getAddress().equals(origin.getMemberContactInfo().getAddress()) ||
                request.getMemberContactInfo().getProvince() != null && !request.getMemberContactInfo().getProvince().equals(origin.getMemberContactInfo().getProvince())
                || request.getMemberContactInfo().getCity() != null && !request.getMemberContactInfo().getCity().equals(origin.getMemberContactInfo().getCity())
                || request.getMemberContactInfo().getDistrict() != null && !request.getMemberContactInfo().getDistrict().equals(origin.getMemberContactInfo().getDistrict())) {
            records.add(String.format("【地址】%s-%s-%s-%s->%s-%s-%s-%s", origin.getMemberContactInfo().getProvince(), origin.getMemberContactInfo().getCity(), origin.getMemberContactInfo().getDistrict(), origin.getMemberContactInfo().getAddress(), request.getMemberContactInfo().getProvince(), request.getMemberContactInfo().getCity(), request.getMemberContactInfo().getDistrict(), request.getMemberContactInfo().getAddress()));
        }
        json.put("records", records);
        return json.toJSONString();
    }

    @LogRecordFunction("addCommonAddress")
    public static String addCommonAddress(SaveCommonAddressRequest request) {
        TwoTuple<Integer, String> masterTuple = ConfigCenterUtil.getMasterByBloc(request.getBlocCode());
        MemberInfoDto memberInfo = memberMedService.getByMemberNo(masterTuple.getFirst(), masterTuple.getSecond(), request.getMemberNo());
        String memberNo = request.getMemberNo();
        JSONObject json = new JSONObject();
        json.put("name", memberNo);
        json.put("blocCode", request.getBlocCode());
        List<String> records = new ArrayList<>();
        records.add(String.format("【%s】-【%s】新增常用地址:%s", memberInfo.getRealName(), memberInfo.getMemberNo(), request.getAddress()));
        json.put("records", records);
        return json.toJSONString();
    }

    @LogRecordFunction("deleteCommonAddress")
    public static String deleteCommonAddress(DeleteCommonAddressRequest request) {
        TwoTuple<Integer, String> masterTuple = ConfigCenterUtil.getMasterByBloc(request.getBlocCode());
        MemberInfoDto memberInfo = memberMedService.getByMemberNo(masterTuple.getFirst(), masterTuple.getSecond(), request.getMemberNo());
        String memberNo = request.getMemberNo();
        JSONObject json = new JSONObject();
        json.put("name", memberNo);
        json.put("blocCode", request.getBlocCode());
        List<String> records = new ArrayList<>();
        records.add(String.format("【%s】-【%s】删除常用地址: %s", memberInfo.getRealName(), memberInfo.getMemberNo(), request.getAddress()));
        json.put("records", records);
        return json.toJSONString();
    }

    @LogRecordFunction("updateCommonAddress")
    public static String updateCommonAddress(SaveCommonAddressRequest request) {
        TwoTuple<Integer, String> masterTuple = ConfigCenterUtil.getMasterByBloc(request.getBlocCode());
        MemberInfoDto memberInfo = memberMedService.getByMemberNo(masterTuple.getFirst(), masterTuple.getSecond(), request.getMemberNo());

        String memberNo = request.getMemberNo();
        JSONObject json = new JSONObject();
        json.put("name", memberNo);
        json.put("blocCode", request.getBlocCode());
        List<String> records = new ArrayList<>();
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(String.format("【%s】-【%s】编辑常用地址:%s", memberInfo.getRealName(), memberInfo.getMemberNo(), request.getAddress()));
        MemberProfileAddressInfoDto origin = memberProfileMedService.getCommonAddress(masterTuple.getFirst(), request.getBlocCode(), memberNo, Long.parseLong(request.getAddressNo()));
        if (!origin.getName().equals(request.getName())) {
            stringBuilder.append(String.format("【姓名】%s->%s", origin.getName(), request.getName()));
        }
        if (!origin.getMobile().equals(request.getMobile())) {
            stringBuilder.append(String.format("【手机号】%s->%s", origin.getMobile(), request.getMobile()));
        }
        if (!origin.getCountry().equals(request.getCountry())) {
            stringBuilder.append(String.format("【国家】%s->%s", origin.getCountry(), request.getCountry()));
        }
        if (!origin.getProvince().equals(request.getProvince())) {
            stringBuilder.append(String.format("【省份】%s->%s", origin.getProvince(), request.getProvince()));
        }
        if (!origin.getCity().equals(request.getCity())) {
            stringBuilder.append(String.format("【城市】%s->%s", origin.getCity(), request.getCity()));
        }
        if (!origin.getDistrict().equals(request.getDistrict())) {
            stringBuilder.append(String.format("【地区】%s->%s", origin.getDistrict(), request.getDistrict()));
        }
        if (!origin.getAddress().equals(request.getAddress())) {
            stringBuilder.append(String.format("【详细地址地址】%s->%s", origin.getAddress(), request.getAddress()));
        }
        if (!origin.getTag().equals(request.getTag())) {
            stringBuilder.append(String.format("【标签】%s->%s", origin.getTag(), request.getTag()));
        }
        records.add(stringBuilder.toString());
        json.put("records", records);
        return json.toJSONString();
    }

    @LogRecordFunction("addCommonInvoiceHeader")
    public static String addCommonInvoiceHeader(SaveCommonInvoiceHeaderRequest request) {
        TwoTuple<Integer, String> masterTuple = ConfigCenterUtil.getMasterByBloc(request.getBlocCode());
        MemberInfoDto memberInfo = memberMedService.getByMemberNo(masterTuple.getFirst(), masterTuple.getSecond(), request.getMemberNo());
        String memberNo = request.getMemberNo();
        JSONObject json = new JSONObject();
        json.put("name", memberNo);
        json.put("blocCode", request.getBlocCode());
        List<String> records = new ArrayList<>();
        records.add(String.format("【%s】-【%s】新增常用发票:%s", memberInfo.getRealName(), memberInfo.getMemberNo(), request.getHeaderName()));
        json.put("records", records);
        return json.toJSONString();
    }

    @LogRecordFunction("deleteCommonInvoiceHeader")
    public static String deleteCommonInvoiceHeader(DeleteCommonInvoiceHeaderRequest request) {
        TwoTuple<Integer, String> masterTuple = ConfigCenterUtil.getMasterByBloc(request.getBlocCode());
        MemberInfoDto memberInfo = memberMedService.getByMemberNo(masterTuple.getFirst(), masterTuple.getSecond(), request.getMemberNo());
        String memberNo = request.getMemberNo();
        JSONObject json = new JSONObject();
        json.put("name", memberNo);
        json.put("blocCode", request.getBlocCode());
        List<String> records = new ArrayList<>();
        records.add(String.format("【%s】-【%s】删除常用发票:%s", memberInfo.getRealName(), memberInfo.getMemberNo(), request.getHeaderName()));
        json.put("records", records);
        return json.toJSONString();
    }

    @LogRecordFunction("updateCommonInvoiceHeader")
    public static String updateCommonInvoiceHeader(SaveCommonInvoiceHeaderRequest request) {
        TwoTuple<Integer, String> masterTuple = ConfigCenterUtil.getMasterByBloc(request.getBlocCode());
        MemberInfoDto memberInfo = memberMedService.getByMemberNo(masterTuple.getFirst(), masterTuple.getSecond(), request.getMemberNo());

        String memberNo = request.getMemberNo();
        JSONObject json = new JSONObject();
        json.put("name", memberNo);
        json.put("blocCode", request.getBlocCode());
        List<String> records = new ArrayList<>();
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(String.format(
                "【%s】-【%s】编辑常用发票:%s", memberInfo.getRealName(), memberInfo.getMemberNo(), request.getHeaderName()
        ));
        MemberProfileInvoiceHeaderInfoDto origin = memberProfileMedService.getCommonInvoiceHeader(masterTuple.getFirst(), request.getBlocCode(), memberNo, Long.parseLong(request.getInvoiceHeaderNo()));
        if (!origin.getInvoiceType().equals(request.getInvoiceType())) {
            stringBuilder.append(String.format("【发票类型】%s->%s", InvoiceTypeEnum.getNameByType(origin.getInvoiceType()), InvoiceTypeEnum.getNameByType(request.getInvoiceType())));
        }
        if (!origin.getHeaderName().equals(request.getHeaderName())) {
            stringBuilder.append(String.format("【发票抬头】%s->%s", origin.getHeaderName(), request.getHeaderName()));
        }
        if (!origin.getTaxCode().equals(request.getTaxCode())) {
            stringBuilder.append(String.format("【纳税人识别号】%s->%s", origin.getTaxCode(), request.getTaxCode()));
        }
        if (!origin.getCompanyAddress().equals(request.getCompanyAddress())) {
            stringBuilder.append(String.format("【公司地址】%s->%s", origin.getCompanyAddress(), request.getCompanyAddress()));
        }
        if (!origin.getCompanyTel().equals(request.getCompanyTel())) {
            stringBuilder.append(String.format("【公司电话】%s->%s", origin.getCompanyTel(), request.getCompanyTel()));
        }
        if (!origin.getBankName().equals(request.getBankName())) {
            stringBuilder.append(String.format("【开户行】%s->%s", origin.getBankName(), request.getBankName()));
        }
        if (!origin.getBankAccount().equals(request.getBankAccount())) {
            stringBuilder.append(String.format("【开户账号】%s->%s", origin.getBankAccount(), request.getBankAccount()));
        }
        if (!origin.getSort().equals(request.getSort())) {
            stringBuilder.append(String.format("【排序】%s->%s", origin.getBankAccount(), request.getBankAccount()));
        }
        records.add(stringBuilder.toString());
        json.put("records", records);
        return json.toJSONString();
    }

    @LogRecordFunction("addCommonOccupant")
    public static String addCommonOccupants(SaveCommonOccupantRequest request) {
        TwoTuple<Integer, String> masterTuple = ConfigCenterUtil.getMasterByBloc(request.getBlocCode());
        MemberInfoDto memberInfo = memberMedService.getByMemberNo(masterTuple.getFirst(), masterTuple.getSecond(), request.getMemberNo());
        String memberNo = request.getMemberNo();
        JSONObject json = new JSONObject();
        json.put("name", memberNo);
        json.put("blocCode", request.getBlocCode());
        List<String> records = new ArrayList<>();
        records.add(String.format("【%s】-【%s】新增常用联系人:%s", memberInfo.getRealName(), memberInfo.getMemberNo(), request.getName()));
        json.put("records", records);
        return json.toJSONString();
    }

    @LogRecordFunction("deleteCommonOccupant")
    public static String deleteCommonOccupants(DeleteCommonOccupantRequest request) {
        TwoTuple<Integer, String> masterTuple = ConfigCenterUtil.getMasterByBloc(request.getBlocCode());
        MemberInfoDto memberInfo = memberMedService.getByMemberNo(masterTuple.getFirst(), masterTuple.getSecond(), request.getMemberNo());
        String memberNo = request.getMemberNo();
        JSONObject json = new JSONObject();
        json.put("name", memberNo);
        json.put("blocCode", request.getBlocCode());
        List<String> records = new ArrayList<>();
        records.add(String.format("【%s】-【%s】删除常用联系人:%s", memberInfo.getRealName(), memberInfo.getMemberNo(), request.getName()));
        json.put("records", records);
        return json.toJSONString();
    }

    @LogRecordFunction("updateCommonOccupant")
    public static String updateCommonOccupants(SaveCommonOccupantRequest request) {
        TwoTuple<Integer, String> masterTuple = ConfigCenterUtil.getMasterByBloc(request.getBlocCode());
        MemberInfoDto memberInfo = memberMedService.getByMemberNo(masterTuple.getFirst(), masterTuple.getSecond(), request.getMemberNo());

        String memberNo = request.getMemberNo();
        JSONObject json = new JSONObject();
        json.put("name", memberNo);
        json.put("blocCode", request.getBlocCode());
        List<String> records = new ArrayList<>();
        String stringBuilder = String.format(
                "【%s】-【%s】编辑常用入住人:%s", memberInfo.getRealName(), memberInfo.getMemberNo(), request.getName()
        );
        records.add(stringBuilder);
        MemberProfileOccupantsInfoDto origin = memberProfileMedService.getCommonOccupants(masterTuple.getFirst(), request.getBlocCode(), memberNo, Long.parseLong(request.getOccupantsNo()));
        if (!origin.getName().equals(request.getName())) {
            records.add(String.format("【姓名】%s->%s", origin.getName(), request.getName()));
        }
        if (!origin.getMobile().equals(request.getMobile())) {
            records.add(String.format("【手机号】%s->%s", origin.getMobile(), request.getMobile()));
        }
        if (!origin.getIdType().equals(request.getIdType())) {
            records.add(String.format("【证件类型】%s->%s", IdTypeEnum.getNameByType(origin.getIdType()), MasterTypeEnum.getDesc(request.getIdType())));
        }
        if (!origin.getIdNo().equals(request.getIdNo())) {
            records.add(String.format("【证件号码】%s->%s", origin.getIdNo(), request.getIdNo()));
        }
        if (!StringUtils.equals(request.getTag(), origin.getTag())) {
            records.add(String.format("【标签】%s->%s", origin.getTag(), request.getTag()));
        }
        json.put("records", records);
        return json.toJSONString();
    }

    @LogRecordFunction("cancel")
    public static String cancel(MemberBaseRequest request) {
        TwoTuple<Integer, String> masterTuple = ConfigCenterUtil.getMasterByBloc(request.getBlocCode());
        MemberInfoDto memberInfo = memberMedService.getByMemberNo(masterTuple.getFirst(), masterTuple.getSecond(), request.getMemberNo());
        String memberNo = request.getMemberNo();
        JSONObject json = new JSONObject();
        json.put("name", memberNo);
        json.put("blocCode", request.getBlocCode());
        List<String> records = new ArrayList<>();
        records.add(String.format("【%s】-【%s】会员注销", memberInfo.getRealName(), memberInfo.getMemberNo()));
        json.put("records", records);
        return json.toJSONString();
    }

    @LogRecordFunction("recover")
    public static String recover(MemberBaseRequest request) {
        TwoTuple<Integer, String> masterTuple = ConfigCenterUtil.getMasterByBloc(request.getBlocCode());
        MemberInfoDto memberInfo = memberMedService.getByMemberNo(masterTuple.getFirst(), masterTuple.getSecond(), request.getMemberNo());
        String memberNo = request.getMemberNo();
        JSONObject json = new JSONObject();
        json.put("name", memberNo);
        json.put("blocCode", request.getBlocCode());
        List<String> records = new ArrayList<>();
        records.add(String.format("【%s】-【%s】会员恢复", memberInfo.getRealName(), memberInfo.getMemberNo()));
        json.put("records", records);
        return json.toJSONString();
    }


    @LogRecordFunction("blacklist")
    public static String blacklist(BlacklistedRequest request) {
        TwoTuple<Integer, String> masterTuple = ConfigCenterUtil.getMasterByBloc(request.getBlocCode());
        MemberInfoDto memberInfo = memberMedService.getByMemberNo(masterTuple.getFirst(), masterTuple.getSecond(), request.getMemberNo());
        String memberNo = request.getMemberNo();
        JSONObject json = new JSONObject();
        json.put("name", memberNo);
        json.put("blocCode", request.getBlocCode());
        List<String> records = new ArrayList<>();
        records.add(String.format("【%s】-【%s】会员被拉黑", memberInfo.getRealName(), memberInfo.getMemberNo()));
        json.put("records", records);
        return json.toJSONString();
    }

    @LogRecordFunction("cancelBlacklist")
    public static String cancelBlacklist(CancelBlacklistedRequest request) {
        TwoTuple<Integer, String> masterTuple = ConfigCenterUtil.getMasterByBloc(request.getBlocCode());
        MemberInfoDto memberInfo = memberMedService.getByMemberNo(masterTuple.getFirst(), masterTuple.getSecond(), request.getMemberNo());
        String memberNo = request.getMemberNo();
        JSONObject json = new JSONObject();
        json.put("name", memberNo);
        json.put("blocCode", request.getBlocCode());
        List<String> records = new ArrayList<>();
        records.add(String.format("【%s】-【%s】取消拉黑", memberInfo.getRealName(), memberInfo.getMemberNo()));
        json.put("records", records);
        return json.toJSONString();
    }


    @LogRecordFunction("pointAdjust")
    public static String pointAdjust(MemberPointAdjustRequest request) {
        TwoTuple<Integer, String> masterTuple = ConfigCenterUtil.getMasterByBloc(request.getBlocCode());
        MemberInfoDto memberInfo = memberMedService.getByMemberNo(masterTuple.getFirst(), masterTuple.getSecond(), request.getMemberNo());
        String memberNo = request.getMemberNo();
        JSONObject json = new JSONObject();
        json.put("name", memberNo);
        json.put("blocCode", request.getBlocCode());
        List<String> records = new ArrayList<>();
        records.add(String.format("【%s】-【%s】会员积分调整: 积分项目【%s】, 调整数量【%s】", memberInfo.getRealName(), memberInfo.getMemberNo(), request.getActionItemDesc(), request.getScore()));
        json.put("records", records);
        return json.toJSONString();
    }

    @LogRecordFunction("storeConsume")
    public static String storeConsume(MemberStoreConsumeRequest request) {
        TwoTuple<Integer, String> masterTuple = ConfigCenterUtil.getMasterByBloc(request.getBlocCode());
        MemberInfoDto memberInfo = memberMedService.getByMemberNo(masterTuple.getFirst(), masterTuple.getSecond(), request.getMemberNo());
        String memberNo = request.getMemberNo();
        JSONObject json = new JSONObject();
        json.put("name", memberNo);
        json.put("blocCode", request.getBlocCode());
        List<String> records = new ArrayList<>();
        records.add(String.format("【%s】-【%s】会员储值消费: 消费金额【%s】, 描述【%s】", memberInfo.getRealName(), memberInfo.getMemberNo(), request.getAmount(), request.getGoodsDes()));
        json.put("records", records);
        return json.toJSONString();
    }

    @LogRecordFunction("storeFreeze")
    public static String storeFreeze(MemberStoreFreezeRequest request) {
        TwoTuple<Integer, String> masterTuple = ConfigCenterUtil.getMasterByBloc(request.getBlocCode());
        MemberInfoDto memberInfo = memberMedService.getByMemberNo(masterTuple.getFirst(), masterTuple.getSecond(), request.getMemberNo());
        String memberNo = request.getMemberNo();
        JSONObject json = new JSONObject();
        json.put("name", memberNo);
        json.put("blocCode", request.getBlocCode());
        List<String> records = new ArrayList<>();
        String logStr = String.format("【%s】-【%s】会员储值冻结: 本金金额【%s】, 礼金金额【%s】, 解冻时间【%s】, 冻结原因:【%s】",
                memberInfo.getRealName(), memberInfo.getMemberNo(), request.getCapitalAmount(), request.getGiftAmount(), request.getIsFreezeLong().equals(Constant.ONE) ? "长期有效" : request.getFreezeDate(), request.getConsumeDesc());
        records.add(logStr);
        json.put("records", records);
        return json.toJSONString();
    }


    @LogRecordFunction("storeUnFreeze")
    public static String storeUnFreeze(MemberStoreUnFreezeRequest request) {
        TwoTuple<Integer, String> masterTuple = ConfigCenterUtil.getMasterByBloc(request.getBlocCode());
        MemberInfoDto memberInfo = memberMedService.getByMemberNo(masterTuple.getFirst(), masterTuple.getSecond(), request.getMemberNo());
        String memberNo = request.getMemberNo();
        JSONObject json = new JSONObject();
        json.put("name", memberNo);
        json.put("blocCode", request.getBlocCode());
        List<String> records = new ArrayList<>();
        records.add(String.format("【%s】-【%s】会员储值取消解冻: 冻结记录【%s】, 解冻原因【%s】", memberInfo.getRealName(), memberInfo.getMemberNo(), request.getConsumeRecordNo(), request.getUnfreezeReason()));
        json.put("records", records);
        return json.toJSONString();
    }

    @LogRecordFunction("addMemberProfileTag")
    public static String addMemberProfileTag(SaveMemberProfileTagRequest request) {
        TwoTuple<Integer, String> masterTuple = ConfigCenterUtil.getMasterByBloc(request.getBlocCode());
        MemberInfoDto memberInfo = memberMedService.getByMemberNo(masterTuple.getFirst(), request.getBlocCode(), request.getMemberNo());
        String memberNo = request.getMemberNo();
        JSONObject json = new JSONObject();
        json.put("name", memberNo);
        json.put("blocCode", request.getBlocCode());
        List<String> records = new ArrayList<>();
        StringBuilder stringBuilder = new StringBuilder();
        String tagName = request.getMemberProfileTagList().stream().map(MemberProfileTagRequest::getTagName)
                .collect(Collectors.joining("、"));
        stringBuilder.append(String.format("【%s】-【%s】新增标签:%s", memberInfo.getRealName(), memberInfo.getMemberNo(), tagName));
        records.add(stringBuilder.toString());
        json.put("records", records);
        return json.toJSONString();
    }

    @LogRecordFunction("deleteMemberProfileTag")
    public static String deleteMemberProfileTag(DeleteMemberProfileTagRequest request) {
        TwoTuple<Integer, String> masterTuple = ConfigCenterUtil.getMasterByBloc(request.getBlocCode());
        MemberInfoDto memberInfo = memberMedService.getByMemberNo(masterTuple.getFirst(), request.getBlocCode(), request.getMemberNo());
        String memberNo = request.getMemberNo();
        JSONObject json = new JSONObject();
        json.put("name", memberNo);
        json.put("blocCode", request.getBlocCode());
        List<String> records = new ArrayList<>();
        records.add(String.format("【%s】-【%s】删除标签:%s", memberInfo.getRealName(), memberInfo.getMemberNo(), request.getTagName()));
        json.put("records", records);
        return json.toJSONString();
    }

}
