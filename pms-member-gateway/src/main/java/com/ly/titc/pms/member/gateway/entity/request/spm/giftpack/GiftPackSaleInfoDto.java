package com.ly.titc.pms.member.gateway.entity.request.spm.giftpack;

import com.ly.titc.pms.spm.dubbo.entity.dto.rule.use.applicable.ApplicableHotelsDto;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * @Description:
 * @Author: huaizhu.zhong
 * @Date: 2024-11-22
 */
@Data
public class GiftPackSaleInfoDto implements Serializable {
    /**
     * 礼包售卖是否允许积分支付 0-不允许 1-允许
     */
    private Integer isAllowPointsPay;

    /**
     * 线上售卖渠道
     */
    private List<String> onlineSaleChannels;

    /**
     * 线上平台待支付金额
     */
    private BigDecimal onlineSaleAmount;

    /**
     * 线上平台待支付积分
     */
    private Integer onlineSalePoints;

    /**
     * 线下售卖金额
     */
    private BigDecimal offlineSaleAmount;

    /**
     * 线下是否可售 0-否 1-是
     */
    private Integer isOfflineSale;

    /**
     * 线下售卖酒店code集合(指定门店)
     */
    private ApplicableHotelsDto offlineApplicableHotel;
}
