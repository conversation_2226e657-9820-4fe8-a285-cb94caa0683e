package com.ly.titc.pms.member.gateway.converter;


import com.ly.titc.cdm.dubbo.entity.response.plan.HotelRatePlanListResp;
import com.ly.titc.chm.entity.response.BlocRatePlanInfoResp;
import com.ly.titc.common.constants.Constant;
import com.ly.titc.mdm.entity.response.hotel.PageHotelsResp;
import com.ly.titc.mdm.entity.response.hotel.room.type.RoomTypeBaseInfoResp;
import com.ly.titc.pms.member.gateway.entity.request.BaseRequest;
import com.ly.titc.pms.member.gateway.entity.request.spm.coupon.PageCouponApplicableHotelRequest;
import com.ly.titc.pms.member.gateway.entity.request.spm.coupon.PageCouponRatePlanRequest;
import com.ly.titc.pms.member.gateway.entity.request.spm.coupon.PageCouponRoomTypeRequest;
import com.ly.titc.pms.member.gateway.entity.request.spm.coupon.template.*;
import com.ly.titc.pms.member.gateway.entity.request.spm.coupon.template.dto.EffectDateConfigDto;
import com.ly.titc.pms.member.gateway.entity.request.spm.coupon.template.dto.GrantRuleDto;
import com.ly.titc.pms.member.gateway.entity.request.spm.coupon.template.dto.TemplateUseRuleDto;
import com.ly.titc.pms.member.gateway.entity.request.spm.coupon.template.dto.TimeBucketDto;
import com.ly.titc.pms.member.gateway.entity.response.spm.coupon.CouponApplicableHotelResponse;
import com.ly.titc.pms.member.gateway.entity.response.spm.coupon.CouponApplicableRatePlanResponse;
import com.ly.titc.pms.member.gateway.entity.response.spm.coupon.CouponApplicableRoomTypeResponse;
import com.ly.titc.pms.member.gateway.entity.response.spm.coupon.template.CouponTemplateInfoResponse;
import com.ly.titc.pms.member.gateway.entity.response.spm.coupon.template.CouponTemplateListResponse;
import com.ly.titc.pms.member.gateway.entity.response.spm.coupon.template.CouponTemplateTreeResponse;
import com.ly.titc.pms.member.gateway.entity.response.spm.coupon.template.SaveCouponTemplateInfoResponse;
import com.ly.titc.pms.member.gateway.entity.response.spm.coupon.template.dto.*;
import com.ly.titc.pms.member.mediator.entity.dto.spm.acitvity.ApplicableHotelsDto;
import com.ly.titc.pms.member.mediator.entity.dto.user.UserInfoDto;
import com.ly.titc.pms.spm.dubbo.entity.dto.CouponEffectDateConfigDto;
import com.ly.titc.pms.spm.dubbo.entity.dto.rule.accounting.CouponAccountingRuleDto;
import com.ly.titc.pms.spm.dubbo.entity.dto.rule.cancel.CouponCancelRuleDto;
import com.ly.titc.pms.spm.dubbo.entity.dto.rule.grant.CouponGrantRuleDto;
import com.ly.titc.pms.spm.dubbo.entity.dto.rule.use.CouponTemplateUseRuleDto;
import com.ly.titc.pms.spm.dubbo.entity.dto.rule.use.applicable.*;
import com.ly.titc.pms.spm.dubbo.entity.request.coupon.GetCouponInfoReq;
import com.ly.titc.pms.spm.dubbo.entity.request.coupon.template.*;
import com.ly.titc.pms.spm.dubbo.entity.response.coupon.template.CouponTemplateDetailResp;
import com.ly.titc.pms.spm.dubbo.entity.response.coupon.template.CouponTemplateListResp;
import com.ly.titc.pms.spm.dubbo.entity.response.coupon.template.SaveCouponTemplateInfoResp;
import com.ly.titc.pms.spm.dubbo.enums.ApplicableScopeTypeEnum;
import com.ly.titc.pms.spm.dubbo.enums.CouponTypeEnum;
import org.apache.logging.log4j.util.Strings;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.Named;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description:
 * @Author: huaizhu.zhong
 * @Date: 2024-11-12
 */
@Mapper(componentModel = "spring")
public interface CouponTemplateConverter extends ApplicableRuleConverter {

    @Named("convertDateFormat")
    default String convertDateFormat(String dateStr) {
        if (StringUtils.isEmpty(dateStr)) {
            return Strings.EMPTY;
        }
        return dateStr.substring(0, 10);
    }

    @Mapping(target = "sourceClient", ignore = true)
    @Mapping(target = "hotelCode", ignore = true)
    @Mapping(target = "currPage", source = "pageIndex")
    GetCouponTemplateListReq convertGetCouponTemplateListReq(PageTemplateListRequest request);

    @Mapping(target = "state", constant = "1")
    GetCouponTemplateListReq convertGetCouponTemplateListReqByBase(BaseRequest request);

    @Mapping(target = "startDate", source = "startDate", qualifiedByName = "convertDateFormat")
    @Mapping(target = "endDate", source = "endDate", qualifiedByName = "convertDateFormat")
    @Mapping(target = "unifiedEndDate", source = "unifiedEndDate", qualifiedByName = "convertDateFormat")
    EffectDateConfigDto convertEffectDateConfigDto(CouponEffectDateConfigDto dto);

    CouponTemplateListResponse convertCouponTemplateList(CouponTemplateListResp resp);

    List<CouponTemplateListResponse> convertCouponTemplateList(List<CouponTemplateListResp> respList);

    GetCouponTemplateDetailReq convertGetCouponTemplateDetailReq(GetTemplateDetailRequest request);

    CouponApplicableHotelResponse convertCouponApplicableHotel(PageHotelsResp pageHotelsResp);

    List<CouponApplicableHotelResponse> convertCouponApplicableHotels(List<PageHotelsResp> pageHotelsRespList);

    @Mappings({
            @Mapping(target = "isEffectOneNight", source = "accountingRuleInfo.isEffectOneNight"),
            @Mapping(target = "postingItemCode", source = "accountingRuleInfo.postingItemCode"),
            @Mapping(target = "postingType", source = "accountingRuleInfo.postingType"),
            @Mapping(target = "orderCancelReturn", source = "cancelRuleInfo.orderCancelReturn"),
            @Mapping(target = "nightAuditBeforeCancel", source = "cancelRuleInfo.nightAuditBeforeCancel"),
            @Mapping(target = "couponTypeName", expression = "java(com.ly.titc.pms.spm.dubbo.enums.CouponTypeEnum.getEnum(resp.getCouponType()).getDesc())"),
            @Mapping(target = "useRuleInfo", ignore = true),
            @Mapping(target = "grantRuleInfo", ignore = true),
            @Mapping(target = "postingItemName", ignore = true),
    })
    CouponTemplateInfoResponse convertCouponTemplateInfoResponse(CouponTemplateDetailResp resp);

    @Mapping(target = "cancelRuleInfo", ignore = true)
    @Mapping(target = "accountingRuleInfo", ignore = true)
    @Mapping(target = "useRuleInfo", ignore = true)
    @Mapping(target = "grantRuleInfo", ignore = true)
    @Mapping(target = "masterType", constant = "1")
    @Mapping(target = "masterCode", source = "request.blocCode")
    @Mapping(target = "sourceClient", constant = "ECRM")
    SaveCouponTemplateReq convertSaveCouponTemplateReq(SaveTemplateRequest request, Long operatorId);


    default SaveCouponTemplateReq buildSaveCouponTemplateReq(SaveTemplateRequest request, UserInfoDto user) {
        SaveCouponTemplateReq req = this.convertSaveCouponTemplateReq(request, user.getUserId());
        req.setAccountingRuleInfo(this.buildCouponAccountingRuleDto(request));
        req.setUseRuleInfo(this.buildCouponTemplateUseRuleDto(request));
        req.setCancelRuleInfo(this.buildCouponCancelRuleDto(request));
        req.setGrantRuleInfo(this.convertCouponGrantRuleDto(request.getGrantRuleInfo()));
        return req;
    }


    default CouponAccountingRuleDto buildCouponAccountingRuleDto(SaveTemplateRequest request) {
        CouponAccountingRuleDto dto = new CouponAccountingRuleDto();
        //默认不入账
        dto.setPostingType(Objects.isNull(request.getPostingType()) ? Constant.ZERO : request.getPostingType());
        //默认入账项目为空
        dto.setPostingItemCode(Objects.isNull(request.getPostingItemCode()) ? Strings.EMPTY : request.getPostingItemCode());
        //默认仅生效一个房晚
        dto.setIsEffectOneNight(Objects.isNull(request.getIsEffectOneNight()) ? Constant.ONE : request.getIsEffectOneNight());
        return dto;
    }

    default CouponCancelRuleDto buildCouponCancelRuleDto(SaveTemplateRequest request) {
        CouponCancelRuleDto dto = new CouponCancelRuleDto();
        //订单取消后返回 默认False
        dto.setOrderCancelReturn(Objects.isNull(request.getOrderCancelReturn()) ? Boolean.FALSE : request.getOrderCancelReturn());
        //夜审或加收房费前是否允许取消使用券 默认false
        dto.setNightAuditBeforeCancel(Objects.isNull(request.getNightAuditBeforeCancel()) ? Boolean.FALSE : request.getNightAuditBeforeCancel());
        return dto;
    }

    @Mapping(target = "customizeHotelInfos", source = "applicableHotelsDto")
    CouponGrantRuleDto convertCouponGrantRuleDto(GrantRuleDto dto);

    @Mapping(target = "unTimeBuckets", ignore = true)
    @Mapping(target = "unWeeks", source = "unApplicableWeekInfos")
    @Mapping(target = "channelInfos", source = "applicablePlatformChannels")
    @Mapping(target = "usersInfo", source = "applicableUsersDto")
    @Mapping(target = "hotelInfo", source = "applicableHotelsDto")
    @Mapping(target = "blocRatePlanInfos", source = "blocApplicableRatePlanInfos")
    @Mapping(target = "blocRoomTypeInfos", source = "blocApplicableRoomTypeInfos")
    @Mapping(target = "hotelApplicableRoomTypeInfo", source = "hotelApplicableRoomTypeInfos")
    CouponTemplateUseRuleDto convertCouponTemplateUseRuleDto(TemplateUseRuleDto templateUseRuleDto);

    default CouponTemplateUseRuleDto buildCouponTemplateUseRuleDto(SaveTemplateRequest request) {
        CouponTemplateUseRuleDto couponTemplateUseRuleDto = convertCouponTemplateUseRuleDto(request.getUseRuleInfo());
        //时间段
        couponTemplateUseRuleDto.setUnTimeBuckets(this.buildApplicableTimeBucket(request.getUseRuleInfo()));
        return couponTemplateUseRuleDto;
    }

    default List<String> buildApplicableTimeBucket(TemplateUseRuleDto request) {
        if (!CollectionUtils.isEmpty(request.getUnApplicableTimeBucketInfos())) {
            return request.getUnApplicableTimeBucketInfos().stream().map(item ->
                    item.getStartTime() + Constant.STRING_COMMA + item.getEndTime()).collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    @Mapping(target = "masterType", constant = "1")
    @Mapping(target = "masterCode", source = "blocCode")
    DeleteCouponTemplateReq convertDeleteCouponTemplateReq(DeleteCouponTemplateRequest request);

    @Mapping(target = "masterType", constant = "1")
    @Mapping(target = "masterCode", source = "blocCode")
    UpdateCouponTemplateStateReq convertRepealCouponTemplateReq(RepealTemplateRequest request);

    SaveCouponTemplateInfoResponse convertSaveCouponTemplateInfoResponse(SaveCouponTemplateInfoResp resp);

    GetCouponTemplateDetailReq convertGetCouponTemplateDetailReqByRoomType(PageCouponRoomTypeRequest request);

    GetCouponTemplateDetailReq convertGetCouponTemplateDetailReqByRatePlan(PageCouponRatePlanRequest request);

    GetApplicableHotelReq convertGetApplicableHotelReqByPage(PageCouponApplicableHotelRequest request);

    @Mapping(target = "hotelCode", ignore = true)
    GetCouponInfoReq convertGetCouponInfoReq(PageCouponApplicableHotelRequest request);

    @Mapping(target = "hotelRuleInfo", source = "applicableHotelsDto")
    GetApplicableHotelReq convertGetApplicableHotelReq(GetApplicableHotelRequest request);


    default List<CouponApplicableRoomTypeResponse> convertCouponApplicableRoomTypes(CouponTemplateUseRuleDto useRuleInfo,
                                                                                    List<RoomTypeBaseInfoResp> roomTypeInfos,
                                                                                    List<PageHotelsResp> pageHotels) {
        ApplicableHotelRoomTypeInfoDto applicableRoomTypeInfo = useRuleInfo.getHotelApplicableRoomTypeInfo();
        if (Objects.equals(applicableRoomTypeInfo.getApplicableScopeType(), ApplicableScopeTypeEnum.ALL.getScopeType())) {
            return this.convertCouponApplicableRoomTypes(roomTypeInfos);
        }
        //房型map
        Map<String, RoomTypeBaseInfoResp> roomTypeBaseInfoMap = roomTypeInfos.stream().collect(
                Collectors.toMap(RoomTypeBaseInfoResp::getRoomTypeCode, Function.identity(), (o1, o2) -> o2));
        //酒店map
        Map<String, String> hotelCodeMap = pageHotels.stream().collect(Collectors.toMap(PageHotelsResp::getHotelCode,
                PageHotelsResp::getHotelName, (o1, o2) -> o2));

        List<CouponApplicableRoomTypeResponse> ans = new ArrayList<>();
        for (ApplicableHotelRoomTypeDataInfoDto item : applicableRoomTypeInfo.getHotelRoomTypeInfos()) {
            String hotelName = hotelCodeMap.getOrDefault(item.getHotelCode(), "");
            if (CollectionUtils.isEmpty(item.getRoomTypeInfos())) {
                ans.add(convertCouponApplicableRoomType(item.getHotelCode(), hotelName, Strings.EMPTY, Strings.EMPTY));
            } else {
                ans.addAll(
                        item.getRoomTypeInfos().stream().map(roomTypeInfo -> {
                            RoomTypeBaseInfoResp roomTypeBaseInfoResp = roomTypeBaseInfoMap.get(roomTypeInfo.getCode());
                            String roomTypeName = Objects.isNull(roomTypeBaseInfoResp) ? Strings.EMPTY
                                    : StringUtils.isEmpty(roomTypeBaseInfoResp.getRoomTypeName()) ? roomTypeInfo.getCode() : roomTypeBaseInfoResp.getRoomTypeName();
                            return convertCouponApplicableRoomType(item.getHotelCode(), hotelName, roomTypeInfo.getCode(), roomTypeName);
                        }).collect(Collectors.toList())
                );
            }
        }
        return ans;
    }

    default List<CouponApplicableRatePlanResponse> convertCouponApplicableRatePlans(CouponTemplateUseRuleDto couponUseRuleDto,
                                                                                    List<HotelRatePlanListResp> hotelRatePlans,
                                                                                    List<PageHotelsResp> pageHotels) {
        //价格方案map
        Map<String, HotelRatePlanListResp> hotelRatePlaMap = hotelRatePlans.stream()
                .collect(Collectors.toMap(HotelRatePlanListResp::getRatePlanCode, Function.identity(), (o1, o2) -> o2));
        //酒店map
        Map<String, String> hotelCodeMap = pageHotels.stream().collect(Collectors.toMap(PageHotelsResp::getHotelCode,
                PageHotelsResp::getHotelName, (o1, o2) -> o2));
        ApplicableHotelRatePlanInfoDto ratePlanRuleInfo = couponUseRuleDto.getHotelApplicableRatePlanInfos();
        if (Objects.equals(ratePlanRuleInfo.getApplicableScopeType(), ApplicableScopeTypeEnum.ALL.getScopeType())) {
            return this.convertCouponApplicableRatePlans(hotelRatePlans, hotelCodeMap);
        }
        List<CouponApplicableRatePlanResponse> ans = new ArrayList<>();
        for (ApplicableHotelRatePlanDataInfoDto item : ratePlanRuleInfo.getHotelRatePlanInfos()) {
            String hotelName = hotelCodeMap.getOrDefault(item.getHotelCode(), "");
            if (CollectionUtils.isEmpty(item.getRatePlanInfos())) {
                ans.add(this.convertCouponApplicableRatePlan(item.getHotelCode(), hotelName, Strings.EMPTY, Strings.EMPTY));
            } else {
                ans.addAll(
                        item.getRatePlanInfos().stream().map(ratePlanInfo -> {
                            HotelRatePlanListResp hotelRatePlanListResp = hotelRatePlaMap.get(ratePlanInfo.getCode());
                            String ratePlanName = Objects.isNull(hotelRatePlanListResp) ? Strings.EMPTY
                                    : StringUtils.isEmpty(hotelRatePlanListResp.getRatePlanName())
                                        ? ratePlanInfo.getCode() : hotelRatePlanListResp.getRatePlanName();
                            return convertCouponApplicableRatePlan(item.getHotelCode(), hotelName, ratePlanInfo.getCode(), ratePlanName);
                        }).collect(Collectors.toList())
                );
            }
        }
        return ans;
    }


    default List<CouponTemplateTreeResponse> convertCouponTemplateTree(List<CouponTemplateListResp> infos) {
        if (CollectionUtils.isEmpty(infos)) {
            return Collections.emptyList();
        }
        Map<Integer, List<CouponTemplateListResp>> couponTypeMap = infos.stream().collect(Collectors.groupingBy(CouponTemplateListResp::getCouponType));
        List<CouponTemplateTreeResponse> returnList = new ArrayList<>();
        couponTypeMap.forEach((key, value) -> {
            CouponTemplateTreeResponse response = new CouponTemplateTreeResponse();
            response.setCouponType(key);
            response.setCouponTypeName(CouponTypeEnum.getEnum(key).getDesc());
            response.setTemplateInfos(this.convertCouponTemplateList(value));
            returnList.add(response);
        });
        return returnList;
    }

    default List<TimeBucketDto> buildTemplateTimeBucketInfoDto(List<String> timeBucketInfos) {
        if (CollectionUtils.isEmpty(timeBucketInfos)) {
            return Collections.emptyList();
        }
        return timeBucketInfos.stream().map(timeBucket -> {
            String[] split = timeBucket.split(Constant.STRING_COMMA);
            TimeBucketDto response = new TimeBucketDto();
            response.setStartTime(split[0]);
            response.setEndTime(split[1]);
            return response;
        }).collect(Collectors.toList());
    }

    default List<CommonNameInfoDto> buildCommonNameInfos(List<String> codes, Map<String, String> nameInfoMap) {
        return codes.stream().map(code -> {
            CommonNameInfoDto commonNameInfoDto = new CommonNameInfoDto();
            commonNameInfoDto.setCode(code);
            commonNameInfoDto.setName(nameInfoMap.getOrDefault(code,code));
            return commonNameInfoDto;
        }).collect(Collectors.toList());
    }

    @Mapping(target = "customHotelInfos", source = "customizeHotelInfos")
    TemplateGrantRuleInfoDto convertCouponTemplateGrantRuleDto(CouponGrantRuleDto couponGrantRuleDto);

    TemplateBlocRatePlanInfoDto convertTemplateBlocRatePlanInfo(ApplicableBlocRatePlanInfoDto blocRatePlanInfoDto);

    List<TemplateBlocRatePlanInfoDto> convertTemplateBlocRatePlanInfos(List<ApplicableBlocRatePlanInfoDto> blocRatePlanInfos);

    @Mapping(target = "name", source = "ratePlanName")
    @Mapping(target = "code", source = "ratePlanCode")
    CommonNameInfoDto convertBlocRatePlanInfo(BlocRatePlanInfoResp ratePlanInfo);

    List<CommonNameInfoDto> convertBlocRatePlanInfos(List<BlocRatePlanInfoResp> blocRatePlanInfos);

    CouponApplicableRatePlanResponse convertCouponApplicableRatePlan(String hotelCode, String hotelName,
                                                                     String ratePlanCode, String ratePlanName);

    CouponApplicableRoomTypeResponse convertCouponApplicableRoomType(String hotelCode, String hotelName,
                                                                     String roomTypeCode, String roomTypeName);


    CouponApplicableRoomTypeResponse convertCouponApplicableRoomType(RoomTypeBaseInfoResp roomTypeBaseInfoResp);

    List<CouponApplicableRoomTypeResponse> convertCouponApplicableRoomTypes(List<RoomTypeBaseInfoResp> roomTypeInfos);

    default List<CouponApplicableRatePlanResponse> convertCouponApplicableRatePlans(List<HotelRatePlanListResp> hotelRatePlans,
                                                                                    Map<String, String> hotelCodeMap) {
        return hotelRatePlans.stream().map(item -> {
            String hotelName = hotelCodeMap.getOrDefault(item.getHotelCode(), "");
            return this.convertCouponApplicableRatePlan(item.getHotelCode(), hotelName, item.getRatePlanCode(), item.getRatePlanName());
        }).collect(Collectors.toList());
    }


}
