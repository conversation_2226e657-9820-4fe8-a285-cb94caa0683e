package com.ly.titc.pms.member.gateway.controller.member;

import com.ly.titc.cc.sdk.log.annotation.LogRecord;
import com.ly.titc.common.entity.Pageable;
import com.ly.titc.common.entity.Response;
import com.ly.titc.common.enums.StatusEnum;
import com.ly.titc.common.util.PageableUtil;
import com.ly.titc.pms.ecrm.dubbo.entity.request.member.PageRelegationRuleReq;
import com.ly.titc.pms.ecrm.dubbo.entity.request.member.PageUpgradeRuleReq;
import com.ly.titc.pms.ecrm.dubbo.entity.response.member.MemberCardLevelConfigInfoResp;
import com.ly.titc.pms.ecrm.dubbo.entity.response.member.MemberCardLevelRelegationRuleResp;
import com.ly.titc.pms.ecrm.dubbo.entity.response.member.MemberCardLevelUpgradeRuleResp;
import com.ly.titc.pms.member.com.annotation.OperationLog;
import com.ly.titc.pms.member.com.constant.ActionConstants;
import com.ly.titc.pms.member.com.constant.CommonConstant;
import com.ly.titc.pms.member.com.constant.ModuleConstants;
import com.ly.titc.pms.member.com.lang.TwoTuple;
import com.ly.titc.pms.member.com.utils.ConfigCenterUtil;
import com.ly.titc.pms.member.gateway.converter.MemberCardConverter;
import com.ly.titc.pms.member.gateway.entity.request.member.GetDetailBaseRequest;
import com.ly.titc.pms.member.gateway.entity.request.member.card.*;
import com.ly.titc.pms.member.gateway.entity.response.member.MemberCardLevelRelegationRuleResponse;
import com.ly.titc.pms.member.gateway.entity.response.member.MemberCardLevelUpgradeRuleResponse;
import com.ly.titc.pms.member.mediator.rpc.dubbo.ecrm.MemberCardInfoDecorator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 会员升降级规则
 *
 * @Author：rui
 * @name：MemberRuleController
 * @Date：2024-11-14 11:24
 * @Filename：MemberRuleController
 */
@Slf4j
@RestController
@RequestMapping("/member/card/rule")
public class MemberRuleController {

    @Resource
    private MemberCardConverter memberCardConverter;

    @Resource
    private MemberCardInfoDecorator memberCardInfoDecorator;

    /**
     * 分页查询升级规则
     *
     * @param request request
     * @return 分页结果
     */
    @PostMapping("/pageUpgradeRule")
    public Response<Pageable<MemberCardLevelUpgradeRuleResponse>> pageUpgradeRule(@RequestBody @Valid PageRuleRequest request) {
        TwoTuple<Integer, String> masterTuple = ConfigCenterUtil.getMasterByBloc(request.getBlocCode());
        PageUpgradeRuleReq req = memberCardConverter.convertPageUpgradeRuleReq(request, masterTuple.getFirst());
        Pageable<MemberCardLevelUpgradeRuleResp> pageable = memberCardInfoDecorator.pageUpgradeRule(req);
        List<MemberCardLevelUpgradeRuleResponse> records = memberCardConverter.convertMemberCardLevelUpgradeRuleResponseList(pageable.getDatas());
        return Response.success(PageableUtil.convert(pageable, records));
    }

    /**
     * 新增升级规则
     *
     * @param request request
     * @return id
     */
    @PostMapping("/addUpgradeRule")
    @LogRecord(tenant = CommonConstant.PROJECT_CODE, bizCode = ModuleConstants.MEMBER_RULE_MANAGE_CODE, bizName = ModuleConstants.MEMBER_RULE_MANAGE_NAME,
            category = ActionConstants.ADD_CODE, categoryName = ActionConstants.ADD_NAME)
    @OperationLog(operateRecord = "#memberCardRule_addUpgradeRule(#request)")
    public Response<Long> saveUpgradeRule(@RequestBody @Valid SaveCardLevelUpgradeRuleRequest request) {
        TwoTuple<Integer, String> masterTuple = ConfigCenterUtil.getMasterByBloc(request.getBlocCode());
        Long id = memberCardInfoDecorator.saveCardLevelUpgradeRule(memberCardConverter.convertSaveCardLevelUpgradeRuleReq(request, masterTuple.getFirst()));
        return Response.success(id);
    }

    /**
     * 更新升级规则
     *
     * @param request request
     * @return id
     */
    @PostMapping("/updateUpgradeRule")
    @LogRecord(tenant = CommonConstant.PROJECT_CODE, bizCode = ModuleConstants.MEMBER_RULE_MANAGE_CODE, bizName = ModuleConstants.MEMBER_RULE_MANAGE_NAME,
            category = ActionConstants.SAVE_CODE, categoryName = ActionConstants.SAVE_NAME)
    @OperationLog(operateRecord = "#memberCardRule_updateUpgradeRule(#request)")
    public Response<Long> updateUpgradeRule(@RequestBody @Valid SaveCardLevelUpgradeRuleRequest request) {
        TwoTuple<Integer, String> masterTuple = ConfigCenterUtil.getMasterByBloc(request.getBlocCode());
        Long id = memberCardInfoDecorator.saveCardLevelUpgradeRule(memberCardConverter.convertSaveCardLevelUpgradeRuleReq(request, masterTuple.getFirst()));
        return Response.success(id);
    }

    /**
     * 启用升级规则
     *
     * @param request
     * @return
     */

    @PostMapping("/enableUpgradeRule")
    @LogRecord(tenant = CommonConstant.PROJECT_CODE, bizCode = ModuleConstants.MEMBER_RULE_MANAGE_CODE, bizName = ModuleConstants.MEMBER_RULE_MANAGE_NAME,
            category = ActionConstants.START_CODE, categoryName = ActionConstants.START_NAME)
    @OperationLog(operateRecord = "#memberCardRule_enableUpgradeRule(#request)")
    public Response enableUpgradeRule(@RequestBody @Valid ActionRuleBaseRequest request) {
        memberCardInfoDecorator.actionCardLevelUpgradeRule(request.getId(), StatusEnum.VALID.getStatus(), request.getOperator());
        return Response.success();
    }

    /**
     * 停用升级规则
     *
     * @param request
     * @return
     */
    @LogRecord(tenant = CommonConstant.PROJECT_CODE, bizCode = ModuleConstants.MEMBER_RULE_MANAGE_CODE, bizName = ModuleConstants.MEMBER_RULE_MANAGE_NAME,
            category = ActionConstants.STOP_CODE, categoryName = ActionConstants.STOP_NAME)
    @OperationLog(operateRecord = "#memberCardRule_disableUpgradeRule(#request)")
    @PostMapping("/disableUpgradeRule")
    public Response disableUpgradeRule(@RequestBody @Valid ActionRuleBaseRequest request) {
        memberCardInfoDecorator.actionCardLevelUpgradeRule(request.getId(), StatusEnum.INVALID.getStatus(), request.getOperator());
        return Response.success();
    }

    /**
     * 删除升级规则
     *
     * @param request request
     */
    @PostMapping("/deleteUpgradeRule")
    @LogRecord(tenant = CommonConstant.PROJECT_CODE, bizCode = ModuleConstants.MEMBER_RULE_MANAGE_CODE, bizName = ModuleConstants.MEMBER_RULE_MANAGE_NAME,
            category = ActionConstants.DELETE_CODE, categoryName = ActionConstants.DELETE_NAME)
    @OperationLog(operateRecord = "#memberCardRule_deleteUpgradeRule(#request)")
    public Response deleteUpgradeRule(@RequestBody @Valid DeleteRuleRequest request) {
        memberCardInfoDecorator.deleteCardLevelUpgradeRule(request.getId(), request.getOperator());
        return Response.success();
    }

    /**
     * 升级规则详情
     *
     * @param request request
     */
    @PostMapping("/getUpgradeRule")
    public Response<MemberCardLevelUpgradeRuleResponse> getUpgradeRule(@RequestBody @Valid GetDetailBaseRequest request) {
        MemberCardLevelUpgradeRuleResp resp = memberCardInfoDecorator.getUpgradeRule(request.getId());
        return Response.success(memberCardConverter.convertMemberCardLevelUpgradeRuleResponse(resp));
    }

    /**
     * 分页查询保级规则
     *
     * @param request request
     * @return 分页结果
     */
    @PostMapping("/pageRelegationRule")
    public Response<Pageable<MemberCardLevelRelegationRuleResponse>> pageRelegationRule(@RequestBody @Valid PageRuleRequest request) {
        TwoTuple<Integer, String> masterTuple = ConfigCenterUtil.getMasterByBloc(request.getBlocCode());
        PageRelegationRuleReq req = memberCardConverter.convertPageRelegationRuleReq(request, masterTuple.getFirst());
        Pageable<MemberCardLevelRelegationRuleResp> pageable = memberCardInfoDecorator.pageRelegationRule(req);
        List<MemberCardLevelRelegationRuleResp> list = pageable.getDatas();
        if (CollectionUtils.isEmpty(list)) {
            return Response.success(pageable);
        }
        List<Long> cardIdList = list.stream().map(MemberCardLevelRelegationRuleResp::getCardId).collect(Collectors.toList());
        List<MemberCardLevelConfigInfoResp> cardLevelConfigInfoRespList = memberCardInfoDecorator.listMemberCardLevel(cardIdList);
        List<MemberCardLevelRelegationRuleResponse> records = memberCardConverter.convertMemberCardLevelRelegationRuleResponseList(list, cardLevelConfigInfoRespList);
        return Response.success(PageableUtil.convert(pageable, records));
    }

    /**
     * 新增保级规则
     */
    @PostMapping("/addRelegationRule")
    @LogRecord(tenant = CommonConstant.PROJECT_CODE, bizCode = ModuleConstants.MEMBER_RULE_MANAGE_CODE, bizName = ModuleConstants.MEMBER_RULE_MANAGE_NAME,
            category = ActionConstants.ADD_CODE, categoryName = ActionConstants.ADD_NAME)
    @OperationLog(operateRecord = "#memberCardRule_addRelegationRule(#request)")
    public Response<Long> addRelegationRule(@RequestBody @Valid SaveCardLevelRelegationRuleRequest request) {
        TwoTuple<Integer, String> masterTuple = ConfigCenterUtil.getMasterByBloc(request.getBlocCode());
        Long id = memberCardInfoDecorator.saveCardLevelRelegationRule(memberCardConverter.convertSaveCardLevelRelegationRuleReq(request, masterTuple.getFirst()));
        return Response.success(id);
    }

    /**
     * 更新保级规则
     *
     * @param request
     * @return
     */
    @PostMapping("/updateRelegationRule")
    @LogRecord(tenant = CommonConstant.PROJECT_CODE, bizCode = ModuleConstants.MEMBER_RULE_MANAGE_CODE, bizName = ModuleConstants.MEMBER_RULE_MANAGE_NAME,
            category = ActionConstants.SAVE_CODE, categoryName = ActionConstants.SAVE_NAME)
    @OperationLog(operateRecord = "#memberCardRule_updateRelegationRule(#request)")
    public Response<Long> updateRelegationRule(@RequestBody @Valid SaveCardLevelRelegationRuleRequest request) {
        TwoTuple<Integer, String> masterTuple = ConfigCenterUtil.getMasterByBloc(request.getBlocCode());
        Long id = memberCardInfoDecorator.saveCardLevelRelegationRule(memberCardConverter.convertSaveCardLevelRelegationRuleReq(request, masterTuple.getFirst()));
        return Response.success(id);
    }

    /**
     * 启用保级规则
     *
     * @param request request
     * @return
     */
    @PostMapping("/enableRelegationRule")
    @LogRecord(tenant = CommonConstant.PROJECT_CODE, bizCode = ModuleConstants.MEMBER_RULE_MANAGE_CODE, bizName = ModuleConstants.MEMBER_RULE_MANAGE_NAME,
            category = ActionConstants.START_CODE, categoryName = ActionConstants.START_NAME)
    @OperationLog(operateRecord = "#memberCardRule_enableRelegationRule(#request)")
    public Response enableRelegationRule(@RequestBody @Valid ActionRuleBaseRequest request) {
        memberCardInfoDecorator.actionCardLevelRelegationRule(request.getId(), StatusEnum.VALID.getStatus(), request.getOperator());
        return Response.success();
    }

    /**
     * 停用保级规则
     *
     * @param request request
     * @return
     */
    @PostMapping("/disableRelegationRule")
    @LogRecord(tenant = CommonConstant.PROJECT_CODE, bizCode = ModuleConstants.MEMBER_RULE_MANAGE_CODE, bizName = ModuleConstants.MEMBER_RULE_MANAGE_NAME,
            category = ActionConstants.STOP_CODE, categoryName = ActionConstants.STOP_NAME)
    @OperationLog(operateRecord = "#memberCardRule_disableRelegationRule(#request)")
    public Response disableRelegationRule(@RequestBody @Valid ActionRuleBaseRequest request) {
        memberCardInfoDecorator.actionCardLevelRelegationRule(request.getId(), StatusEnum.INVALID.getStatus(), request.getOperator());
        return Response.success();
    }

    /**
     * 删除保级规则
     *
     * @param request request
     * @return
     */
    @PostMapping("/deleteRelegationRule")
    @LogRecord(tenant = CommonConstant.PROJECT_CODE, bizCode = ModuleConstants.MEMBER_RULE_MANAGE_CODE, bizName = ModuleConstants.MEMBER_RULE_MANAGE_NAME,
            category = ActionConstants.DELETE_CODE, categoryName = ActionConstants.DELETE_NAME)
    @OperationLog(operateRecord = "#memberCardRule_deleteRelegationRule(#request)")
    public Response deleteRelegationRule(@RequestBody @Valid DeleteRuleRequest request) {
        memberCardInfoDecorator.deleteCardLevelRelegationRule(request.getId(), request.getOperator());
        return Response.success();
    }

    /**
     * 保级规则详情
     */
    @PostMapping("/getRelegationRule")
    public Response<MemberCardLevelRelegationRuleResponse> getRelegationRule(@RequestBody @Valid GetDetailBaseRequest request) {
        MemberCardLevelRelegationRuleResp resp = memberCardInfoDecorator.getRelegationRule(request.getId());
        return Response.success(memberCardConverter.convertMemberCardLevelRelegationRuleResp(resp));
    }
}
