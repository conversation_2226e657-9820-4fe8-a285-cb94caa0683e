package com.ly.titc.pms.member.gateway.controller.account;

import com.ly.titc.common.entity.Response;
import com.ly.titc.pms.account.dubbo.entity.response.SettlementInfoResp;
import com.ly.titc.pms.member.gateway.converter.AccountConverter;
import com.ly.titc.pms.member.gateway.entity.request.BaseRequest;
import com.ly.titc.pms.member.gateway.entity.response.account.PostingItemResponse;
import com.ly.titc.pms.member.mediator.rpc.dubbo.account.AccountSettlementDecorator;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 结算项
 * @Description:
 * @Author: huaizhu.zhong
 * @Date: 2025-2-8
 */
@RestController
@RequestMapping("/settlement")
public class SettlementController {
    @Resource(type = AccountSettlementDecorator.class)
    private AccountSettlementDecorator accountSettlementDecorator;
    @Resource(type = AccountConverter.class)
    private AccountConverter accountConverter;

    @PostMapping("/list")
    public Response<List<PostingItemResponse>> listSettlement(@RequestBody BaseRequest request){
        List<SettlementInfoResp> settlementInfos = accountSettlementDecorator.listSettlement(request.getBlocCode());
        return Response.success(accountConverter.convertSettlementItemResponses(settlementInfos));
    }
}
