package com.ly.titc.pms.member.gateway.entity.response.spm.activity;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2025-3-5 15:39
 */
@Data
@Accessors(chain = true)
public class EventGrantInfoResponse {

    /**
     * 活动名
     */
    private String activityName;

    /**
     * 活动类型名
     */
    private String displayTypeDesc;

    /**
     * 活动优惠类型名
     */
    private String discountTypeDesc;

    /**
     * 优惠业务产品名
     */
    private String discountBizName;

    /**
     * 奖励数量
     */
    private Integer discountCount;

    /**
     * 奖励人唯一标识
     */
    private String receiverCode;

    /**
     * 触发规则
     */
    private String triggerRule;

    /**
     * 创建时间
     */
    private String gmtCreate;
}
