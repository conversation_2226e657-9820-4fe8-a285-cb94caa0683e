package com.ly.titc.pms.member.gateway.entity.request.spm.coupon;

import com.ly.titc.pms.member.gateway.entity.request.PageBaseRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

/**
 * @Description:
 * @Author: huaizhu.zhong
 * @Date: 2024-11-28
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PageCouponApplicableHotelRequest extends PageBaseRequest {

    /**
     * 优惠券编码不能为空
     */
    private String couponCode;

    /**
     * 酒店名称或编码
     */
    private String lkNameOrCode;

    /**
     * 优惠券模版
     */
    private String templateCode;

    /**
     * 优惠券模版版本
     */
    private Integer version;
}
