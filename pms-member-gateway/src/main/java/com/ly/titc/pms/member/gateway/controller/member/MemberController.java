package com.ly.titc.pms.member.gateway.controller.member;

import com.ly.titc.cc.sdk.log.annotation.LogRecord;
import com.ly.titc.common.entity.Pageable;
import com.ly.titc.common.entity.Response;
import com.ly.titc.common.exceptions.ServiceException;
import com.ly.titc.common.util.PageableUtil;
import com.ly.titc.pms.member.com.annotation.OperationLog;
import com.ly.titc.pms.member.com.constant.ActionConstants;
import com.ly.titc.pms.member.com.constant.CommonConstant;
import com.ly.titc.pms.member.com.constant.ModuleConstants;
import com.ly.titc.pms.member.com.enums.RespCodeEnum;
import com.ly.titc.pms.member.com.enums.SmsSceneEnum;
import com.ly.titc.pms.member.com.lang.TwoTuple;
import com.ly.titc.pms.member.com.utils.ConfigCenterUtil;
import com.ly.titc.pms.member.gateway.converter.MemberConverter;
import com.ly.titc.pms.member.gateway.entity.request.BaseRequest;
import com.ly.titc.pms.member.gateway.entity.request.member.*;
import com.ly.titc.pms.member.gateway.entity.request.member.config.GetMemberRelatedConfigRequest;
import com.ly.titc.pms.member.gateway.entity.response.member.MemberRelatedConfigResponse;
import com.ly.titc.pms.member.gateway.entity.response.member.MemberResponse;
import com.ly.titc.pms.member.mediator.entity.dto.member.*;
import com.ly.titc.pms.member.mediator.rpc.dubbo.ecrm.MemberCardInfoDecorator;
import com.ly.titc.pms.member.mediator.rpc.dubbo.message.MessageDecorator;
import com.ly.titc.pms.member.mediator.service.MemberCardMedService;
import com.ly.titc.pms.member.mediator.service.MemberMedService;
import com.ly.titc.pms.member.mediator.service.MessageMedService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * 会员管理
 *
 * @Author：rui
 * @name：MemberController
 * @Date：2024-11-18 17:03
 * @Filename：MemberController
 */
@Slf4j
@RestController
@RequestMapping("/member")
public class MemberController {

    @Resource
    private MemberCardMedService memberCardMedService;

    @Resource
    private MemberMedService memberMedService;

    @Resource
    private MemberConverter memberConverter;

    @Resource
    private MemberCardInfoDecorator decorator;

    @Resource
    private MessageDecorator messageDecorator;

    @Resource
    private MessageMedService messageMedService;

    /**
     * 会员分页查询
     *
     * @param request
     * @return
     */
    @PostMapping("/page")
    public Response<Pageable<MemberResponse>> pageMember(@RequestBody @Valid PageMemberRequest request) {
        TwoTuple<Integer, String> masterTuple = ConfigCenterUtil.getMasterByBloc(request.getBlocCode());
        PageMemberParamDto pageMemberParamDto = memberConverter.convertPageMemberParamBO(request, masterTuple.getFirst(), masterTuple.getSecond());
        if (StringUtils.isNotEmpty(request.getTagId())) {
            pageMemberParamDto.setTagIds(Collections.singletonList(Long.valueOf(request.getTagId())));
        }
        Pageable<MemberDetailDto> pageable = memberMedService.pageMemberByFinalMode(pageMemberParamDto);
        List<MemberDetailDto> records = pageable.getDatas();
        if (CollectionUtils.isEmpty(records)) {
            return Response.success(Pageable.empty());
        }
         return Response.success(PageableUtil.convert(pageable, memberConverter.convertMemberResponseList(records)));
    }


    /**
     * 获取会员相关配置(注册，验证，列表展示等)
     * @param request
     */
    @PostMapping("/relatedConfig")
    public Response<MemberRelatedConfigResponse> relatedConfig(@RequestBody @Valid GetMemberRelatedConfigRequest request) {
        TwoTuple<Integer, String> masterTuple = ConfigCenterUtil.getMasterByBloc(request.getBlocCode());
        return Response.success(memberConverter.convertMemberRegisterCheckConfigResponse(decorator.getMemberRelatedConfig(masterTuple.getFirst(), masterTuple.getSecond(), request.getType())));
    }

    /**
     * 保存会员相关配置(注册，验证，列表展示等)
     * @param request
     */
    @PostMapping("/saveRelatedConfig")
    public Response saveRegisterCheckConfig(@RequestBody @Valid SaveMemberRegisterCheckConfigRequest request) {
        TwoTuple<Integer, String> masterTuple = ConfigCenterUtil.getMasterByBloc(request.getBlocCode());
        decorator.saveMemberRelatedConfig(masterTuple.getFirst(), masterTuple.getSecond(), request.getParams(), request.getOperator(), request.getId() == null ? null : request.getId(), request.getType());
        return Response.success();
    }

    /**
     * 校验卡号是否已经存在
     */
    @PostMapping("/checkCardNo")
    public Response<Boolean> checkCardNo(@RequestBody @Valid CheckCardNoRequest request) {
        TwoTuple<Integer, String> masterTuple = ConfigCenterUtil.getMasterByBloc(request.getBlocCode());
        return Response.success(memberCardMedService.checkCardNoExist(masterTuple.getFirst(), masterTuple.getSecond(), request.getMemberCardNo()));
    }

    /**
     * 查询短信余额
     */
    @PostMapping("/querySmsBalance")
    public Response<Integer> querySmsBalance(@RequestBody @Valid BaseRequest request) {
        return Response.success(messageDecorator.querySmsBalance(request.getBlocCode()));
    }

    /**
     * 发送验证码
     */
    @PostMapping("/sendRegisterSms")
    public Response sendRegisterSms(@RequestBody @Valid SendRegisterSmsRequest request) {
        TwoTuple<Integer, String> masterTuple = ConfigCenterUtil.getMasterByBloc(request.getBlocCode());
        messageMedService.sendSceneSms(masterTuple.getFirst(), masterTuple.getSecond(), request.getMobile(), SmsSceneEnum.REGISTER.getScene());
        return Response.success();
    }

    /**
     * 验证码验证--这里可能要考虑一下有没有必要单独走接口验证，目前在注册的接口会验证
     */
    @PostMapping("/verifyRegisterSms")
    public Response verifyRegisterSms(@RequestBody @Valid VerifyRegisterSmsRequest request) {
        messageMedService.verifyRegisterSms(request.getMobile(), request.getCode(), SmsSceneEnum.REGISTER.getScene());
        return Response.success();
    }

    /**
     * 会员详情
     */
    @PostMapping("/detail")
    public Response<MemberResponse> getDetail(@RequestBody @Valid MemberDetailRequest request) {
        TwoTuple<Integer, String> masterTuple = ConfigCenterUtil.getMasterByBloc(request.getBlocCode());
        MemberDetailDto memberDetailDto = memberMedService.getDetailByMemberNo(masterTuple.getFirst(), masterTuple.getSecond(), request.getMemberNo());
        if (memberDetailDto == null) {
            throw new ServiceException(RespCodeEnum.MEMBER_10011);
        }
        return Response.success(memberConverter.convertMemberResponse(memberDetailDto));
    }

    /**
     * 保存会员信息
     */
    @PostMapping("/update")
    @LogRecord(tenant = CommonConstant.PROJECT_CODE, bizCode = ModuleConstants.MEMBER_MANAGE_CODE, bizName = ModuleConstants.MEMBER_MANAGE_NAME,
            category = ActionConstants.MEMBER_INFO_UPDATE_CODE, categoryName = ActionConstants.MEMBER_INFO_UPDATE_NAME)
    @OperationLog(operateRecord = "#member_update(#request)")
    public Response<String> updateMember(@RequestBody @Valid SaveMemberRequest request) {
        UpdateMemberInfoDto dto = memberConverter.convertUpdateMemberInfoDto(request);
        TwoTuple<Integer, String> masterTuple = ConfigCenterUtil.getMasterByBloc(request.getBlocCode());
        dto.getMemberInfo().setMasterType(masterTuple.getFirst());
        dto.getMemberInfo().setMasterCode(masterTuple.getSecond());
        memberMedService.updateMember(dto);
        return Response.success(null);
    }

    /**
     * 生成会员卡号
     */
    @PostMapping("/generateCardNo")
    public Response<String> generateCardNo(@RequestBody @Valid GenerateCardNoRequest request) {
        TwoTuple<Integer, String> masterTuple = ConfigCenterUtil.getMasterByBloc(request.getBlocCode());
        String cardNo = memberCardMedService.generateCardNo(masterTuple.getFirst(), masterTuple.getSecond(), Long.parseLong(request.getCardId()));
        return Response.success(cardNo);
    }

    /**
     * 会员注销
     */
    @PostMapping("/cancel")
    @LogRecord(tenant = CommonConstant.PROJECT_CODE, bizCode = ModuleConstants.MEMBER_MANAGE_CODE, bizName = ModuleConstants.MEMBER_MANAGE_NAME,
            category = ActionConstants.MEMBER_CANCEL_CODE, categoryName = ActionConstants.MEMBER_CANCEL_NAME)
    @OperationLog(operateRecord = "#member_cancel(#request)")
    public Response<String> cancelMember(@RequestBody @Valid MemberBaseRequest request) {
        TwoTuple<Integer, String> masterTuple = ConfigCenterUtil.getMasterByBloc(request.getBlocCode());
        memberMedService.cancelMember(masterTuple.getFirst(), masterTuple.getSecond(), request.getMemberNo(), request.getOperator());
        return Response.success(null);
    }

    /**
     * 会员恢复
     *
     * @param request
     * @return
     */
    @PostMapping("/recover")
    @LogRecord(tenant = CommonConstant.PROJECT_CODE, bizCode = ModuleConstants.MEMBER_MANAGE_CODE, bizName = ModuleConstants.MEMBER_MANAGE_NAME,
            category = ActionConstants.MEMBER_RECOVER_CODE, categoryName = ActionConstants.MEMBER_RECOVER_NAME)
    @OperationLog(operateRecord = "#member_recover(#request)")
    public Response<String> recoverMember(@RequestBody @Valid MemberBaseRequest request) {
        TwoTuple<Integer, String> masterTuple = ConfigCenterUtil.getMasterByBloc(request.getBlocCode());
        memberMedService.recoverMember(masterTuple.getFirst(), masterTuple.getSecond(), request.getMemberNo(), request.getOperator());
        return Response.success(null);
    }

    /**
     * 修改密码
     */
    @PostMapping("/changePassword")
    @LogRecord(tenant = CommonConstant.PROJECT_CODE, bizCode = ModuleConstants.MEMBER_MANAGE_CODE, bizName = ModuleConstants.MEMBER_MANAGE_NAME,
            category = ActionConstants.MEMBER_CHANGE_PASSWORD_CODE, categoryName = ActionConstants.MEMBER_CHANGE_PASSWORD_NAME)
    @OperationLog(operateRecord = "#member_changePassword(#request)")
    public Response<String> changePassword(@RequestBody @Valid ChangePasswordRequest request) {
        memberMedService.changePassword(request.getMemberNo(), request.getNewPassword(), request.getConfirmPassword(), request.getOperator());
        return Response.success(null);
    }

    /**
     * 根据手机号查询会员（传入当前会员，判断时排除当前会员）
     */
    @PostMapping("/queryByMobile")
    public Response<MemberResponse> getByMobile(@RequestBody @Valid GetByMobileRequest request) {
        TwoTuple<Integer, String> masterTuple = ConfigCenterUtil.getMasterByBloc(request.getBlocCode());
        MemberInfoDto memberInfoDto = memberMedService.getByMobile(masterTuple.getFirst(), masterTuple.getSecond(), request.getMobile(), request.getMemberNo());
        return Response.success(memberConverter.convertMemberResponse(memberInfoDto));
    }

    /**
     * 根据手机号查询会员号
     *
     * @param request
     * @return
     */
    @PostMapping("/getMemberNo")
    public Response<String> getMemberNoByMobile(@RequestBody @Valid GetMemberNoRequest request){
        TwoTuple<Integer, String> masterTuple = ConfigCenterUtil.getMasterByBloc(request.getBlocCode());
        String memberNo = memberMedService.getMemberNoByMobile(masterTuple.getFirst(), masterTuple.getSecond(), request.getMobile());
        return Response.success(memberNo);
    }

}
