package com.ly.titc.pms.member.gateway.entity.response.asset;

import lombok.Data;

/**
 * @Author：rui
 * @name：MemberRecordConsumeRecordResponse
 * @Date：2024-12-9 17:24
 * @Filename：MemberRecordConsumeRecordResponse
 */
@Data
public class MemberPointConsumeRecordResponse {

    /**
     * 会员号
     */
    private String memberNo;

    /**
     * 记录号
     */
    private String recordNo;

    /**
     * 积分主体类型   1:集团 2:门店 3:酒馆
     */
    private Integer masterType;

    /**
     * 积分主体CODE ELONG 集团编码 门店编码
     */
    private String masterCode;

    /**
     * 积分主体名称
     */
    private String masterName;

    /**
     * 酒馆组code ELONG (冗余)
     */
    private String clubCode;

    /**
     * 集团code （冗余）
     */
    private String blocCode;

    /**
     * 酒店code （冗余）
     */
    private String hotelCode;

    /**
     * 来源门店名称
     */
    private String source;

    /**
     * 平台渠道 CMS ，PMS ，微订房小程序，微订房公众号，艺龙会小程序
     */
    private String platformChannel;

    /**
     * 交易类型 pay:支付 refund:退款 recharge：充值
     */
    private String tradeType;

    /**
     * 原记录号
     */
    private String originalRecordNo;

    /**
     * 积分类型 1、增加积分 2、使用积分
     */
    private Integer action;

    /**
     * 积分项目：日结加积分、日租房加积分、预定担保扣积分、积分兑换...
     */
    private String actionItem;

    /**
     * 积分项目名称
     */
    private String actionItemName;

    /**
     * 积分数
     */
    private Integer score;

    /**
     * 剩余积分(积分过期时使用剩余积分过期)
     */
    private Integer balanceScore;

    /**
     * 活动code
     */
    private String activityCode;

    /**
     * 过期时间
     */
    private String expireDate;

    /**
     * 使用场景
     */
    private String scene;

    /**
     * 业务类型：客房单-ROOM，会员-MEMBER，商品部-SHOP
     */
    private String businessType;

    /**
     * 业务订单编号
     */
    private String businessNo;

    /**
     * 业务信息
     */
    private String businessNote;

    /**
     * 备注（原因）
     */
    private String remark;

    /**
     *
     */
    private String reason;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 修改人
     */
    private String modifyUser;

    /**
     * 创建时间;dbRouter内建字段
     */
    private String createTime;

    /**
     * 更新时间;dbRouter内建字段
     */
    private String updateTime;

}
