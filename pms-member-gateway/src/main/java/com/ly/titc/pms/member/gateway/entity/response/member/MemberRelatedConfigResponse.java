package com.ly.titc.pms.member.gateway.entity.response.member;

import lombok.Data;

import java.util.List;

/**
 * @Author：rui
 * @name：MemberRegisterCheckConfigResponse
 * @Date：2024-11-25 11:45
 * @Filename：MemberRegisterCheckConfigResponse
 */
@Data
public class MemberRelatedConfigResponse {

    /**
     * ID
     */
    private Long id;

    /**
     * 主类型 1:集团;2:门店;3:艺龙
     */
    private Integer masterType;

    /**
     * 主类型编码 ELONG 集团编码 门店编码
     */
    private String masterCode;

    /**
     * 参数
     */
    private List<String> params;

    private Integer type;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 修改人
     */
    private String modifyUser;

    /**
     * 创建时间
     */
    private String gmtCreate;

    /**
     * 修改时间
     */
    private String gmtModified;
}
