package com.ly.titc.pms.member.gateway.entity.request.spm.activity.dto;

import com.ly.titc.pms.spm.dubbo.entity.dto.activity.gear.BaseGearDto;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 售卡
 *
 * <AUTHOR>
 * @date 2024/12/31 10:38
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MemberCardSaleGearsDto extends BaseGearDto {

    /**
     * 会员卡等级
     */
    private String memberCardLevel;

    /**
     * 会员卡等级名称
     */
    private String memberCardLevelName;

    /**
     * 金额
     */
    private BigDecimal amount;

    /**
     * 积分
     */
    private Integer point;

}
