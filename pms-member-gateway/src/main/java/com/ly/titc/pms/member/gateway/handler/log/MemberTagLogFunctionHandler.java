package com.ly.titc.pms.member.gateway.handler.log;

import com.alibaba.excel.util.StringUtils;
import com.alibaba.fastjson.JSONObject;
import com.ly.titc.pms.ecrm.dubbo.entity.response.member.MemberTagConfigInfoResp;
import com.ly.titc.pms.ecrm.dubbo.entity.response.member.MemberTagMarkRuleInfoResp;
import com.ly.titc.pms.member.com.annotation.LogRecordFunction;
import com.ly.titc.pms.member.com.enums.CalculateTypeEnum;
import com.ly.titc.pms.member.com.enums.MemberTagEnum;
import com.ly.titc.pms.member.com.enums.ModuleEnum;
import com.ly.titc.pms.member.gateway.entity.request.member.card.DeleteTagRequest;
import com.ly.titc.pms.member.gateway.entity.request.member.tag.MemberTagMarkRuleInfoRequest;
import com.ly.titc.pms.member.gateway.entity.request.member.tag.SaveMemberTagConfigRequest;
import com.ly.titc.pms.member.mediator.rpc.dubbo.ecrm.MemberCardInfoDecorator;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Author：rui
 * @name：MemberTagLogFunctionHandler
 * @Date：2024-11-15 14:36
 * @Filename：MemberTagLogFunctionHandler
 */
@LogRecordFunction("memberTag")
public class MemberTagLogFunctionHandler extends AbstractModuleServiceHandler implements ApplicationContextAware {

    public static MemberCardInfoDecorator decorator;

    @Override
    public List<String> execute(String blocCode, String hotelCode, String name, String trackingId) {
        return null;
    }

    @Override
    public Integer getModuleId() {
       return ModuleEnum.TAG_MANAGE.getCode();
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        decorator = applicationContext.getBean(MemberCardInfoDecorator.class);
    }

    @LogRecordFunction("addMemberTag")
    public static String addMemberTag(SaveMemberTagConfigRequest request) {
        String name = request.getName();
        JSONObject json = new JSONObject();
        json.put("name", name);
        json.put("blocCode", request.getBlocCode());
        List<String> records = new ArrayList<>();
        records.add(String.format("新增【%s】", name));
        json.put("records", records);
        return json.toJSONString();
    }

    @LogRecordFunction("updateMemberTag")
    public static String updateMemberTag(SaveMemberTagConfigRequest request) {
        String name = request.getName();
        JSONObject json = new JSONObject();
        json.put("name", name);
        json.put("blocCode", request.getBlocCode());
        List<String> records = new ArrayList<>();
        records.add(String.format("修改【%s】", name));
        // 查询详情
        MemberTagConfigInfoResp  memberTagConfigInfoResp = decorator.getTagConfig(request.getId());
        if (StringUtils.equals(memberTagConfigInfoResp.getName(), request.getName())) {
            records.add(String.format("【标签名称】%s->%s", memberTagConfigInfoResp.getName(), request.getName()));
        }
        if (StringUtils.equals(memberTagConfigInfoResp.getRemark(), request.getRemark())) {
            records.add(String.format("【标签描述】%s->%s", memberTagConfigInfoResp.getRemark(), request.getRemark()));
        }
        if (!Objects.equals(memberTagConfigInfoResp.getType(), request.getType())) {
            records.add(String.format("【标签类型】%s->%s", memberTagConfigInfoResp.getType(), request.getType()));
        }
        if (!Objects.equals(memberTagConfigInfoResp.getMarkType(), request.getMarkType())) {
            records.add(String.format("【打标类型】%s->%s", memberTagConfigInfoResp.getMarkType(), request.getMarkType()));
        }
        if (!Objects.equals(memberTagConfigInfoResp.getState(), request.getState())) {
            records.add(String.format("【标签状态】%s->%s", memberTagConfigInfoResp.getState(), request.getState()));
        }
        List<MemberTagMarkRuleInfoResp> original = memberTagConfigInfoResp.getMarkRuleList();
        Map<Long, MemberTagMarkRuleInfoResp> map = original.stream().collect(Collectors.toMap(MemberTagMarkRuleInfoResp::getId, item -> item));
        List<MemberTagMarkRuleInfoRequest> current = request.getMarkRuleList();
        List<MemberTagMarkRuleInfoResp> delete = original.stream().filter(o -> !current.stream().filter(item -> Objects.nonNull(item.getId())).map(item -> item.getId()).collect(Collectors.toList()).contains(o.getId())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(delete)) {
            List<String> s = new ArrayList<>();
            for (MemberTagMarkRuleInfoResp memberTagMarkRuleInfoResp : delete) {
                s.add(String.format("%s%s%s", MemberTagEnum.getDescByType(memberTagMarkRuleInfoResp.getConditionType()), CalculateTypeEnum.getNameByType(memberTagMarkRuleInfoResp.getCalculateType()), memberTagMarkRuleInfoResp.getConditionValue()));
            }
            records.add(String.format("【删除统计规则】%s", String.join("、", s)));
        }
        List<MemberTagMarkRuleInfoRequest> add = current.stream().filter(item -> Objects.isNull(item.getId())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(add)) {
            List<String> s = new ArrayList<>();
            for (MemberTagMarkRuleInfoRequest memberTagMarkRuleInfoRequest : add) {
                s.add(String.format("%s%s%s", MemberTagEnum.getDescByType(memberTagMarkRuleInfoRequest.getConditionType()), CalculateTypeEnum.getNameByType(memberTagMarkRuleInfoRequest.getCalculateType()), memberTagMarkRuleInfoRequest.getConditionValue()));
            }
            records.add(String.format("【新增统计规则】%s", String.join(",", s)));
        }
        List<MemberTagMarkRuleInfoRequest> update = current.stream().filter(item -> Objects.nonNull(item.getId())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(update)) {
            List<String> s = new ArrayList<>();
            for (MemberTagMarkRuleInfoRequest memberTagMarkRuleInfoRequest : update) {
                if (!memberTagMarkRuleInfoRequest.getConditionType().equals(map.get(memberTagMarkRuleInfoRequest.getId()).getConditionType())
                        || !memberTagMarkRuleInfoRequest.getCalculateType().equals(map.get(memberTagMarkRuleInfoRequest.getId()).getCalculateType())
                        || !memberTagMarkRuleInfoRequest.getConditionValue().equals(map.get(memberTagMarkRuleInfoRequest.getId()).getConditionValue())) {
                    s.add(String.format("%s%s%s->%s%s%s", MemberTagEnum.getDescByType(memberTagMarkRuleInfoRequest.getConditionType()), CalculateTypeEnum.getNameByType(memberTagMarkRuleInfoRequest.getCalculateType()), memberTagMarkRuleInfoRequest.getConditionValue(),
                            MemberTagEnum.getDescByType(map.get(memberTagMarkRuleInfoRequest.getId()).getConditionType()), CalculateTypeEnum.getNameByType(map.get(memberTagMarkRuleInfoRequest.getId()).getCalculateType()), map.get(memberTagMarkRuleInfoRequest.getId()).getConditionValue()));
                }
            }
            if (CollectionUtils.isNotEmpty(s)) {
                records.add(String.format("【修改统计规则】%s", String.join(",", s)));
            }
        }
        json.put("records", records);
        return json.toJSONString();
    }

    @LogRecordFunction("deleteMemberTag")
    public static String deleteMemberTag(DeleteTagRequest request) {
        String name = request.getName();
        JSONObject json = new JSONObject();
        json.put("name", name);
        json.put("blocCode", request.getBlocCode());
        List<String> records = new ArrayList<>();
        records.add(String.format("删除【%s】", name));
        json.put("records", records);
        return json.toJSONString();
    }
}
