package com.ly.titc.pms.member.gateway.entity.request.ratePlan;

import com.ly.titc.pms.member.gateway.entity.request.BaseRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * @Description:
 * @Author: huaizhu.zhong
 * @Date: 2024-12-13
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ListRatePlanRequest extends BaseRequest {

    /**
     * 价格方案名称或编码
     */
    private String lkNameOrCode;

    /**
     * 酒店名称或编码
     */
    private String hotelFuzzyNameOrCode;

    /**
     * 状态
     */
    private Integer state;

    /**
     * 酒店编码
     */
    private List<String> hotelCodes;

}
