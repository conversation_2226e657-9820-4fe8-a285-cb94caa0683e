package com.ly.titc.pms.member.gateway.entity.request.spm.coupon;

import com.ly.titc.pms.member.gateway.entity.request.BaseRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024-11-11 19:32
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class GetGrantReceiverRequest extends BaseRequest {

    /**
     * 会员编号
     */
    private String memberNo;

    /**
     * 客户编号
     */
    private String customerNo;

    /**
     * 查询客户编码指定的酒店编码
     */
    private String hotelCode;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 身份证号
     */
    private String idCard;
}

