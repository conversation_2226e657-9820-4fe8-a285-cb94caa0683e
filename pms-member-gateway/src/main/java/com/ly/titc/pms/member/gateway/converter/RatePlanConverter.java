package com.ly.titc.pms.member.gateway.converter;

import com.ly.titc.cdm.dubbo.entity.response.plan.HotelRatePlanListResp;
import com.ly.titc.chm.entity.response.BlocRatePlanInfoResp;
import com.ly.titc.pms.member.gateway.entity.response.rateplan.BlocRatePlanListResponse;
import com.ly.titc.pms.member.gateway.entity.response.rateplan.RatePlanListResponse;
import org.mapstruct.Mapper;
import org.springframework.util.CollectionUtils;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description:
 * @Author: huaizhu.zhong
 * @Date: 2024-12-13
 */
@Mapper(componentModel = "spring")
public interface RatePlanConverter {
    RatePlanListResponse convertRatePlanListResponse(HotelRatePlanListResp hotelRatePlanListResp);
    default List<RatePlanListResponse> convertRatePlanListResponses(List<HotelRatePlanListResp> hotelRatePlanList,
                                                                    Map<String,String> hotelCodeAndNameMap){
        if(CollectionUtils.isEmpty(hotelRatePlanList)){
            return Collections.emptyList();
        }
        return hotelRatePlanList.stream().map(hotelRatePlanListResp->{
            RatePlanListResponse ratePlanListResponse = convertRatePlanListResponse(hotelRatePlanListResp);
            ratePlanListResponse.setHotelName(hotelCodeAndNameMap.get(hotelRatePlanListResp.getHotelCode()));
            return ratePlanListResponse;
        }).collect(Collectors.toList());
    }

    BlocRatePlanListResponse convertBlocRatePlanListResponse(BlocRatePlanInfoResp blocRatePlanInfoResp);
    List<BlocRatePlanListResponse> convertBlocRatePlanListResponses(List<BlocRatePlanInfoResp> blocRatePlanInfoRespList);
}
