package com.ly.titc.pms.member.gateway.controller.member;

import com.ly.titc.common.entity.Pageable;
import com.ly.titc.common.entity.Response;
import com.ly.titc.mdm.entity.response.hotel.HotelBaseInfoResp;
import com.ly.titc.pms.member.asset.dubbo.entity.request.points.PageMemberPointsFlowMemberReq;
import com.ly.titc.pms.member.asset.dubbo.entity.request.stored.PageMemberStoreConsumeMemberReq;
import com.ly.titc.pms.member.asset.dubbo.entity.response.MemberPointsFlowInfoResp;
import com.ly.titc.pms.member.asset.dubbo.entity.response.store.MemberStoreConsumeRecordResp;
import com.ly.titc.pms.member.gateway.converter.AssetConverter;
import com.ly.titc.pms.member.gateway.entity.request.asset.PageMemberPointConsumeRecordRequest;
import com.ly.titc.pms.member.gateway.entity.request.asset.PageMemberStoreConsumeRequest;
import com.ly.titc.pms.member.gateway.entity.request.asset.RechargeRecordDetailRequest;
import com.ly.titc.pms.member.gateway.entity.request.member.MemberBaseRequest;
import com.ly.titc.pms.member.gateway.entity.response.asset.*;
import com.ly.titc.pms.member.mediator.entity.dto.order.MemberRechargeOrderDetailDto;
import com.ly.titc.pms.member.mediator.entity.dto.order.MemberRechargeOrderDto;
import com.ly.titc.pms.member.mediator.entity.dto.order.PageOrderQueryDto;
import com.ly.titc.pms.member.mediator.rpc.dsf.mdm.HotelDecorator;
import com.ly.titc.pms.member.mediator.rpc.dubbo.asset.AssetDecorator;
import com.ly.titc.pms.member.mediator.rpc.dubbo.coupon.CouponDecorator;
import com.ly.titc.pms.member.mediator.service.MemberAssetMedService;
import com.ly.titc.pms.spm.dubbo.entity.request.coupon.StatisticsCouponReq;
import com.ly.titc.pms.spm.dubbo.entity.response.coupon.CouponStatisticsResp;
import com.ly.titc.pms.spm.dubbo.enums.MasterTypeEnum;
import com.ly.titc.pms.spm.dubbo.enums.ReceiverTypeEnum;
import com.ly.watcher.common.utils.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 会员资产
 *
 * @Author：rui
 * @name：MemberAssetController
 * @Date：2024-12-5 10:59
 * @Filename：MemberAssetController
 */
@Slf4j
@RestController
@RequestMapping("/member/asset")
public class MemberAssetController {

    @Resource
    private AssetDecorator assetDecorator;

    @Resource
    private CouponDecorator couponDecorator;

    @Resource
    private AssetConverter assetConverter;

    @Resource
    private MemberAssetMedService memberAssetMedService;

    @Resource
    private HotelDecorator hotelDecorator;

    /**
     * 分页查询会员消费、冻结记录
     *
     * @param request
     * @return
     */
    @PostMapping("/pageConsumeRecord")
    public Response<Pageable<MemberStoreConsumeRecordResponse>> pageConsumeRecord(@RequestBody @Valid PageMemberStoreConsumeRequest request) {
        PageMemberStoreConsumeMemberReq req =assetConverter.convert(request);
        Pageable<MemberStoreConsumeRecordResp> pageable = assetDecorator.pageConsumeRecord(req);
        return Response.success(Pageable.convert(pageable, pageable.getDatas()
                .stream().map(assetConverter::convertMemberStoreConsumeRecord).collect(Collectors.toList())));
    }

    /**
     * 分页查询会员充值记录
     */
    @PostMapping("/pageRechargeRecord")
    public Response<Pageable<MemberOrderRechargeResponse>> pageRechargeRecord(@RequestBody @Valid PageMemberStoreConsumeRequest request) {
        PageOrderQueryDto dto = assetConverter.convertDto(request);
        Pageable<MemberRechargeOrderDto> pageable = memberAssetMedService.pageMemberStoredRechargeRecord(dto);
        return Response.success(Pageable.convert(pageable, pageable.getDatas().stream().map(assetConverter::convertMemberOrderRecharge).collect(Collectors.toList())));
    }

    /**
     * 充值记录详情
     */
    @PostMapping("/rechargeRecordDetail")
    public Response<MemberOrderRechargeDetailResponse> rechargeRecordDetail(@RequestBody @Valid RechargeRecordDetailRequest request) {
        MemberRechargeOrderDetailDto dto = memberAssetMedService.getMemberStoredRechargeRecord(request.getMemberOderNo());
        return Response.success(assetConverter.convert(dto));
    }

    /**
     * 积分记录查询
     */
    @PostMapping("/pagePointRecord")
    public Response<Pageable<MemberPointConsumeRecordResponse>> pagePointRecord(@RequestBody @Valid PageMemberPointConsumeRecordRequest request) {
        // 这里的主体，是查询中的下拉框选择传入
        PageMemberPointsFlowMemberReq req = assetConverter.convert(request);
        Pageable<MemberPointsFlowInfoResp> pageable = assetDecorator.pagePointRecord(req);
        return Response.success(Pageable.convert(pageable, assetConverter.convertMemberPointConsumeRecord(pageable.getDatas())));
    }

    /**
     * 会员积分统计列表头查询 (积分调整头查询)
     */
    @PostMapping("getMemberTotalAccountPoints")
    public Response<MemberTotalPointResponse> getMemberTotalAccountPoints(@RequestBody @Valid MemberBaseRequest request) {
        return Response.success(assetConverter.convertMemberTotalPoint(assetDecorator.getTotalAccountPoints(request.getMemberNo())));
    }

    /**
     * 会员储值统计列表头查询
     */
    @PostMapping("getMemberTotalAccountAmount")
    public Response<MemberTotalAmountResponse> getMemberTotalAccountAmount(@RequestBody @Valid MemberBaseRequest request) {
        return Response.success(assetConverter.convertMemberTotalAmount(assetDecorator.getTotalAccountAmount(request.getMemberNo())));
    }

    /**
     * 查询会员资产
     *
     * @param request
     * @return
     */
    @PostMapping("getMemberAsset")
    public Response<MemberAssetResponse> getMemberAsset(@RequestBody @Valid MemberBaseRequest request){
        MemberAssetResponse memberAsset = new MemberAssetResponse();
        memberAsset.setPoint(assetConverter.convertMemberTotalPoint(assetDecorator.getTotalAccountPoints(request.getMemberNo())));
        memberAsset.setStore(assetConverter.convertMemberTotalAmount(assetDecorator.getTotalAccountAmount(request.getMemberNo())));

        StatisticsCouponReq apiReq = new StatisticsCouponReq();
        apiReq.setReceiverType(ReceiverTypeEnum.MEMBER.getCode());
        apiReq.setReceiverCode(request.getMemberNo());
        apiReq.setBlocCode(request.getBlocCode());
        apiReq.setTrackingId(request.getTrackingId());
        CouponStatisticsResp couponStatistics = couponDecorator.statisticsCoupon(apiReq);

        memberAsset.setCouponNum(couponStatistics == null ? 0 : couponStatistics.getUnUsedCouponNum());
        return Response.success(memberAsset);
    }

}
