package com.ly.titc.pms.member.gateway.entity.request.spm.coupon.template;

import com.ly.titc.pms.member.gateway.entity.request.BaseRequest;
import com.ly.titc.pms.member.mediator.entity.dto.spm.acitvity.ApplicableHotelsDto;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Description:
 * @Author: huaizhu.zhong
 * @Date: 2024-11-28
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class GetApplicableHotelRequest extends BaseRequest {

    /**
     * 模版code
     */
    private String templateCode;

    /**
     * 适用酒店范围
     */
    private ApplicableHotelsDto applicableHotelsDto;
}
