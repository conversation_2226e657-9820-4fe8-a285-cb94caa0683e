package com.ly.titc.pms.member.gateway.converter;

import com.ly.titc.pms.member.com.converter.BaseConverter;
import com.ly.titc.pms.member.gateway.entity.request.member.profile.*;
import com.ly.titc.pms.member.gateway.entity.response.member.profile.*;
import com.ly.titc.pms.member.mediator.entity.dto.profile.*;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.util.List;

/**
 * @Author：rui
 * @name：MemberProfileConverter
 * @Date：2024-11-18 14:14
 * @Filename：MemberProfileConverter
 */
@Mapper(componentModel = "spring")
public interface MemberProfileConverter extends BaseConverter {

    List<MemberCommonInvoiceHeaderResponse> convertMemberCommonInvoiceHeaderResponse(List<MemberProfileInvoiceHeaderInfoDto> infoList);

    @Mapping(target = "createUser", source = "request.operator")
    SaveMemberProfileInvoiceHeaderDto convertSaveMemberProfileInvoiceHeaderInfo(SaveCommonInvoiceHeaderRequest request, Integer masterType, String masterCode);
    @Mapping(target = "modifyUser", source = "request.operator")
    SaveMemberProfileInvoiceHeaderDto convertUpdateMemberProfileInvoiceHeaderInfo(SaveCommonInvoiceHeaderRequest request, Integer masterType, String masterCode);

    List<MemberCommonAddressResponse> convertMemberCommonAddressResponse(List<MemberProfileAddressInfoDto> infoList);
    @Mapping(target = "createUser", source = "request.operator")
    SaveMemberProfileAddressDto convertSaveMemberProfileAddressInfo(SaveCommonAddressRequest request, Integer masterType, String masterCode);

    @Mapping(target = "modifyUser", source = "request.operator")
    SaveMemberProfileAddressDto convertUpdateMemberProfileAddressInfo(SaveCommonAddressRequest request, Integer masterType, String masterCode);

    @Mapping(target = "createUser", source = "request.operator")
    SaveMemberProfileOccupantsDto convertSaveMemberProfileOccupantsInfo(SaveCommonOccupantRequest request, Integer masterType, String masterCode);

    @Mapping(target = "modifyUser", source = "request.operator")
    SaveMemberProfileOccupantsDto convertUpdateMemberProfileOccupantsInfo(SaveCommonOccupantRequest request, Integer masterType, String masterCode);

//    default List<MemberProfileTagTypeResponse> convertMemberProfileTagResponse(List<MemberProfileTagInfoDto> infoList, List<MemberTagConfigInfoResp> memberTagConfigInfoRespList) {
//        Map<Integer, List<MemberProfileTagInfoDto>> map = infoList.stream().collect(Collectors.groupingBy(MemberProfileTagInfoDto::getTagType));
//        Map<Long, MemberTagConfigInfoResp> memberTagConfigInfoRespMap = memberTagConfigInfoRespList.stream().collect(Collectors.toMap(MemberTagConfigInfoResp::getId,
//                memberTagConfigInfoResp -> memberTagConfigInfoResp));
//        return map.entrySet().stream().map(entry -> {
//            MemberProfileTagTypeResponse response = new MemberProfileTagTypeResponse();
//            response.setType(entry.getKey());
//            response.setName(MemberTagEnum.getDescByType(entry.getKey()));
//            response.setList(entry.getValue().stream().map(item -> convertMemberProfileTagResponseItem(item, memberTagConfigInfoRespMap.getOrDefault(item.getTagId(), new MemberTagConfigInfoResp())))
//                    .sorted(Comparator.comparing(MemberProfileTagResponse::getSort).reversed()).collect(Collectors.toList()));
//            return response;
//        }).collect(Collectors.toList());
//    }

//    default MemberProfileTagResponse convertMemberProfileTagResponseItem(MemberProfileTagInfoDto info, MemberTagConfigInfoResp resp) {
//        MemberProfileTagResponse response = convertMemberProfileTagResponseItem(info);
//        response.setTagName(resp.getName());
//        response.setSort(resp.getSort());
//        return response;
//    }
    MemberProfileTagResponse convertMemberProfileTagResponseItem(MemberProfileTagInfoDto info);

    List<MemberCommonOccupantResponse> convertMemberCommonOccupantResponse(List<MemberProfileOccupantsInfoDto> infoList);

    MemberProfileTagGroupResponse convertMemberProfileTagTypeResponse(MemberProfileTagInfoGroupDto e);

    @Mappings({
            @Mapping(target = "tags", source = "request.memberProfileTagList")
    })
    BatchAddSingleMemberTagDto convertBatchAddSingleMemberTagDto(SaveMemberProfileTagRequest request, Integer masterType, String masterCode, String platformChannel);

    TagBaseDto converTagBaseDto(MemberProfileTagRequest request);
}
