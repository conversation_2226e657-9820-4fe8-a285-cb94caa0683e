package com.ly.titc.pms.member.gateway.entity.request.spm.activity.dto;

import com.ly.titc.pms.spm.dubbo.entity.dto.activity.gear.BaseGearDto;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 升级活动参数
 *
 * <AUTHOR>
 * @date 2024/12/31 10:38
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MemberUpgradeGearsDto extends BaseGearDto {

    /**
     * 原会员等级
     */
    private String originMemberCardLevel;

    /**
     * 原等级名称
     */
    private String originMemberCardLevelName;
    /**
     * 升级会员等级
     */
    private String targetMemberCardLevel;

    /**
     * 升级等级名称
     */
    private String targetMemberCardLevelName;

    /**
     * 金额
     */
    private BigDecimal amount;

    /**
     * 积分
     */
    private Integer point;

}
