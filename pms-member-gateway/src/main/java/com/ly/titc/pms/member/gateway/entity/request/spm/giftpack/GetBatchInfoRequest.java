package com.ly.titc.pms.member.gateway.entity.request.spm.giftpack;

import com.ly.titc.pms.member.gateway.entity.request.BaseRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024-11-28 19:53
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class GetBatchInfoRequest extends BaseRequest {
    /**
     * 批次号
     */
    @NotBlank(message = "礼包发放批次号不能为空")
    private String batchNo;
}
