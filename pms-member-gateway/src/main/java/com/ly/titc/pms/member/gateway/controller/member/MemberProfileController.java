package com.ly.titc.pms.member.gateway.controller.member;

import com.ly.titc.cc.sdk.log.annotation.LogRecord;
import com.ly.titc.common.entity.Response;
import com.ly.titc.common.enums.PlatformChannelEnum;
import com.ly.titc.pms.member.com.annotation.OperationLog;
import com.ly.titc.pms.member.com.constant.ActionConstants;
import com.ly.titc.pms.member.com.constant.CommonConstant;
import com.ly.titc.pms.member.com.constant.ModuleConstants;
import com.ly.titc.pms.member.com.lang.TwoTuple;
import com.ly.titc.pms.member.com.utils.ConfigCenterUtil;
import com.ly.titc.pms.member.gateway.converter.MemberProfileConverter;
import com.ly.titc.pms.member.gateway.entity.request.member.profile.*;
import com.ly.titc.pms.member.gateway.entity.response.member.profile.*;
import com.ly.titc.pms.member.mediator.entity.dto.profile.BatchAddSingleMemberTagDto;
import com.ly.titc.pms.member.mediator.entity.dto.profile.MemberProfileTagInfoGroupDto;
import com.ly.titc.pms.member.mediator.entity.dto.profile.AddMemberProfileTagDto;
import com.ly.titc.pms.member.mediator.entity.dto.profile.TagBaseDto;
import com.ly.titc.pms.member.mediator.rpc.dubbo.ecrm.MemberCardInfoDecorator;
import com.ly.titc.pms.member.mediator.service.MemberProfileMedService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 会员常用信息、客户画像
 * @Author：rui
 * @name：MemberController
 * @Date：2024-11-18 13:56
 * @Filename：MemberController
 */
@Slf4j
@RestController
@RequestMapping("/member/profile")
public class MemberProfileController {

    @Resource
    private MemberProfileMedService memberProfileMedService;

    @Resource
    private MemberProfileConverter memberProfileConverter;

    /**
     * 查询会员常用发票
     *
     * @param request request
     * @return
     */
    @PostMapping("/listCommonInvoiceHeader")
    public Response<List<MemberCommonInvoiceHeaderResponse>> listCommonInvoiceHeader(@RequestBody @Valid ListCommonInvoiceHeaderRequest request) {
        TwoTuple<Integer, String> masterTuple = ConfigCenterUtil.getMasterByBloc(request.getBlocCode());
        return Response.success(memberProfileConverter.convertMemberCommonInvoiceHeaderResponse(memberProfileMedService.listCommonInvoiceHeader(masterTuple.getFirst(), request.getBlocCode(), request.getMemberNo(), request.getHeaderName())));
    }

    /**
     * 新增常用发票
     *
     * @param request
     * @return
     */
    @PostMapping("/addCommonInvoiceHeader")
    @LogRecord(tenant = CommonConstant.PROJECT_CODE, bizCode = ModuleConstants.MEMBER_MANAGE_CODE, bizName = ModuleConstants.MEMBER_MANAGE_NAME,
            category = ActionConstants.MEMBER_COMMON_PROFILE_ADD_CODE, categoryName = ActionConstants.MEMBER_COMMON_PROFILE_ADD_NAME)
    @OperationLog(operateRecord = "#member_addCommonInvoiceHeader(#request)")
    public Response addCommonInvoiceHeader(@RequestBody @Valid SaveCommonInvoiceHeaderRequest request) {
        TwoTuple<Integer, String> masterTuple = ConfigCenterUtil.getMasterByBloc(request.getBlocCode());
        memberProfileMedService.saveCommonInvoiceHeader(memberProfileConverter.convertSaveMemberProfileInvoiceHeaderInfo(request, masterTuple.getFirst(), request.getBlocCode()));
        return Response.success();
    }

    /**
     * 修改常用发票
     *
     * @param request
     * @return
     */
    @PostMapping("/updateCommonInvoiceHeader")
    @LogRecord(tenant = CommonConstant.PROJECT_CODE, bizCode = ModuleConstants.MEMBER_MANAGE_CODE, bizName = ModuleConstants.MEMBER_MANAGE_NAME,
            category = ActionConstants.MEMBER_COMMON_PROFILE_UPDATE_CODE, categoryName = ActionConstants.MEMBER_COMMON_PROFILE_UPDATE_NAME)
    @OperationLog(operateRecord = "#member_updateCommonInvoiceHeader(#request)")
    public Response updateCommonInvoiceHeader(@RequestBody @Valid SaveCommonInvoiceHeaderRequest request) {
        TwoTuple<Integer, String> masterTuple = ConfigCenterUtil.getMasterByBloc(request.getBlocCode());
        memberProfileMedService.saveCommonInvoiceHeader(memberProfileConverter.convertUpdateMemberProfileInvoiceHeaderInfo(request, masterTuple.getFirst(), request.getBlocCode()));
        return Response.success();
    }

    /**
     * 删除常用发票
     *
     * @param request
     * @return
     */
    @PostMapping("/deleteCommonInvoiceHeader")
    @LogRecord(tenant = CommonConstant.PROJECT_CODE, bizCode = ModuleConstants.MEMBER_MANAGE_CODE, bizName = ModuleConstants.MEMBER_MANAGE_NAME,
            category = ActionConstants.MEMBER_COMMON_PROFILE_DELETE_CODE, categoryName = ActionConstants.MEMBER_COMMON_PROFILE_DELETE_NAME)
    @OperationLog(operateRecord = "#member_deleteCommonInvoiceHeader(#request)")
    public Response deleteCommonInvoiceHeader(@RequestBody @Valid DeleteCommonInvoiceHeaderRequest request) {
        TwoTuple<Integer, String> masterTuple = ConfigCenterUtil.getMasterByBloc(request.getBlocCode());
        memberProfileMedService.deleteCommonInvoiceHeader(masterTuple.getFirst(), request.getBlocCode(), request.getMemberNo(), Long.parseLong(request.getInvoiceHeaderNo()), request.getOperator());
        return Response.success();
    }

    /**
     * 查询常用地址
     *
     * @param request
     * @return
     */
    @PostMapping("/listCommonAddress")
    public Response<List<MemberCommonAddressResponse>> listCommonAddress(@RequestBody @Valid ListCommonAddressRequest request) {
        TwoTuple<Integer, String> masterTuple = ConfigCenterUtil.getMasterByBloc(request.getBlocCode());
        return Response.success(memberProfileConverter.convertMemberCommonAddressResponse(memberProfileMedService.listCommonAddress(masterTuple.getFirst(), request.getBlocCode(), request.getMemberNo(), request.getName())));
    }

    /**
     * 新增常用地址
     *
     * @param request
     * @return
     */
    @PostMapping("/addCommonAddress")
    @LogRecord(tenant = CommonConstant.PROJECT_CODE, bizCode = ModuleConstants.MEMBER_MANAGE_CODE, bizName = ModuleConstants.MEMBER_MANAGE_NAME,
            category = ActionConstants.MEMBER_COMMON_PROFILE_ADD_CODE, categoryName = ActionConstants.MEMBER_COMMON_PROFILE_ADD_NAME)
    @OperationLog(operateRecord = "#member_addCommonAddress(#request)")
    public Response addCommonAddress(@RequestBody @Valid SaveCommonAddressRequest request) {
        TwoTuple<Integer, String> masterTuple = ConfigCenterUtil.getMasterByBloc(request.getBlocCode());
        memberProfileMedService.saveCommonAddress(memberProfileConverter.convertSaveMemberProfileAddressInfo(request, masterTuple.getFirst(), request.getBlocCode()));
        return Response.success();
    }

    /**
     * 编辑常用地址
     *
     * @param request request
     * @return
     */
    @PostMapping("/updateCommonAddress")
    @LogRecord(tenant = CommonConstant.PROJECT_CODE, bizCode = ModuleConstants.MEMBER_MANAGE_CODE, bizName = ModuleConstants.MEMBER_MANAGE_NAME,
            category = ActionConstants.MEMBER_COMMON_PROFILE_UPDATE_CODE, categoryName = ActionConstants.MEMBER_COMMON_PROFILE_UPDATE_NAME)
    @OperationLog(operateRecord = "#member_updateCommonAddress(#request)")
    public Response updateCommonAddress(@RequestBody @Valid SaveCommonAddressRequest request) {
        TwoTuple<Integer, String> masterTuple = ConfigCenterUtil.getMasterByBloc(request.getBlocCode());
        memberProfileMedService.saveCommonAddress(memberProfileConverter.convertUpdateMemberProfileAddressInfo(request, masterTuple.getFirst(), request.getBlocCode()));
        return Response.success();
    }

    /**
     * 删除常用地址
     *
     * @param request request
     * @return
     */
    @PostMapping("/deleteCommonAddress")
    @LogRecord(tenant = CommonConstant.PROJECT_CODE, bizCode = ModuleConstants.MEMBER_MANAGE_CODE, bizName = ModuleConstants.MEMBER_MANAGE_NAME,
            category = ActionConstants.MEMBER_COMMON_PROFILE_DELETE_CODE, categoryName = ActionConstants.MEMBER_COMMON_PROFILE_DELETE_NAME)
    @OperationLog(operateRecord = "#member_deleteCommonAddress(#request)")
    public Response deleteCommonAddress(@RequestBody @Valid DeleteCommonAddressRequest request) {
        TwoTuple<Integer, String> masterTuple = ConfigCenterUtil.getMasterByBloc(request.getBlocCode());
        memberProfileMedService.deleteCommonAddress(masterTuple.getFirst(), request.getBlocCode(), request.getMemberNo(), Long.parseLong(request.getAddressNo()), request.getOperator());
        return Response.success();
    }

    /**
     * 查询常用入住人
     *
     * @param request
     * @return
     */
    @PostMapping("/listCommonOccupant")
    public Response<List<MemberCommonOccupantResponse>> listCommonOccupant(@RequestBody @Valid ListCommonOccupantRequest request) {
        TwoTuple<Integer, String> masterTuple = ConfigCenterUtil.getMasterByBloc(request.getBlocCode());
        return Response.success(memberProfileConverter.convertMemberCommonOccupantResponse(memberProfileMedService.listCommonOccupants(masterTuple.getFirst(), request.getBlocCode(), request.getMemberNo(), request.getName())));
    }

    /**
     * 新增常用入住人
     *
     * @param request
     * @return
     */
    @PostMapping("/addCommonOccupant")
    @LogRecord(tenant = CommonConstant.PROJECT_CODE, bizCode = ModuleConstants.MEMBER_MANAGE_CODE, bizName = ModuleConstants.MEMBER_MANAGE_NAME,
            category = ActionConstants.MEMBER_COMMON_PROFILE_ADD_CODE, categoryName = ActionConstants.MEMBER_COMMON_PROFILE_ADD_NAME)
    @OperationLog(operateRecord = "#member_addCommonOccupant(#request)")
    public Response addCommonOccupant(@RequestBody @Valid SaveCommonOccupantRequest request) {
        TwoTuple<Integer, String> masterTuple = ConfigCenterUtil.getMasterByBloc(request.getBlocCode());
        memberProfileMedService.saveCommonOccupants(memberProfileConverter.convertSaveMemberProfileOccupantsInfo(request, masterTuple.getFirst(), request.getBlocCode()));
        return Response.success();
    }

    /**
     * 编辑常用入住人
     *
     * @param request request
     * @return
     */
    @PostMapping("/updateCommonOccupant")
    @LogRecord(tenant = CommonConstant.PROJECT_CODE, bizCode = ModuleConstants.MEMBER_MANAGE_CODE, bizName = ModuleConstants.MEMBER_MANAGE_NAME,
            category = ActionConstants.MEMBER_COMMON_PROFILE_UPDATE_CODE, categoryName = ActionConstants.MEMBER_COMMON_PROFILE_UPDATE_NAME)
    @OperationLog(operateRecord = "#member_updateCommonOccupant(#request)")
    public Response updateCommonOccupant(@RequestBody @Valid SaveCommonOccupantRequest request) {
        TwoTuple<Integer, String> masterTuple = ConfigCenterUtil.getMasterByBloc(request.getBlocCode());
        memberProfileMedService.saveCommonOccupants(memberProfileConverter.convertUpdateMemberProfileOccupantsInfo(request, masterTuple.getFirst(), request.getBlocCode()));
        return Response.success();
    }

    /**
     * 删除常用入住人
     *
     * @param request request
     * @return
     */
    @PostMapping("/deleteCommonOccupant")
    @LogRecord(tenant = CommonConstant.PROJECT_CODE, bizCode = ModuleConstants.MEMBER_MANAGE_CODE, bizName = ModuleConstants.MEMBER_MANAGE_NAME,
            category = ActionConstants.MEMBER_COMMON_PROFILE_DELETE_CODE, categoryName = ActionConstants.MEMBER_COMMON_PROFILE_DELETE_NAME)
    @OperationLog(operateRecord = "#member_deleteCommonOccupant(#request)")
    public Response deleteCommonOccupant(@RequestBody @Valid DeleteCommonOccupantRequest request) {
        TwoTuple<Integer, String> masterTuple = ConfigCenterUtil.getMasterByBloc(request.getBlocCode());
        memberProfileMedService.deleteCommonOccupants(masterTuple.getFirst(), request.getBlocCode(), request.getMemberNo(), Long.parseLong(request.getOccupantsNo()), request.getOperator());
        return Response.success();
    }

    /**
     * 会员画像-查询会员标签
     */
    @PostMapping("/listMemberProfileTag")
    public Response<List<MemberProfileTagGroupResponse>> listMemberProfileTag(@RequestBody @Valid ListMemberProfileTagRequest request) {
        TwoTuple<Integer, String> masterTuple = ConfigCenterUtil.getMasterByBloc(request.getBlocCode());
        List<MemberProfileTagInfoGroupDto> memberProfileTagInfos = memberProfileMedService.listMemberTagGroup(masterTuple.getFirst(), request.getBlocCode(), request.getMemberNo());
        if (CollectionUtils.isEmpty(memberProfileTagInfos)) {
            return Response.success(Collections.emptyList());
        }
        List<MemberProfileTagGroupResponse> list = memberProfileTagInfos.stream().map(e -> memberProfileConverter.convertMemberProfileTagTypeResponse(e)).collect(Collectors.toList());
        return Response.success(list);
    }

    /**
     * 会员画像，新增标签
     */
    @PostMapping("/addMemberProfileTag")
    @LogRecord(tenant = CommonConstant.PROJECT_CODE, bizCode = ModuleConstants.MEMBER_MANAGE_CODE, bizName = ModuleConstants.MEMBER_MANAGE_NAME,
            category = ActionConstants.MEMBER_TAG_ADD_CODE, categoryName = ActionConstants.MEMBER_TAG_ADD_NAME)
    @OperationLog(operateRecord = "#member_addMemberProfileTag(#request)")
    public Response addMemberProfileTag(@RequestBody @Valid SaveMemberProfileTagRequest request) {
        TwoTuple<Integer, String> masterTuple = ConfigCenterUtil.getMasterByBloc(request.getBlocCode());
        BatchAddSingleMemberTagDto batchAddSingleMemberTagDto = memberProfileConverter
                .convertBatchAddSingleMemberTagDto(request, masterTuple.getFirst(), masterTuple.getSecond(), PlatformChannelEnum.CRM.getPlatformChannel());
        memberProfileMedService.batchAddSingleMemberTag(batchAddSingleMemberTagDto);
        return Response.success();
    }

    /**
     * 会员画像，删除标签
     */
    @PostMapping("/deleteMemberProfileTag")
    @LogRecord(tenant = CommonConstant.PROJECT_CODE, bizCode = ModuleConstants.MEMBER_MANAGE_CODE, bizName = ModuleConstants.MEMBER_MANAGE_NAME,
            category = ActionConstants.MEMBER_TAG_DELETE_CODE, categoryName = ActionConstants.MEMBER_TAG_DELETE_NAME)
    @OperationLog(operateRecord = "#member_deleteMemberProfileTag(#request)")
    public Response deleteMemberProfileTag(@RequestBody @Valid DeleteMemberProfileTagRequest request) {
        TwoTuple<Integer, String> masterTuple = ConfigCenterUtil.getMasterByBloc(request.getBlocCode());
        memberProfileMedService.deleteMemberTag(masterTuple.getFirst(), masterTuple.getSecond(), request.getMemberNo(), Long.parseLong(request.getTagNo()), request.getOperator());
        return Response.success();
    }
}
