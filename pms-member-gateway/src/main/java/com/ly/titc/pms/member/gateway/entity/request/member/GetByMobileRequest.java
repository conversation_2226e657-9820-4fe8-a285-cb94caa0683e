package com.ly.titc.pms.member.gateway.entity.request.member;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

/**
 * 根据手机号查询会员
 *
 * @Author：rui
 * @name：QueryByMobileRequest
 * @Date：2024-12-12 19:58
 * @Filename：QueryByMobileRequest
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class GetByMobileRequest extends MemberBaseRequest {

    @NotBlank(message = "手机号不能为空")
    private String mobile;

}
