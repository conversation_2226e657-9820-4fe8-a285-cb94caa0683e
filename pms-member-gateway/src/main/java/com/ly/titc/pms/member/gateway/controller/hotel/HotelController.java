package com.ly.titc.pms.member.gateway.controller.hotel;

import com.google.common.collect.Lists;
import com.ly.titc.common.entity.Response;
import com.ly.titc.mdm.entity.request.hotel.SelectHotelsByFuzzyReq;
import com.ly.titc.mdm.entity.response.hotel.HotelBaseInfoResp;
import com.ly.titc.mdm.entity.response.hotel.PageHotelsResp;
import com.ly.titc.mdm.entity.response.hotel.SelectHotelResp;
import com.ly.titc.pms.member.gateway.converter.HotelConverter;
import com.ly.titc.pms.member.gateway.entity.request.brand.ListByBrandRequest;
import com.ly.titc.pms.member.gateway.entity.request.hotel.SelectHotelsByFuzzyRequest;
import com.ly.titc.pms.member.gateway.entity.request.hotel.SelectHotelsRequest;
import com.ly.titc.pms.member.gateway.entity.response.SelectResponse;
import com.ly.titc.pms.member.gateway.entity.response.hotel.BrandWithHotelsResponse;
import com.ly.titc.pms.member.gateway.entity.response.hotel.HotelDistributeResponse;
import com.ly.titc.pms.member.gateway.helper.UserPermissionHelper;
import com.ly.titc.pms.member.gateway.thread.local.UserInfoThreadHolder;
import com.ly.titc.pms.member.mediator.entity.dto.user.UserInfoDto;
import com.ly.titc.pms.member.mediator.rpc.dsf.mdm.HotelDecorator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 门店相关
 *
 * @Author：rui
 * @name：HotelController
 * @Date：2024-11-12 19:57
 * @Filename：HotelController
 */
@Slf4j
@RestController
@RequestMapping("/hotel")
public class HotelController {

    @Resource
    private HotelDecorator hotelDecorator;

    @Resource
    private HotelConverter hotelConverter;

    /**
     * 用户权限酒店下拉列表
     *
     * @param request request
     * @return 列表
     */
    @PostMapping("/list")
    public Response<List<SelectResponse>> listHotel(@RequestBody @Valid SelectHotelsRequest request) {
        List<String> hotelCodes = UserInfoThreadHolder.getUser().getHotelCodes();
        String blocCode = request.getBlocCode();
        String hotelName = request.getHotelName();
        List<HotelBaseInfoResp> hotelBaseInfoRespList = hotelDecorator.listHotelBaseInfos(blocCode, hotelName, hotelCodes);
        return Response.success(hotelConverter.convertHotelInfoResp2SelectResponse(hotelBaseInfoRespList));
    }

    @PostMapping("/search/list")
    public Response<List<SelectResponse>> listSearch(@RequestBody @Valid SelectHotelsByFuzzyRequest request) {
        SelectHotelsByFuzzyReq req = hotelConverter.convertSelectHotelsByFuzzyReq(request);
        List<PageHotelsResp> pageHotels = hotelDecorator.selectHotelsByFuzzy(req);
        if (CollectionUtils.isEmpty(pageHotels)) {
            return Response.success(Lists.newArrayList());
        }
        if (!CollectionUtils.isEmpty(request.getHotelCodes())) {
            pageHotels = pageHotels.stream().filter(h -> request.getHotelCodes().contains(String.valueOf(h.getHotelVid()))).collect(Collectors.toList());
        }
        return Response.success(hotelConverter.convertSelectResponseByPageHotels(pageHotels));
    }

    /**
     * 根据多个品牌code和门店名称模糊匹配门店信息
     *
     * @param request
     * @return
     */
    @PostMapping("/brandHotelTrees")
    public Response<List<BrandWithHotelsResponse<HotelDistributeResponse>>> brandHotelTrees(@RequestBody ListByBrandRequest request) {

        UserInfoDto user = UserInfoThreadHolder.getUser();
        String trackingId = UserInfoThreadHolder.getTrackingId();
        Set<String> hotelCodes = UserPermissionHelper.allAccessHotelCodes(user);
        List<SelectHotelResp> hotels = hotelDecorator.selectHotels(user.getBlocCode(), request.getBrands(),
                request.getHotelName(), new ArrayList<>(hotelCodes));
        //品牌code不能为空
        Map<String, List<SelectHotelResp>> hotelGroups = hotels.stream().filter(h -> !StringUtils.isEmpty(h.getBrandCode()))
                .collect(Collectors.groupingBy(SelectHotelResp::getBrandCode));
        List<BrandWithHotelsResponse<HotelDistributeResponse>> list = hotelGroups.entrySet().stream().map(e -> {
            String brandCode = e.getKey();
            List<SelectHotelResp> currBrandHotels = e.getValue();
            String brandName = currBrandHotels.get(0).getBrandName();

            List<HotelDistributeResponse> currHotels = currBrandHotels.stream().map(c -> {
                HotelDistributeResponse hotelResponse = new HotelDistributeResponse();
                hotelResponse.setBrandCode(c.getBrandCode());
                hotelResponse.setHotelCode(String.valueOf(c.getHotelVid()));
                hotelResponse.setHotelName(c.getHotelName());
                return hotelResponse;
            }).collect(Collectors.toList());

            BrandWithHotelsResponse<HotelDistributeResponse> brandResponse = new BrandWithHotelsResponse<>();
            brandResponse.setBrandCode(brandCode);
            brandResponse.setBrandName(brandName);
            brandResponse.setHotels(currHotels);
            return brandResponse;
        }).collect(Collectors.toList());
        return Response.success(list);
    }
}
