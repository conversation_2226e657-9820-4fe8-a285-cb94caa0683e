package com.ly.titc.pms.member.dubbo.provider.impl;

import com.ly.titc.common.entity.Response;
import com.ly.titc.common.exceptions.ServiceException;
import com.ly.titc.pms.member.com.enums.RespCodeEnum;
import com.ly.titc.pms.member.dubbo.entity.request.card.*;
import com.ly.titc.pms.member.dubbo.entity.response.MemberDetailInfoResp;
import com.ly.titc.pms.member.dubbo.interfaces.MemberCardDubboService;
import com.ly.titc.pms.member.dubbo.provider.converter.MemberCardConverter;
import com.ly.titc.pms.member.dubbo.provider.converter.MemberConverter;
import com.ly.titc.pms.member.mediator.entity.dto.member.IssueMemberCardDto;
import com.ly.titc.pms.member.mediator.entity.dto.member.MemberCardInfoDto;
import com.ly.titc.pms.member.mediator.entity.dto.member.MemberDetailDto;
import com.ly.titc.pms.member.mediator.entity.dto.member.UpdateCardLevelDto;
import com.ly.titc.pms.member.mediator.service.MemberCardMedService;
import com.ly.titc.pms.member.mediator.service.MemberMedService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.stream.Collectors;

/**
 * 会员卡Dubbo服务实现
 *
 * <AUTHOR>
 * @date 2024/10/31 11:04
 */
@Slf4j
@Validated
@DubboService
public class MemberCardDubboServiceImpl implements MemberCardDubboService {

    @Resource
    private MemberCardMedService memberCardMedService;

    @Resource
    private MemberMedService memberMedService;

    @Resource
    private MemberCardConverter memberCardConverter;

    @Resource(type = MemberConverter.class)
    private MemberConverter memberConverter;

    @Override
    public Response<String> getMemberNo(GetByCardNoReq request) {
        MemberCardInfoDto memberCardInfo = memberCardMedService.getByMemberCardNo(request.getMasterType(), request.getMasterCode(), request.getMemberCardNo());
        return Response.success(memberCardInfo == null ? null : memberCardInfo.getMemberNo());
    }

    @Override
    public Response<MemberDetailInfoResp> getDetailByCardNo(GetByCardNoReq request) {
        MemberCardInfoDto memberCardInfo = memberCardMedService.getByMemberCardNo(request.getMasterType(), request.getMasterCode(), request.getMemberCardNo());
        if (memberCardInfo == null) {
            return Response.success(null);
        }
        MemberDetailDto memberInfo = memberMedService.getDetailByMemberNo(memberCardInfo.getMasterType(), memberCardInfo.getMasterCode(), memberCardInfo.getMemberNo());
        memberInfo.setMemberCardInfos(memberInfo.getMemberCardInfos().stream().filter(e -> e.getMemberCardNo().equals(request.getMemberCardNo())).collect(Collectors.toList()));
        return Response.success(memberConverter.convertDtoToResp(memberInfo));
    }

    @Override
    public Response<String> issueCard(IssueCardReq request) {

        IssueMemberCardDto memberCardInfo = memberConverter.convertReqToDto(request.getMemberCardInfo(), request.getOperator());
        String memberCardNo = memberCardMedService.issueCard(memberCardInfo);
        return Response.success(memberCardNo);
    }

    @Override
    public Response<String> updateCardInfo(UpdateCardInfoReq request) {
        Integer masterType = request.getMasterType();
        String masterCode = request.getMasterCode();
        String memberNo = request.getMemberNo();
        String memberCardNo = request.getMemberCardNo();

        MemberCardInfoDto memberCardInfo = memberCardMedService.getByMemberCardNo(masterType, masterCode, memberCardNo);
        if (memberCardInfo == null || !memberCardInfo.getMemberNo().equals(memberNo)) {
            throw new ServiceException(RespCodeEnum.MEMBER_10013);
        }
        memberCardConverter.convertReqToPo(request, memberCardInfo);
        memberCardMedService.updateCardInfo(memberCardInfo);
        return Response.success(memberCardNo);
    }

    @Override
    public Response<String> updateCardLevel(UpdateCardLevelReq request) {

        UpdateCardLevelDto updateCardLevelDto = memberCardConverter.convertReqToDto(request);
        memberCardMedService.updateCardLevel(updateCardLevelDto);
        return Response.success(null);
    }

    @Override
    public Response<String> generateCardNo(GenerateCardNoReq request) {
        Integer masterType = request.getMasterType();
        String masterCode = request.getMasterCode();
        Long cardId = request.getCardId();

        String memberCardNo = memberCardMedService.generateCardNo(masterType, masterCode, cardId);
        return Response.success(memberCardNo);
    }

    @Override
    public Response<Boolean> checkCardNoExist(CheckCardNoReq request) {
        return Response.success(memberCardMedService.checkCardNoExist(request.getMasterType(), request.getMasterCode(), request.getMemberCardNo()));
    }

    @Override
    public Response<Boolean> checkHasCard(CheckHasCardReq request) {
        return Response.success(memberCardMedService.checkHasCard(request.getMasterType(), request.getMasterCode(), request.getCardId(), request.getCardLevel()));
    }
}
