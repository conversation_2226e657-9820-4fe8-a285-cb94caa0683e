package com.ly.titc.pms.member.dubbo.provider.converter;

import com.ly.titc.common.enums.PlatformChannelEnum;
import com.ly.titc.common.enums.ScopeSourceEnum;
import com.ly.titc.pms.ecrm.dubbo.entity.request.MemberAssetSysConfigGetReq;
import com.ly.titc.pms.ecrm.dubbo.entity.request.MemberAssetSysConfigReq;
import com.ly.titc.pms.ecrm.dubbo.entity.request.member.usageRule.QueryMemberScopeUsageForAssetReq;
import com.ly.titc.pms.ecrm.dubbo.entity.response.MemberAssetSysConfigResp;
import com.ly.titc.pms.ecrm.dubbo.entity.response.member.usageRule.MemberStoreUsageRuleAssetResp;
import com.ly.titc.pms.member.dubbo.entity.request.BlocBaseReq;
import com.ly.titc.pms.member.dubbo.entity.request.asset.SaveStoreConfigReq;
import com.ly.titc.pms.member.dubbo.entity.request.usage.DeleteStoreUsageReq;
import com.ly.titc.pms.member.dubbo.entity.request.usage.PageStoreUsageRuleReq;
import com.ly.titc.pms.member.dubbo.entity.request.usage.SaveStoreUsageConfigReq;
import com.ly.titc.pms.member.dubbo.entity.request.usage.UpdateStoreUsageStateReq;
import com.ly.titc.pms.member.dubbo.entity.response.MemberStoreAvailableUsageRuleResp;
import com.ly.titc.pms.member.dubbo.entity.response.MemberStoreConfigResp;
import com.ly.titc.pms.member.dubbo.entity.response.MemberStoreUsageRuleResp;
import com.ly.titc.pms.member.dubbo.entity.response.SaveStoreUsageRuleResultResp;
import com.ly.titc.pms.member.mediator.entity.dto.BaseDto;
import com.ly.titc.pms.member.mediator.entity.dto.usage.*;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

/**
 * <AUTHOR>
 * @date 2025/6/12 19:01
 */
@Mapper(componentModel = "spring")
public interface MemberStoreRuleConverter {

    @Mappings({
            @Mapping(target = "masterType", constant = "1"),
            @Mapping(target = "masterCode", source = "blocCode")
    })
    MemberAssetSysConfigReq convertRequestToDto(SaveStoreConfigReq request);

    MemberStoreConfigResp convertRespToResponse(MemberAssetSysConfigResp resp);

    @Mappings({
            @Mapping(target = "masterType", constant = "1"),
            @Mapping(target = "masterCode", source = "blocCode")
    })
    MemberAssetSysConfigGetReq convertRequestToDto(BlocBaseReq request);

    QueryMemberMasterUsageDto convertRequestToDto(PageStoreUsageRuleReq request);

    MemberStoreUsageRuleResp convertDtoToResponse(MemberStoreUsageRuleDto dto);

    SaveStoreUsageRuleResultResp convertDtoToResponse(MemberUsageRuleSaveResultDto dto);

    MemberStoreUsageRuleConfigSaveDto convertRequestToDto(SaveStoreUsageConfigReq req);

    UpdateMemberUsageStateDto convertRequestToDto(UpdateStoreUsageStateReq req);

    DeleteMemberUsageDto convertRequestToDto(DeleteStoreUsageReq req);

    BaseDto convertReqToDto(BlocBaseReq req);

    default QueryMemberScopeUsageForAssetReq convertReqToReq(BlocBaseReq request){
        QueryMemberScopeUsageForAssetReq req = new QueryMemberScopeUsageForAssetReq();
        req.setScopeSource(ScopeSourceEnum.BLOC.getCode());
        req.setScopeSourceCode(request.getBlocCode());
        req.setPlatformChannel(PlatformChannelEnum.CRM.getPlatformChannel());
        req.setTrackingId(request.getTrackingId());
        return req;
    }

    MemberStoreAvailableUsageRuleResp convertRespToResponse(MemberStoreUsageRuleAssetResp resp);
}
