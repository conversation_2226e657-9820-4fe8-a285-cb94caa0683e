package com.ly.titc.pms.member.dubbo.provider.impl;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.ly.titc.common.entity.Pageable;
import com.ly.titc.common.entity.Response;
import com.ly.titc.common.enums.PlatformChannelEnum;
import com.ly.titc.common.util.PageableUtil;
import com.ly.titc.pms.ecrm.dubbo.entity.request.MemberAssetSysConfigGetReq;
import com.ly.titc.pms.ecrm.dubbo.entity.request.MemberAssetSysConfigReq;
import com.ly.titc.pms.ecrm.dubbo.entity.request.member.usageRule.QueryMemberScopeUsageForAssetReq;
import com.ly.titc.pms.ecrm.dubbo.entity.response.MemberAssetSysConfigResp;
import com.ly.titc.pms.ecrm.dubbo.entity.response.member.usageRule.MemberStoreUsageRuleAssetResp;
import com.ly.titc.pms.member.dubbo.entity.request.BlocBaseReq;
import com.ly.titc.pms.member.dubbo.entity.request.asset.SaveStoreConfigReq;
import com.ly.titc.pms.member.dubbo.entity.request.usage.DeleteStoreUsageReq;
import com.ly.titc.pms.member.dubbo.entity.request.usage.PageStoreUsageRuleReq;
import com.ly.titc.pms.member.dubbo.entity.request.usage.SaveStoreUsageConfigReq;
import com.ly.titc.pms.member.dubbo.entity.request.usage.UpdateStoreUsageStateReq;
import com.ly.titc.pms.member.dubbo.entity.response.*;
import com.ly.titc.pms.member.dubbo.interfaces.MemberStoreRuleDubboService;
import com.ly.titc.pms.member.dubbo.provider.converter.MemberStoreRuleConverter;
import com.ly.titc.pms.member.mediator.entity.dto.BaseDto;
import com.ly.titc.pms.member.mediator.entity.dto.usage.*;
import com.ly.titc.pms.member.mediator.rpc.dubbo.ecrm.MemberAssetSysConfigDecorator;
import com.ly.titc.pms.member.mediator.rpc.dubbo.ecrm.MemberStoreUsageDecorator;
import com.ly.titc.pms.member.mediator.service.MemberStoreUsageMedService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/2/8 11:11
 */
@Slf4j
@Validated
@DubboService
public class MemberStoreRuleDubboServiceImpl implements MemberStoreRuleDubboService {

    @Resource(type = MemberAssetSysConfigDecorator.class)
    private MemberAssetSysConfigDecorator configDecorator;
    @Resource
    private MemberStoreRuleConverter memberStoreRuleConverter;
    @Resource
    private MemberStoreUsageMedService storeUsageMedService;
    @Resource
    private MemberStoreUsageDecorator storeUsageDecorator;

    @Override
    public Response<Boolean> saveConfig(SaveStoreConfigReq request) {
        MemberAssetSysConfigReq req = memberStoreRuleConverter.convertRequestToDto(request);
        configDecorator.save(req);
        return Response.success(true);
    }

    @Override
    public Response<MemberStoreConfigResp> getConfig(BlocBaseReq request) {
        MemberAssetSysConfigGetReq req = memberStoreRuleConverter.convertRequestToDto(request);
        MemberAssetSysConfigResp resp = configDecorator.get(req);
        return Response.success(memberStoreRuleConverter.convertRespToResponse(resp));
    }

    @Override
    public Response<Pageable<MemberStoreUsageRuleResp>> pageRule(PageStoreUsageRuleReq request) {
        QueryMemberMasterUsageDto dto = memberStoreRuleConverter.convertRequestToDto(request);
        Pageable<MemberStoreUsageRuleDto> page = storeUsageMedService.page(dto);
        return Response.success(PageableUtil.convert(page, memberStoreRuleConverter::convertDtoToResponse));
    }

    @Override
    public Response<List<SaveStoreUsageRuleResultResp>> saveRule(SaveStoreUsageConfigReq request) {
        MemberStoreUsageRuleConfigSaveDto dto = memberStoreRuleConverter.convertRequestToDto(request);
        List<MemberUsageRuleSaveResultDto> results = storeUsageMedService.save(dto);
        return Response.success(results.stream().map(memberStoreRuleConverter::convertDtoToResponse).collect(Collectors.toList()));
    }

    @Override
    public Response<Boolean> updateState(UpdateStoreUsageStateReq request) {
        UpdateMemberUsageStateDto dto = memberStoreRuleConverter.convertRequestToDto(request);
        storeUsageMedService.updateState(dto);
        return Response.success(true);
    }

    @Override
    public Response<Boolean> deleteRule(DeleteStoreUsageReq request) {
        DeleteMemberUsageDto dto = memberStoreRuleConverter.convertRequestToDto(request);
        Boolean result = storeUsageMedService.delete(dto);
        return Response.success(result);
    }

    @Override
    public Response<List<SaveStoreUsageRuleResultResp>> remind(BlocBaseReq request) {
        BaseDto dto = memberStoreRuleConverter.convertReqToDto(request);
        List<MemberUsageRuleSaveResultDto> results = storeUsageMedService.remind(dto);
        return Response.success(results.stream().map(memberStoreRuleConverter::convertDtoToResponse).collect(Collectors.toList()));
    }

    @Override
    public Response<List<MemberStoreAvailableUsageRuleResp>> listAvailableRule(BlocBaseReq request) {
        QueryMemberScopeUsageForAssetReq req = memberStoreRuleConverter.convertReqToReq(request);
        List<MemberStoreUsageRuleAssetResp> resps = storeUsageDecorator.listRuleForAsset(req);
        List<MemberStoreUsageRuleAssetResp> ruleAssetResps = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(resps)) {
            resps.forEach(resp -> {
                if (resp.getScopePlatformChannels() != null
                        && resp.getScopePlatformChannels().contains(PlatformChannelEnum.CRM.getPlatformChannel())) {
                    ruleAssetResps.add(resp);
                }
            });
        }
        return Response.success(ruleAssetResps.stream().map(memberStoreRuleConverter::convertRespToResponse).collect(Collectors.toList()));
    }
}
